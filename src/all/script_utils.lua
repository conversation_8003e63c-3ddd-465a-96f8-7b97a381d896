--
-- coroutine scripts
--

local log = (require 'klua.log'):new('script_utils')
local log_xp = ( log.xp or log:new('xp'))

require 'klua.table'
local km = require 'klua.macros'
local signal = require 'hump.signal'

local AC = require 'achievements'
local E = require 'entity_db'
local GR = require 'grid_db'
local GS = require 'game_settings'
local P = require 'path_db'
local S = require 'sound_db'
local U = require 'utils'
local LU = require 'level_utils'
local UP = require 'upgrades'
local V = require 'klua.vector'

local bit = require 'bit'
local band = bit.band
local bor = bit.bor
local bnot = bit.bnot

require 'i18n'

------------------------------------------------------------
-- aux functions

local function queue_insert(store,e) simulation:queue_insert_entity(e) end  -- global
local function queue_remove(store,e) simulation:queue_remove_entity(e) end
local function queue_damage(store,damage) table.insert(store.damage_queue, damage) end  -- sys.health
local function fts(v) return v / FPS end

------------------------------------------------------------
-- common functions

local function ui_click_proxy_add(proxy,dest)
    if not proxy.ui then
        log.error('cannot proxy. entity has no ui component: (%s)%s', proxy.id, proxy.template_name)
        return
    end
    proxy.ui.click_proxies = proxy.ui.click_proxies or {}
    table.insert(proxy.ui.click_proxies, dest)
end

local function ui_click_proxy_remove(proxy,dest)
    if proxy.ui and proxy.ui.click_proxies then 
        table.removeobject(proxy.ui.click_proxies, dest)
    end
end

local function remove_modifiers(store,entity,mod_name,exclude_name)
    local mods = table.filter(store.entities,
                              function(k,v)
                                  return ( v.modifier and
                                              v.modifier.target_id == entity.id and
                                              (not mod_name or mod_name == v.template_name) and
                                              (not exclude_name or exclude_name ~= v.template_name) )
                              end
    )
    for _,m in pairs(mods) do
        queue_remove(store,m)
    end
end

local function remove_modifiers_by_type(store,entity,mod_type,exclude_name)
    local mods = table.filter(store.entities,
                              function(k,v)
                                  return ( v.modifier and
                                              v.modifier.target_id == entity.id and
                                              v.modifier.mod_type == mod_type and
                                              (not exclude_name or exclude_name ~= v.template_name))
                              end
    )
    for _,m in pairs(mods) do
        queue_remove(store,m)
    end
end

local function remove_auras(store,entity)
    -- WARNING: requires aura.track_source to work! (Should I remove this requirement?)
    local auras = table.filter(store.entities,
                               function(k,v)
                                   return v.aura and v.aura.track_source and v.aura.source_id == entity.id
                               end
    )
    for _,a in pairs(auras) do
        queue_remove(store,a)
    end
end

--

local function hide_modifiers(store,entity,keep,exclude_mod)
    local mods = table.filter(
        store.entities,
        function(k,v)
            return v.modifier and v.modifier.target_id == entity.id and v ~= exclude_mod
        end
    )
    for _,m in pairs(mods) do
        U.sprites_hide(m,nil,nil,keep)
    end
end

local function show_modifiers(store,entity,restore,exclude_mod)
    local mods = table.filter(
        store.entities,
        function(k,v)
            return v.modifier and v.modifier.target_id == entity.id and v ~= exclude_mod
        end
    )
    for _,m in pairs(mods) do
        U.sprites_show(m,nil,nil,restore)        
    end
end

local function hide_auras(store,entity,keep)
    local auras = table.filter(store.entities,function(k,v) return v.aura and v.aura.track_source and v.aura.source_id == entity.id end)
    for _,a in pairs(auras) do
        U.sprites_hide(a,nil,nil,keep)
    end
end

local function show_auras(store,entity,restore)
    local auras = table.filter(store.entities,function(k,v) return v.aura and v.aura.track_source and v.aura.source_id == entity.id end)
    for _,a in pairs(auras) do
        U.sprites_show(a,nil,nil,restore)
    end
end

--

local function unit_dodges(store,this,ranged_attack,attack,source)
    if not this.dodge then
        return false
    end
    this.dodge.last_check_ts = store.tick_ts
    if ( not this.unit.is_stunned and
             ( not this.dodge.requires_magic or (this.enemy and this.enemy.can_do_magic)) and
             ( not ranged_attack or this.dodge.ranged ) and
             ( not this.dodge.cooldown or ( store.tick_ts - this.dodge.ts > this.dodge.cooldown ) ) and
             ( not attack or not attack.damage_type or band(attack.damage_type, DAMAGE_NO_DODGE) == 0 ) and
             ( math.random() <= this.dodge.chance ) and
             ( not this.dodge.can_dodge or this.dodge.can_dodge(store,this,ranged_attack,attack,source) ) )
    then
        this.dodge.last_doge_ts = store.tick_ts
        this.dodge.last_attack = attack
        this.dodge.active = true
        return true
    end
    return false
end

local function show_dodge_pop(store,this)
    local pop = E:create_entity(this.dodge.pop or 'pop_miss')
    pop.pos = V.v(this.pos.x, this.pos.y)
    if this.unit and this.unit.pop_offset then
        -- unit.pop_offset overrides
        pop.pos.y = pop.pos.y + this.unit.pop_offset.y
    end                
    pop.pos.y = pop.pos.y + pop.pop_y_offset
    pop.render.sprites[1].r = math.random(-21,21) * math.pi / 180
    pop.render.sprites[1].ts = store.tick_ts
    queue_insert(store,pop)  

    if this.dodge.silent and not this.dodge.animation then
        this.dodge.active = false
    end
end

local function stun_inc(this)
    if this and this.unit and not this.unit.ignore_stun then
        local u = this.unit
        u.stun_count = u.stun_count + 1
        if u.stun_count > 0 then
            u.is_stunned = true
        end
    end
end

local function stun_dec(this,remove_all)
    if this and this.unit and not this.unit.ignore_stun then
        local u = this.unit
        u.stun_count = remove_all and 0 or ( u.stun_count - 1 )
        if u.stun_count < 1 then
            u.is_stunned = nil
            u.stun_count = 0
        end
    end
end

local function armor_inc(this, value)
    if not this.health.raw_armor then this.health.raw_armor = this.health.armor end
    this.health.raw_armor = this.health.raw_armor + value
    this.health.armor = km.clamp(0,1,this.health.raw_armor)
end

local function armor_dec(this, value)
    armor_inc(this, (-1) * value)
end

local function magic_armor_inc(this,value)
    if not this.health.raw_magic_armor then this.health.raw_magic_armor = this.health.magic_armor end
    this.health.raw_magic_armor = this.health.raw_magic_armor + value
    this.health.magic_armor = km.clamp(0,1,this.health.raw_magic_armor)
end

local function magic_armor_dec(this,value)
    magic_armor_inc(this,(-1) * value)
end

local function spiked_armor_inc(this, value)
    if not this.health.raw_spiked_armor then this.health.raw_spiked_armor = this.health.spiked_armor end
    this.health.raw_spiked_armor = this.health.raw_spiked_armor + value
    this.health.spiked_armor = km.clamp(0,1,this.health.raw_spiked_armor)
end

local function spiked_armor_dec(this, value)
    spiked_armor_inc(this, (-1) * value)
end

local function tower_block_inc(this)
    if this and this.tower and not this.tower_holder then
        local t = this.tower
        t.block_count = t.block_count + 1
        if t.block_count > 0 then
            t.blocked = true
            if this.ui then this.ui.can_click = false end    
        end
    end
end

local function tower_block_dec(this,remove_all)
    if this and this.tower and not this.tower_holder then
        local t = this.tower
        t.block_count = remove_all and 0 or ( t.block_count - 1 )
        if t.block_count < 1 then
            t.blocked = nil
            t.block_count = 0
            if this.ui then this.ui.can_click = true end
        end
    end
end

local function tower_update_silenced_powers(store,this)
    for k,pow in pairs(this.powers) do
        local pa = this.attacks.list[pow.attack_idx]
        if pa then
            if not this.tower.can_do_magic and not pa.silence_ts then
                pa.silence_ts = store.tick_ts
            elseif this.tower.can_do_magic and pa.silence_ts then
                pa.ts = store.tick_ts - (pa.silence_ts - pa.ts)
                pa.silence_ts = nil
            end
        end
    end
end

local function do_death_spawns(store,this)
    if this.death_spawns.fx then
        local fx = E:create_entity(this.death_spawns.fx)
        fx.pos = V.vclone(this.pos)
        fx.render.sprites[1].ts = store.tick_ts
        if this.death_spawns.fx_flip_to_source and this.render and this.render.sprites[1] then
            fx.render.sprites[1].flip_x = this.render.sprites[1].flip_x
        end
        queue_insert(store,fx)        
    end    
    for i=1,this.death_spawns.quantity do
        local s = E:create_entity(this.death_spawns.name)
        s.pos = V.vclone(this.pos)
        if this.death_spawns.spawn_animation and s.render then
            s.render.sprites[1].name = this.death_spawns.spawn_animation
        end
        if s.render and s.render.sprites[1] and this.render and this.render.sprites[1] then 
            s.render.sprites[1].flip_x = this.render.sprites[1].flip_x
        end
        if s.nav_path then 
            s.nav_path.pi = this.nav_path.pi
            local spread_nodes = this.death_spawns.spread_nodes
            if spread_nodes > 0 then
                s.nav_path.spi = km.zmod(this.nav_path.spi + i, 3)
                local node_offset = spread_nodes * -2 * math.floor(i/3)
                s.nav_path.ni = this.nav_path.ni + node_offset + spread_nodes
            else
                s.nav_path.spi = this.nav_path.spi
                s.nav_path.ni = this.nav_path.ni + 2
            end
        end
        if this.death_spawns.offset then
            s.pos.x = s.pos.x + this.death_spawns.offset.x
            s.pos.y = s.pos.y + this.death_spawns.offset.y            
        end
        queue_insert(store,s)
    end
end

local function delay_attack(store,attack,time)
    -- if time is 1 tick, it is scheduled for the next tick
    attack.ts = store.tick_ts - attack.cooldown + time - 1e-6
end

-- create, insert and reset sprite ts
local function insert_sprite(store,name,pos,flip_x,ts_offset)
    local e = E:create_entity(name)
    e.pos.x,e.pos.y = pos.x,pos.y
    if e.render then
        for _,s in pairs(e.render.sprites) do
            s.ts = store.tick_ts + (ts_offset or 0)
            s.flip_x = flip_x
        end
    end
    queue_insert(store,e)
    return e
end

-- fade out entity
local function fade_out_entity(store, entity, delay, duration, delete_after)
    duration = duration or 2
    if entity.tween then
        log.error('entity %s already has tween. cannot be faded out', entity.id)
        return
    end
    entity.tween = E:clone_c('tween')
    entity.tween.ts = store.tick_ts
    if delete_after and entity.health then
        -- tween handles entity removal
        entity.health.ignore_delete_after = true
        entity.tween.remove = true
    end
    local p = E:clone_c('tween_prop')            
    p.keys = {{0,255},{delay,255}, {delay+duration,0}}
    for i,s in ipairs(entity.render.sprites) do
        local pp = table.deepclone(p)
        pp.sprite_id = i
        entity.tween.props[i] = pp
    end
end

-- bullet aux functions
local function create_pop(store,pos,pop)
    local name = pop[math.random(1,#pop)]
    local e = E:create_entity(name)
    e.pos = V.v(pos.x, pos.y + e.pop_y_offset)
    e.render.sprites[1].r = math.random(-21,21) * math.pi / 180
    e.render.sprites[1].ts = store.tick_ts
    return e    
end

local function create_bullet_pop(store,this)
    -- create pops directly (not through sys.pops)
    local b = this.bullet
    if b.pop and (not b.pop_chance or math.random() < b.pop_chance) then
        return create_pop(store,this.pos,b.pop)        
    end
end

local function create_bullet_damage(bullet, target_id, source_id)
    -- create damage form bullet component table (not bullet entity!)
    local d = E:create_entity('damage')
    d.damage_type = bullet.damage_type
    d.reduce_armor = bullet.reduce_armor
    d.reduce_magic_armor = bullet.reduce_magic_armor
    local vmin,vmax = bullet.damage_min, bullet.damage_max    
    if bullet.level and bullet.level > 0 then
        if type(vmin) == 'table' then
            vmin = table.safe_index(vmin, bullet.level)
            vmax = table.safe_index(vmax, bullet.level)
        else
            if bullet.damage_min_inc then
                vmin = vmin + bullet.damage_min_inc * bullet.level
            end
            if bullet.damage_max_inc then
                vmax = vmax + bullet.damage_max_inc * bullet.level
            end
            if bullet.damage_inc then
                vmax = vmax + bullet.damage_inc * bullet.level
                vmin = vmin + bullet.damage_inc * bullet.level
            end
        end
    end
    -- NOTE: ceil from TowerArcher:getDamage, TowerMage:getDamage
    -- minimum of 1 and ceil from Utils:getPercentOfValue
    local value = math.ceil(U.frandom(vmin,vmax))
    d.value = math.max(1,math.ceil(bullet.damage_factor * value))
    d.target_id = target_id
    d.source_id = source_id
    d.xp_gain_factor = bullet.xp_gain_factor
    d.xp_dest_id = bullet.xp_dest_id
    d.pop = bullet.pop
    d.pop_chance = bullet.pop_chance
    d.pop_conds = bullet.pop_conds
    d.track_damage = bullet.track_damage
    
    return d    
end

local function create_attack_damage(a, target_id, source_id)
    local vmax,vmin = a.damage_max,a.damage_min
    if a.level and a.level > 0 then
        if a.damage_max_inc then
            vmax = vmax + a.damage_max_inc * a.level
        end
        if a.damage_min_inc then
            vmin = vmin + a.damage_min_inc * a.level
        end
        if a.damage_inc then 
            vmax = vmax + a.damage_inc * a.level
            vmin = vmin + a.damage_inc * a.level
        end
    end
    local d = E:create_entity('damage')
    d.value = math.ceil(U.frandom(vmin, vmax))
    d.damage_type = a.damage_type
    d.target_id = target_id
    d.source_id = source_id
    return d
end

local function initial_parabola_speed(from, to, time, g)
    return V.v(
        (to.x - from.x) / time,
        ( (to.y - from.y) - 0.5 * g * time * time ) / time
    )
end

local function position_in_parabola(t, from, speed, g)
    -- ecuacion sacada de BulletParabole.m, sin revisar
    local x = speed.x * t + from.x
    local y = (g * t * t / 2) + speed.y * t + from.y
    return x,y
end

local function parabola_y(phase, from_y, to_y, max_y)
    -- inverted parabola that has a 0 in the origin and in b
    -- the parabola intersects y=C at x=xc
    local max_y = math.max(max_y,from_y,to_y)
    local reverse = (from_y > to_y)
    local offset = reverse and to_y or from_y
    local xc = (reverse and from_y or to_y) - offset
    local M = reverse and ( max_y - to_y ) or ( max_y - from_y )
    local C = (reverse and from_y or to_y) - offset
    local x = reverse and 1-phase or phase    
    local b 
    if C>M or C<0.0001 then
        b = 1
    else    
        b = (2/C) * (M - math.sqrt(M*M - M*C))
    end
    local y = M * 4 / (b * b) * x * (b-x)
    return y + offset
    
end

------------------------------------------------------------
-- hero helper functions

local function soldier_interrupted(this)
    return ( this.nav_rally.new or this.health.dead or this.unit.is_stunned )
end

local function y_soldier_wait(store,this,time)
    return U.y_wait(store,time,
                    function(store,time) return soldier_interrupted(this) end
    )
end

local function y_soldier_animation_wait(this)
    -- returns true if interrupted
    while not U.animation_finished(this) do
        if soldier_interrupted(this) then return true end
        coroutine.yield()
    end
    return false
end

local function hero_will_teleport(this,new_rally_pos)
    local tp = this.teleport
    local r = new_rally_pos
    return ( tp and not tp.disabled and
             V.dist(r.x,r.y, this.pos.x,this.pos.y) > tp.min_distance )
end

local function hero_will_transfer(this,new_rally_pos)
    local tr = this.transfer
    local r = new_rally_pos
    return ( tr and not tr.disabled and
             V.dist(r.x,r.y, this.pos.x,this.pos.y) > tr.min_distance )
end

local function y_hero_walk_waypoints(store,this,animation)
    local animation = animation or 'walk'
    local r = this.nav_rally
    local n = this.nav_grid
    local dest = r.pos
    local x_to_flip = KR_GAME == 'kr5' and 2 or 0 -- if moves less than this in x, keep last flip
    local last_af

    while not V.veq(this.pos, dest) do            
        local w = table.remove(n.waypoints,1) or dest
        local unsnap = (#n.waypoints > 0)  -- only snap last step
        U.set_destination(this,w)        
        local an, af = U.animation_name_facing_point(this,animation,this.motion.dest)
        local new_af = af
        if last_af and math.abs(this.pos.x - this.motion.dest.x) < x_to_flip then
            new_af = last_af
        end
        U.animation_start(this, an, new_af, store.tick_ts, true)
        last_af = new_af
        while not this.motion.arrived do 
            if this.health.dead and not this.health.ignore_damage then return true end -- break w/true if dead 
            if r.new then return false end           -- break w/false if new rally set
            U.walk(this,store.tick_length,nil,unsnap)
            coroutine.yield()
            this.motion.speed.x,this.motion.speed.y = 0,0
        end
    end
end

local function y_hero_new_rally(store,this)
    -- move to the new rally point, following the waypoints
    -- return true: dead  (breaks while loop in hero)
    --        false: new rally or other (stays in while loop in hero)
    local r = this.nav_rally
    if r.new then
        r.new = false
        U.unblock_target(store,this)

        if this.sound_events then
            S:queue(this.sound_events.change_rally_point)
        end
        
        if hero_will_teleport(this,r.pos) then
            -- teleport
            local tp = this.teleport
            -- teleport out
            local vis_bans = this.vis.bans
            this.vis.bans = F_ALL
            this.health.ignore_damage = true
            this.health_bar.hidden = true
            S:queue(tp.sound)
            if tp.fx_out then
                local fx = E:create_entity(tp.fx_out)
                fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
                fx.render.sprites[1].ts = store.tick_ts                
                if fx.tween then fx.tween.ts = store.tick_ts end
                queue_insert(store,fx)
            end
            U.y_animation_play(this,tp.animations[1],nil,store.tick_ts)
            -- delay
            if tp.delay > 0 then 
                U.sprites_hide(this,nil,nil,true)
                U.y_wait(store,tp.delay)
                U.sprites_show(this,nil,nil,true)
            end
            this.pos.x,this.pos.y = r.pos.x,r.pos.y
            U.set_destination(this,this.pos)
            this.motion.speed.x,this.motion.speed.y = 0,0
            -- teleport in
            if tp.fx_in then
                local fx = E:create_entity(tp.fx_in)
                fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
                fx.render.sprites[1].ts = store.tick_ts
                if fx.tween then fx.tween.ts = store.tick_ts end
                queue_insert(store,fx)
            end
            U.y_animation_play(this,tp.animations[2],nil,store.tick_ts)
            this.health_bar.hidden = false
            this.vis.bans = vis_bans
            this.health.ignore_damage = false
            return false
        elseif hero_will_transfer(this,r.pos) then
            -- transfer
            local tr = this.transfer
            local interrupt = false
            local ps = nil
            -- start
            local vis_bans = this.vis.bans
            this.vis.bans = F_ALL
            this.health.ignore_damage = true
            this.health_bar.hidden = true
            S:queue(tr.sound_loop)
            U.y_animation_play(this,tr.animations[1],nil,store.tick_ts)
            this.motion.max_speed = this.motion.max_speed + tr.extra_speed
            if tr.particles_name then
                ps = E:create_entity(tr.particles_name)
                ps.particle_system.track_id = this.id
                queue_insert(store,ps)                
            end
            -- loop
            repeat 
                r.new = false
                if y_hero_walk_waypoints(store,this,tr.animations[2]) then
                    interrupt = true
                    break
                end
            until this.motion.arrived 
            -- end
            if tr.particles_name then
                ps.particle_system.emit = false
                ps.particle_system.source_lifetime = 1  -- cleanup
            end
            this.motion.max_speed = this.motion.max_speed - tr.extra_speed
            S:stop(tr.sound_loop)
            U.y_animation_play(this,tr.animations[3],nil,store.tick_ts)
            this.health_bar.hidden = false
            this.vis.bans = vis_bans
            this.health.ignore_damage = false
            return interrupt
        else
            -- walk
            local vis_bans = this.vis.bans
            local prev_immune = this.health.immune_to
            this.vis.bans = F_ALL
            this.health.immune_to = r.immune_to
            local out = y_hero_walk_waypoints(store,this)
            U.animation_start(this, 'idle', nil, store.tick_ts, true)  -- ranged cooldown holds last anim
            this.vis.bans = vis_bans
            this.health.immune_to = prev_immune
            return out
        end
    end
end
        
local function hero_gain_xp_from_skill(this,skill)
    if skill.level then
        local amount
        if skill.xp_gain then
            amount = skill.xp_gain[skill.level]
        else
            amount = skill.level * skill.xp_gain_factor
        end
        this.hero.xp_queued = this.hero.xp_queued + amount

        -- logging
        if log_xp.level >= log_xp.DEBUG_LEVEL then 
            local skill_name 
            for k,v in pairs(this.hero.skills) do
                if v == skill then
                    skill_name = k
                    break
                end
            end    
            log_xp.debug(
                'XP QUEUE SKILL: (%s)%s xp:%.2f skill:%s level:%s factor:%.2f',
                this.id, this.template_name, amount,
                skill_name, skill.level, skill.xp_gain_factor
            )
        end
    end
end

local function hero_gain_xp(this,value,desc)
    this.hero.xp_queued = this.hero.xp_queued + value
    log_xp.debug('XP QUEUE MANUAL: (%s)%s xp:%.2f from:%s',
                this.id, this.template_name, value, desc)
end

local function hero_level_up(store,this)
    -- return: true if level up pending
    
    local h = this.hero
    if h.xp_queued == 0 then return false end

    -- expected level 
    local expected_level_multiplier = 1
    local expected_level = GS.hero_level_expected[store.level_idx]
    if expected_level then 
        local level_diff = h.level - expected_level
        if level_diff < 0 then
            expected_level_multiplier = GS.hero_level_expected_multipliers_below[km.clamp(1,2,-level_diff)]
        elseif level_diff > 0 then
            expected_level_multiplier = GS.hero_level_expected_multipliers_above[km.clamp(1,2,level_diff)]
        end
    end
    -- difficulty
    local difficulty_multiplier = GS.hero_xp_gain_per_difficulty_mode[store.level_difficulty]
    
    -- net xp gained
    local net_xp = h.xp_queued * expected_level_multiplier * difficulty_multiplier    

    log_xp.debug(
        'XP+: (%s)%s xp:%07.2f + net_xp:%6.2f = %8.2f | net_xp = xp_queued:%s * exp_lvl_mul:%s * diff_mul:%s',
        this.id,this.template_name,
        h.xp, km.round(net_xp), h.xp + km.round(net_xp), 
        h.xp_queued, expected_level_multiplier, difficulty_multiplier
    )

    -- add xp
    h.xp = h.xp + km.round(net_xp)
    h.xp_queued = 0

    -- level up 
    if h.level >= 10 or h.xp < GS.hero_xp_thresholds[h.level] then
        return false
    end
    this.hero.level = this.hero.level + 1
    this.hero.fn_level_up(this,store)
    signal.emit('hero-level-increased', this)
    S:queue('HeroLevelUp')
    return true
end

local function y_hero_death_and_respawn(store,this)
    local h = this.health
    local he = this.hero
    
    this.ui.can_click = false
    local death_ts = store.tick_ts
    local dead_lifetime = h.dead_lifetime
    
    -- unblock
    U.unblock_target(store,this)

    -- death graphic type
    if ( this.selfdestruct and not this.selfdestruct.disabled and
         band(h.last_damage_types,
              bor(DAMAGE_EAT, DAMAGE_HOST, DAMAGE_DISINTEGRATE_BOSS)) == 0 )
    then
        local sd = this.selfdestruct
        this.unit.hide_after_death = true
        this.health_bar.hidden = true
        dead_lifetime = sd.dead_lifetime or dead_lifetime
        U.animation_start(this,sd.animation,nil,store.tick_ts)
        S:queue(this.sound_events.death, this.sound_events.death_args)
        S:queue(sd.sound,sd.sound_args)
        U.y_wait(store,sd.hit_time)
        S:queue(sd.sound_hit)
        if sd.hit_fx then
            insert_sprite(store,sd.hit_fx,this.pos)
        end
        if sd.xp_from_skill then
            hero_gain_xp_from_skill(this,this.hero.skills[sd.xp_from_skill])
        end
        local targets = U.find_enemies_in_range(
            store.entities,this.pos,0,sd.damage_radius,sd.vis_flags,sd.vis_bans
        )
        if targets then
            for _,t in pairs(targets) do
                local d = E:create_entity('damage')
                d.damage_type = sd.damage_type
                d.value = sd.damage and sd.damage or math.random(sd.damage_min, sd.damage_max)
                d.source_id = this.id
                d.target_id = t.id
                queue_damage(store,d)
            end
        end
        U.y_animation_wait(this)

    elseif band(h.last_damage_types, bor(DAMAGE_DISINTEGRATE_BOSS)) ~= 0 then
        this.unit.hide_after_death = true
        local fx = E:create_entity('fx_soldier_desintegrate')
        fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
        fx.render.sprites[1].ts = store.tick_ts
        queue_insert(store,fx)
        
    elseif band(h.last_damage_types, bor(DAMAGE_EAT)) ~= 0 then
        this.unit.hide_after_death = true

    elseif band(h.last_damage_types, bor(DAMAGE_HOST)) ~= 0 then
        this.unit.hide_after_death = true
        S:queue('DeathEplosion')
        local fx = E:create_entity('fx_unit_explode')        
        fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
        fx.render.sprites[1].ts = store.tick_ts
        fx.render.sprites[1].name = fx.render.sprites[1].size_names[this.unit.size]
        queue_insert(store,fx)

        if this.unit.show_blood_pool and this.unit.blood_color ~= BLOOD_NONE then 
            -- blood pool
            local decal = E:create_entity('decal_blood_pool')
            decal.pos = V.vclone(this.pos)
            decal.render.sprites[1].ts = store.tick_ts
            decal.render.sprites[1].name = this.unit.blood_color
            queue_insert(store,decal)
        end

    else
        -- standard death animation
        S:queue(this.sound_events.death, this.sound_events.death_args)
        U.y_animation_play(this, 'death', nil, store.tick_ts, 1)
    end

    this.health.death_finished_ts = store.tick_ts

    -- hide after death
    if this.unit.hide_after_death then
        for _,s in pairs(this.render.sprites) do
            s.hidden = true
        end
    end
    
    local tombstone
    if he and he.tombstone_show_time then
        while store.tick_ts - death_ts < he.tombstone_show_time do
            coroutine.yield()
        end
        -- show tombstone
        tombstone = E:create_entity(he.tombstone_decal)
        tombstone.pos = this.pos
        queue_insert(store,tombstone)
        -- hide hero, if it wasnt hidden before
        for _,s in pairs(this.render.sprites) do
            s.hidden = true
        end
    end
    
    -- wait for respawn
    while store.tick_ts - death_ts < dead_lifetime do
        coroutine.yield()
    end

    -- respawn
    this.health.death_finished_ts = nil
    if tombstone then queue_remove(store,tombstone) end
    if he and he.respawn_point then
        local p = he.respawn_point
        -- do not break references
        this.pos.x,this.pos.y = p.x,p.y 
        this.nav_rally.pos.x,this.nav_rally.pos.y = p.x,p.y
        this.nav_rally.center.x,this.nav_rally.center.y = p.x,p.y
        this.nav_rally.new = false
    end
    for _,s in pairs(this.render.sprites) do
        s.hidden = false
    end
    h.ignore_damage = true
    S:queue(this.sound_events.respawn)
    U.y_animation_play(this,'respawn',nil,store.tick_ts,1)
    this.health_bar.hidden = false
    this.ui.can_click = true
    h.dead = false
    h.hp = h.hp_max
    h.ignore_damage = false
end

local function y_hero_death_and_respawn_kr5(store,this)
    local h = this.health
    local he = this.hero
    
    this.ui.can_click = false
    local death_ts = store.tick_ts
    local dead_lifetime = h.dead_lifetime
    
    -- unblock
    U.unblock_target(store,this)

    -- death graphic type
    if ( this.selfdestruct and not this.selfdestruct.disabled and
         band(h.last_damage_types,
              bor(DAMAGE_EAT, DAMAGE_HOST, DAMAGE_DISINTEGRATE_BOSS)) == 0 )
    then
        local sd = this.selfdestruct
        this.unit.hide_after_death = true
        this.health_bar.hidden = true
        dead_lifetime = sd.dead_lifetime or dead_lifetime
        U.animation_start(this,sd.animation,nil,store.tick_ts)
        S:queue(this.sound_events.death, this.sound_events.death_args)
        S:queue(sd.sound,sd.sound_args)
        U.y_wait(store,sd.hit_time)
        S:queue(sd.sound_hit)
        if sd.hit_fx then
            insert_sprite(store,sd.hit_fx,this.pos)
        end
        if sd.xp_from_skill then
            hero_gain_xp_from_skill(this,this.hero.skills[sd.xp_from_skill])
        end
        local targets = U.find_enemies_in_range(
            store.entities,this.pos,0,sd.damage_radius,sd.vis_flags,sd.vis_bans
        )
        if targets then
            for _,t in pairs(targets) do
                local d = E:create_entity('damage')
                d.damage_type = sd.damage_type
                d.value = sd.damage and sd.damage or math.random(sd.damage_min, sd.damage_max)
                d.source_id = this.id
                d.target_id = t.id
                queue_damage(store,d)
            end
        end
        U.y_animation_wait(this)

    elseif band(h.last_damage_types, bor(DAMAGE_DISINTEGRATE_BOSS)) ~= 0 then
        this.unit.hide_after_death = true
        local fx = E:create_entity('fx_soldier_desintegrate')
        fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
        fx.render.sprites[1].ts = store.tick_ts
        queue_insert(store,fx)
        
    elseif band(h.last_damage_types, bor(DAMAGE_EAT)) ~= 0 then
        this.unit.hide_after_death = true

    elseif band(h.last_damage_types, bor(DAMAGE_HOST)) ~= 0 then
        this.unit.hide_after_death = true
        S:queue('DeathEplosion')
        local fx = E:create_entity('fx_unit_explode')        
        fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
        fx.render.sprites[1].ts = store.tick_ts
        fx.render.sprites[1].name = fx.render.sprites[1].size_names[this.unit.size]
        queue_insert(store,fx)

        if this.unit.show_blood_pool and this.unit.blood_color ~= BLOOD_NONE then 
            -- blood pool
            local decal = E:create_entity('decal_blood_pool')
            decal.pos = V.vclone(this.pos)
            decal.render.sprites[1].ts = store.tick_ts
            decal.render.sprites[1].name = this.unit.blood_color
            queue_insert(store,decal)
        end

    else
        -- standard death animation
        S:queue(this.sound_events.death, this.sound_events.death_args)
        if this.unit.death_animation then
            U.animation_start(this, this.unit.death_animation, nil, store.tick_ts, false)
        else
            U.animation_start(this, 'death', nil, store.tick_ts, false)
        end
    end

    if not he.tombstone_concurrent_with_death then
        U.y_animation_wait(this)
        this.health.death_finished_ts = store.tick_ts
        -- hide after death
        if this.unit.hide_after_death then
            for _,s in pairs(this.render.sprites) do
                s.hidden = true
            end
        end
    end
    
    local tombstone
    if he and he.tombstone_show_time then
        while store.tick_ts - death_ts < he.tombstone_show_time do
            coroutine.yield()
        end
        -- show tombstone
        tombstone = E:create_entity(he.tombstone_decal)

        if he.tombstone_force_over_path then
            local nodes = P:nearest_nodes(this.pos.x,this.pos.y,nil,{1, 2, 3},true)
            local pi,spi,ni = unpack(nodes[1])
            local npos = P:node_pos(pi,spi,ni)
            tombstone.pos = npos
        else
            tombstone.pos = this.pos
        end

        for _,s in pairs(tombstone.render.sprites) do
            s.ts = store.tick_ts
        end
        queue_insert(store,tombstone)
    end

    if he.tombstone_concurrent_with_death then
        U.y_animation_wait(this)
        this.health.death_finished_ts = store.tick_ts
    end

    -- hide hero, if it wasnt hidden before
    if this.unit.hide_after_death then
        for _,s in pairs(this.render.sprites) do
            s.hidden = true
        end
    end

    -- wait for respawn
    while store.tick_ts - death_ts < dead_lifetime do
        if this.force_respawn then
            this.force_respawn = nil
            break
        end
        coroutine.yield()
    end

    -- respawn
    this.health.death_finished_ts = nil

    if he and he.tombstone_force_over_path then
        he.respawn_point = tombstone.pos
    end

    if tombstone and tombstone.tween then
        tombstone.tween.disabled = false
        tombstone.tween.ts = store.tick_ts
    end

    if he and he.tombstone_respawn_animation then
        U.animation_start(tombstone,he.tombstone_respawn_animation,nil,store.tick_ts)
    end

    if he and he.respawn_point then
        local p = he.respawn_point
        -- do not break references
        this.pos.x,this.pos.y = p.x,p.y 
        this.nav_rally.pos.x,this.nav_rally.pos.y = p.x,p.y
        this.nav_rally.center.x,this.nav_rally.center.y = p.x,p.y
        this.nav_rally.new = false
    end
    for _,s in pairs(this.render.sprites) do
        if this.use_hidden_count_on_respawn and s.hidden_count then
            s.hidden = (s.hidden_count > 0)
        else
            s.hidden = false
        end
    end
    h.ignore_damage = true
    S:queue(this.sound_events.respawn)
    if he.respawn_animation then
        U.y_animation_play(this, he.respawn_animation, nil, store.tick_ts, 1)
    else
        U.y_animation_play(this, 'respawn', nil, store.tick_ts, 1)
    end
    if tombstone then queue_remove(store,tombstone) end
    this.health_bar.hidden = false
    this.ui.can_click = true
    h.dead = false
    this.force_respawn = nil
    h.hp = h.hp_max
    h.ignore_damage = false
end

------------------------------------------------------------
-- soldier helper functions

local function y_reinforcement_fade_in(store,this)
    -- Note: the walk in only in the ipad version
    --this.pos.x = this.pos.x - 20
    --this.pos.y = this.pos.y - 20
    --U.set_destination(this,this.nav_rally.pos)    
    --local an,af,ai = U.animation_name_for_angle(this,'walk',0)
    --U.animation_start(this,an,af,store.tick_ts,-1)
    --while not this.motion.arrived do
    --    U.walk(this,store.tick_length)
    --    coroutine.yield()
    --end

    -- fade in
    U.y_wait(store,fts(10))
    this.tween.disabled = true
end

local function y_reinforcement_fade_out(store,this)
    -- fade out 
    this.render.sprites[1].ts = store.tick_ts
    local offset = 50
    if this.render.sprites[1].flip_x then offset = -1 * offset end
    local o = V.v(this.pos.x + offset, this.pos.y)
    U.set_destination(this,o)
    local t_angle = offset > 0 and 0 or math.pi 
    local an,af,ai = U.animation_name_for_angle(this,'walk',t_angle)
    U.animation_start(this,an,af,store.tick_ts,-1)
    this.tween.reverse = true
    this.tween.disabled = nil
    this.health.hp = 0   -- to hide the health bar mostly
    while not this.motion.arrived do
        U.walk(this,store.tick_length)
        coroutine.yield()
    end
end

local function y_soldier_new_rally(store,this)
    -- moves to the new rally point in a straight line
    -- return true: dead or stunned
    --        false: arrived or new rally
    local r = this.nav_rally
    local out = false
    local vis_bans = this.vis.bans
    local prev_immune = this.health.immune_to
    this.health.immune_to = r.immune_to
    this.vis.bans = F_ALL
    if r.new then
        r.new = false
        U.unblock_target(store,this)
        U.set_destination(this,r.pos)
        if r.delay_max then
            -- idle + random wait
            U.animation_start(this,this.idle_flip.last_animation,nil,store.tick_ts,this.idle_flip.loop)
            if y_soldier_wait(store,this,r.delay_min + (r.delay_max - r.delay_min) * math.random()) then
                goto interrupt
            end
        end
        local an, af = U.animation_name_facing_point(this,'walk',this.motion.dest)
        U.animation_start(this, an, af, store.tick_ts, -1)                        
        while not this.motion.arrived do
            if this.health.dead or this.unit.is_stunned then
                out = true
                goto interrupt
            end
            if r.new then
                out = false
                goto interrupt
            end
            U.walk(this,store.tick_length)
            coroutine.yield()
            this.motion.speed.x,this.motion.speed.y = 0,0
        end
    end
    ::interrupt::
    this.vis.bans = vis_bans
    this.health.immune_to = prev_immune
    return out
end

local function y_soldier_revive(store,this)
    -- returns true if succesful
    -- eg: holy grail
    
    if ( not this.revive or this.revive.disabled or
         this.unit.is_stunned or
         band(this.health.last_damage_types,
              bor(DAMAGE_DISINTEGRATE, DAMAGE_EAT, DAMAGE_DISINTEGRATE_BOSS)) ~= 0 )
    then
        return false
    end
    
    local r = this.revive
    if math.random() < r.chance then 
        local r = this.revive
        if r.remove_modifiers then remove_modifiers(store,this) end
        this.health.ignore_damage = true
        this.health.dead = false
        this.health_bar.hidden = false
        -- re-block enemy so it does not advance while recovering health                
        if this.soldier.target_id then 
            local enemy = store.entities[this.soldier.target_id]
            if enemy then 
                U.block_enemy(store,this,enemy)
            end
        end
        -- revive fx
        if r.fx then
            local fx = E:create_entity(r.fx)
            fx.pos = this.pos  -- reference 
            fx.render.sprites[1].ts = store.tick_ts
            queue_insert(store,fx)
        end
        -- revive animation
        if r.animation then
            S:queue(r.sound)
            U.animation_start(this, r.animation, nil, store.tick_ts, false)
            r.ts = store.tick_ts
            while (store.tick_ts - r.ts < r.hit_time) do
                coroutine.yield()
            end
        end
        r.revive_count = ( r.revive_count or 0 ) + 1
        signal.emit('entity-revived',this,r.revive_count)
        -- recover health at hit time
        this.health.hp = km.clamp(0,this.health.hp_max,  this.health.hp + this.health.hp_max * r.health_recover)
        -- finish animation
        if r.animation then 
            while not U.animation_finished(this) do
                coroutine.yield()
            end
        end
        this.health.ignore_damage = false
        return true
    end
    return false
end

local function y_soldier_death(store,this)
    -- unblock enemy
    U.unblock_target(store,this)

    -- death graphic type
    local h = this.health
    if band(h.last_damage_types,
            bor(DAMAGE_DISINTEGRATE,DAMAGE_DISINTEGRATE_BOSS)) ~= 0 
    then
        this.unit.hide_during_death = true
        local fx = E:create_entity('fx_soldier_desintegrate')
        fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
        fx.render.sprites[1].ts = store.tick_ts
        queue_insert(store,fx)
        
    elseif band(h.last_damage_types, bor(DAMAGE_EAT)) ~= 0 then
        this.unit.hide_during_death = true

    elseif band(h.last_damage_types, bor(DAMAGE_HOST)) ~= 0 then
        -- alien host death
        S:queue(this.sound_events.death_by_explosion)
        this.unit.hide_during_death = true

        local fx = E:create_entity('fx_unit_explode')        
        fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
        fx.render.sprites[1].ts = store.tick_ts
        fx.render.sprites[1].name = fx.render.sprites[1].size_names[this.unit.size]
        queue_insert(store,fx)

        if this.unit.show_blood_pool and this.unit.blood_color ~= BLOOD_NONE then 
            -- blood pool
            local decal = E:create_entity('decal_blood_pool')
            decal.pos = V.vclone(this.pos)
            decal.render.sprites[1].ts = store.tick_ts
            decal.render.sprites[1].name = this.unit.blood_color
            queue_insert(store,decal)
        end
        
    elseif this.reinforcement and (this.reinforcement.fade or this.reinforcement.fade_out) then
        -- reinforcements death
        y_reinforcement_fade_out(store,this)
        return
    else
        -- standard death
        S:queue(this.sound_events.death,this.sound_events.death_args)
        U.y_animation_play(this,'death',nil,store.tick_ts,1)
        this.ui.can_select = false
    end
    
    this.health.death_finished_ts = store.tick_ts

    -- ui
    if this.ui then
        if IS_TRILOGY then
            this.ui.can_click = not this.unit.hide_after_death
        else
            this.ui.can_click = this.ui.can_click and not this.unit.hide_after_death
        end
        this.ui.z = -1
    end
    
    -- hide 
    if this.unit.hide_during_death or this.unit.hide_after_death then
        U.sprites_hide(this, nil, nil, true)
    end

    -- fade
    if this.unit.fade_time_after_death then
        local delay = this.unit.fade_time_after_death
        local duration = this.unit.fade_duration_after_death
        if this.health and this.health.delete_after and duration then
            -- adjust the delay so the fade is shown
            delay = this.health.delete_after - store.tick_ts - duration
        end
        fade_out_entity(store, this, delay, duration)
    end
end

-- ranged attacks

local function y_soldier_do_loopable_ranged_attack(store,this,target,attack)
    local attack_done = false
    local start_ts = store.tick_ts
    local b
    local an,af,ai
    
    -- pre animation
    S:queue(attack.sound, attack.sound_args)
    if attack.animations[1] then 
        an,af,ai = U.animation_name_facing_point(this,attack.animations[1],target.pos)
        U.y_animation_play_group(this,an,af,store.tick_ts,1,attack.sprite_group)
    end

    -- loop animation
    for i=1,attack.loops do
        an,af,ai = U.animation_name_facing_point(this,attack.animations[2],target.pos)
        U.animation_start_group(this,an,af,store.tick_ts,false,attack.sprite_group)
        
        for si,st in pairs(attack.shoot_times) do
            -- shoot time
            while store.tick_ts - U.get_animation_ts(this,attack.sprite_group) < st do
                if this.unit.is_stunned then goto finish end  -- stunned                
                if ( this.health.dead or ( this.nav_rally and this.nav_rally.new ) )
                then
                    goto interrupt
                end
                coroutine.yield()
            end

            -- fire
            b = E:create_entity(attack.bullet)
            b.pos = V.vclone(this.pos)
            if attack.bullet_start_offset then
                local offset = attack.bullet_start_offset[ai]  -- from the animation angle
                b.pos.x,b.pos.y = b.pos.x + ( af and -1 or 1 ) * offset.x, b.pos.y + offset.y
            end
            if attack.bullet_shot_start_offset then
                local offset = attack.bullet_shot_start_offset[si]  -- from the shot index
                b.pos.x,b.pos.y = b.pos.x + ( af and -1 or 1 ) * offset.x, b.pos.y + offset.y
            end
            b.bullet.from = V.vclone(b.pos)
            b.bullet.to = V.v(target.pos.x + target.unit.hit_offset.x,target.pos.y + target.unit.hit_offset.y)
            b.bullet.target_id = target.id
            b.bullet.shot_index = si
            b.bullet.loop_index = i
            b.bullet.source_id = this.id
            b.bullet.xp_dest_id = this.id

            if IS_KR5 and attack.level then
                b.bullet.level = attack.level
            end

            queue_insert(store, b)

            -- gain xp (one per bullet fired)
            if attack.xp_from_skill then
                hero_gain_xp_from_skill(this,this.hero.skills[attack.xp_from_skill])
            end
            
            -- at least one shot
            attack_done = true
        end

        while not U.animation_finished_group(this,attack.sprite_group) do
            if this.unit.is_stunned then goto finish end  -- stunned                
            if ( this.health.dead or ( this.nav_rally and this.nav_rally.new ) )
            then
                goto interrupt
            end
            coroutine.yield()
        end
    end

    ::finish::
    if attack.animations[3] then
        an,af,ai = U.animation_name_facing_point(this,attack.animations[3],target.pos)
        U.animation_start_group(this,an,af,store.tick_ts,false,attack.sprite_group)

        while not U.animation_finished_group(this,attack.sprite_group) do
            if ( this.health.dead or ( this.nav_rally and this.nav_rally.new )  )
            then
                goto interrupt
            end
            coroutine.yield()
        end
    end

    ::interrupt::
    -- gain xp (once)
    if attack_done and attack.xp_from_skill_once then 
        hero_gain_xp_from_skill(this,this.hero.skills[attack.xp_from_skill_once])
    end
    return attack_done
end


local function y_soldier_do_ranged_attack(store,this,target,attack,pred_pos)
    local attack_done = false
    local start_ts = store.tick_ts
    local bullet
    local bullet_to = pred_pos or target.pos
    local bullet_to_start = V.vclone(bullet_to)
    
    -- start animation    
    local an,af,ai = U.animation_name_facing_point(this,attack.animation,bullet_to)
    U.animation_start(this,an,af,store.tick_ts,false)
    S:queue(attack.sound, attack.sound_args)

    -- wait for shoot time
    while (store.tick_ts - start_ts < attack.shoot_time) do
        if ( this.unit.is_stunned or
             this.health.dead or
             ( this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    -- still valid target
    if attack.check_target_before_shot and (target.health.dead or not store.entities[target.id])
    then
        log.debug('target (%s) is dead or removed from store', target.id)
        goto interrupt
    end
    
    -- still in track range
    if ( attack.max_track_distance  and 
             V.dist(target.pos.x,target.pos.y, bullet_to_start.x,bullet_to_start.y) > attack.max_track_distance )
    then 
        log.debug('target (%s) at %s,%s  exceeds attack.max_track_distance %s to %s,%s',
                  target.id, target.pos.x,target.pos.y, attack.max_track_distance, bullet_to_start.x,bullet_to_start.y)
        goto interrupt
    end

    -- shoot
    S:queue(attack.sound_shoot)
    bullet = E:create_entity(attack.bullet)
    bullet.pos = V.vclone(this.pos)
    if attack.bullet_start_offset then
        local offset = attack.bullet_start_offset[ai]
        bullet.pos.x,bullet.pos.y = bullet.pos.x + ( af and -1 or 1 ) * offset.x, bullet.pos.y + offset.y
    end
    bullet.bullet.from = V.vclone(bullet.pos)
    bullet.bullet.to = V.vclone(bullet_to)
    if not attack.ignore_hit_offset then
        bullet.bullet.to.x = bullet.bullet.to.x + target.unit.hit_offset.x
        bullet.bullet.to.y = bullet.bullet.to.y + target.unit.hit_offset.y
    end        
    bullet.bullet.target_id = target.id
    bullet.bullet.source_id = this.id
    bullet.bullet.xp_dest_id = this.id
    bullet.bullet.level = attack.level
    if bullet.bullet.use_unit_damage_factor then
        bullet.bullet.damage_factor = this.unit.damage_factor
    end
    queue_insert(store,bullet)

    -- gain xp (one per bullet fired)
    if attack.xp_from_skill then
        hero_gain_xp_from_skill(this,this.hero.skills[attack.xp_from_skill])
    end
    
    -- at least one shot
    attack_done = true
    
    -- wait animation
    while not U.animation_finished(this) do
        if ( this.unit.is_stunned or
             this.health.dead or
             ( this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    ::interrupt::
    return attack_done
end


local function soldier_pick_ranged_target_and_attack(store,this)
    local in_range = false
    local awaiting_target = nil
    for _,i in pairs(this.ranged.order) do
        local a = this.ranged.attacks[i]
        -- Flying enemies override the min range check, so
        -- they are targeted anyway when very near.
        -- origin = current position
        if a.disabled then goto skip end
        if a.sync_animation and not this.render.sprites[1].sync_flag then goto skip end
        local target,_,pred_pos = U.find_foremost_enemy(
            store.entities,this.pos,a.min_range,a.max_range,
            a.node_prediction,a.vis_flags,a.vis_bans,a.filter_fn,F_FLYING
        )        
        if target then
            if pred_pos then 
                log.paranoid(' target.pos:%s,%s  pred_pos:%s,%s', target.pos.x,target.pos.y,pred_pos.x,pred_pos.y)
            end
            local ready = store.tick_ts - a.ts >= a.cooldown
            if this.ranged.forced_cooldown then
                ready = ready and ( store.tick_ts - this.ranged.forced_ts >= this.ranged.forced_cooldown )
            end
            
            if not ready then
                awaiting_target = target
            elseif math.random() <= a.chance then
                return target, a, pred_pos
            else
                -- to avoid trying the same attack multiple time when chance fails
                a.ts = store.tick_ts
            end
        end
        ::skip::
    end
    return awaiting_target,nil
end

local function y_soldier_ranged_attacks(store,this)
    -- returns brk,sta
    --   brk == true: interrupt main loop
    --       - dead, change rally, stun
    --   brk == false: continue main loop
    --       - sta: A_NO_TARGET, A_IN_COOLDOWN, A_DONE
    local target,attack,pred_pos = soldier_pick_ranged_target_and_attack(store,this)
    if not target then
        return false, A_NO_TARGET
    end    
    if not attack then
        return false, A_IN_COOLDOWN
    end

    local start_ts = store.tick_ts
    local attack_done
    U.set_destination(this, this.pos) -- to stop moving
    if attack.loops then
        attack_done = y_soldier_do_loopable_ranged_attack(store,this,target,attack)
    else
        attack_done = y_soldier_do_ranged_attack(store,this,target,attack,pred_pos)
    end
    if attack_done then
        attack.ts = start_ts
        if attack.shared_cooldown then
            -- shared cooldown resets all ts when one works, so no attacks
            -- are made back to back
            for _,aa in pairs(this.ranged.attacks) do
                if aa ~= attack and aa.shared_cooldown then aa.ts = attack.ts end
            end
        end
        if this.ranged.forced_cooldown then
            this.ranged.forced_ts = start_ts
        end
    end

    if attack_done then
        return false, A_DONE
    else
        return true
    end    
end

-- timed actions (not dependent on a target)

local function y_soldier_do_timed_action(store,this,action)
    local action_done = false
    local start_ts = store.tick_ts
    
    -- start animation
    U.animation_start(this,action.animation,nil,store.tick_ts)
    S:queue(action.sound)
    if action.cast_time then
        if y_soldier_wait(store,this,action.cast_time) then
            goto interrupt
        end
    end    
            
    -- cooldown
    action.ts = start_ts
    action_done = true

    -- do action
    if action.mod then
        -- mods are self targeted
        local e = E:create_entity(action.mod)
        e.modifier.target_id = this.id
        e.modifier.source_id = this.id
        e.modifier.level = action.level
        queue_insert(store,e)
    elseif action.aura then
        -- auras are dropped in place
        local e = E:create_entity(action.aura)
        e.aura.source_id = this.id
        e.aura.level = action.level
        e.pos = V.vclone(this.pos)
        queue_insert(store,e)
    end
    
    -- wait animation
    y_soldier_animation_wait(this)

    ::interrupt::
    return action_done
end

local function y_soldier_timed_actions(store,this)
    -- returns brk,sta
    --   brk == true: interrupt main loop
    --       - dead, change rally, stun
    --   brk == false: continue main loop
    --       - sta: A_NO_TARGET, A_IN_COOLDOWN, A_DONE

    for _,a in pairs(this.timed_actions.list) do
        if a.disabled or store.tick_ts - a.ts < a.cooldown then 
            goto continue
        end
        -- check action.fn_can() if exists
        if a.fn_can and not a.fn_can(this,store,a) then
            return false, A_NO_TARGET
        else
            if not a.chance or math.random() < a.chance then
                local attack_done = y_soldier_do_timed_action(store,this,a)
                if attack_done then
                    return false, A_DONE
                else
                    return true
                end
            else
                -- to avoid trying the same attack multiple time when chance fails
                a.ts = store.tick_ts
            end
        end
        ::continue::
    end
    return false, A_IN_COOLDOWN
end

-- timed attacks

local function y_soldier_do_timed_attack(store,this,target,attack)
    local attack_done = false
    local start_ts = store.tick_ts
    local spell 
    
    -- start animation
    local an,af = U.animation_name_facing_point(this,attack.animation,target.pos)
    U.animation_start(this,an,af,store.tick_ts)
    S:queue(attack.sound)
    
    while (store.tick_ts - start_ts < attack.cast_time) do
        if ( this.health.dead or 
             this.unit.is_stunned or 
             ( this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    -- cooldown
    attack.ts = start_ts

    -- cast
    spell = E:create_entity(attack.spell)
    spell.spell.source_id = this.id
    spell.spell.target_id = target.id
    queue_insert(store,spell)

    attack_done = true
    
    -- wait animation
    while not U.animation_finished(this) do
        if ( this.health.dead or 
             ( this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    ::interrupt::
    S:stop(attack.sound)

    return attack_done
end

local function y_soldier_timed_attacks(store,this)
    -- returns brk,sta
    --   brk == true: interrupt main loop
    --       - dead, change rally, stun
    --   brk == false: continue main loop
    --       - sta: A_NO_TARGET, A_IN_COOLDOWN, A_DONE

    for _,a in pairs(this.timed_attacks.list) do
        if store.tick_ts - a.ts < a.cooldown then
            goto continue
        end
        local target = U.find_foremost_enemy(
            store.entities, this.pos, a.min_range, a.max_range,
            false, a.vis_flags, a.vis_bans
        )
        if not target then 
            return false, A_NO_TARGET
        else
            if math.random() < a.chance then
                local attack_done = y_soldier_do_timed_attack(store,this,target,a)
                if attack_done then
                    return false, A_DONE
                else
                    return true
                end
            else
                -- to avoid trying the same attack multiple time when chance fails
                a.ts = store.tick_ts
            end
        end
        ::continue::
    end
    return false, A_IN_COOLDOWN
end

-- area attack

local function y_soldier_do_single_area_attack(store,this,target,attack)
    local attack_done = false
    local start_ts = store.tick_ts
    local targets
    local hit_pos

    -- start animation
    local an,af = U.animation_name_facing_point(this,attack.animation,target.pos)
    U.animation_start(this,an,af,store.tick_ts,1)
    S:queue(attack.sound, attack.sound_args)

    -- wait for hit time
    while (store.tick_ts - start_ts < attack.hit_time) do
        if ( this.health.dead or 
             this.unit.is_stunned or 
             ( this.dodge and this.dodge.active and not this.dodge.silent ) or 
             ( this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    S:queue(attack.sound_hit,attack.sound_hit_args)
    
    -- cooldown
    attack.ts = start_ts
    if attack.shared_cooldown then
        -- shared cooldown resets all ts when one works, so no attacks
        -- are made back to back
        for _,aa in pairs(this.melee.attacks) do
            if aa ~= attack and aa.shared_cooldown then aa.ts = attack.ts end
        end
    end                            
    if attack.forced_cooldown then
        -- mark when the last attack happened
        this.melee.forced_ts = attack.ts
    end
    if attack.cooldown_group then
        for _,aa in pairs(this.melee.attacks) do
            if aa ~= attack and aa.cooldown_group == attack.cooldown_group then
                aa.ts = attack.ts
            end
        end
    end

    if attack.signal then
        signal.emit('soldier-attack',this,attack,attack.signal)
    end
    
    -- hit position offset
    hit_pos = V.vclone(this.pos)
    if attack.hit_offset then
        hit_pos.x = hit_pos.x + ( af and -1 or 1 ) * attack.hit_offset.x
        hit_pos.y = hit_pos.y + attack.hit_offset.y
    end

    -- do damage (using damage_flags/bans)
    targets = table.filter(
        store.entities,
        function(k,v)
            return(
                v.enemy and v.vis and
                v.health and not v.health.dead and
                band(v.vis.flags, attack.damage_bans) == 0 and
                band(v.vis.bans, attack.damage_flags) == 0 and
                U.is_inside_ellipse(v.pos, hit_pos, attack.damage_radius)
            )
        end
    )
    if attack.count then
        table.sort(
            targets,
            function(e1,e2)
                return ( V.dist(e1.pos.x,e1.pos.y, hit_pos.x,hit_pos.y) <
                         V.dist(e2.pos.x,e2.pos.y, hit_pos.x,hit_pos.y) )
            end
        )
    end
    for i=1,math.min(attack.count or #targets, #targets) do 
        local e = targets[i]
        local d = E:create_entity('damage')
        d.source_id = this.id
        d.target_id = e.id
        d.damage_type = attack.damage_type
        d.value = math.random(attack.damage_min, attack.damage_max)
        d.track_kills = (this.track_kills ~= nil)
        d.track_damage = attack.track_damage
        d.xp_gain_factor = attack.xp_gain_factor
        d.xp_dest_id = attack.xp_dest_id
        d.pop = attack.pop
        d.pop_chance = attack.pop_chance
        d.pop_conds = attack.pop_conds

        if IS_KR5 then
            d.reduce_armor = attack.reduce_armor
            d.reduce_magic_armor = attack.reduce_magic_armor
        end

        queue_damage(store,d)

        -- apply mod if any
        if attack.mod then
            local mod = E:create_entity(attack.mod)
            mod.modifier.ts = store.tick_ts
            mod.modifier.target_id = e.id
            mod.modifier.source_id = this.id
            mod.modifier.level = attack.level
            mod.modifier.target_idx = i  -- for delays and custom effects
            queue_insert(store,mod)
        end        
    end

    -- apply aura if any
    if attack.hit_aura then
        local a = E:create_entity(attack.hit_aura)
        a.pos = V.vclone(hit_pos)
        a.aura.target_id = target.id
        a.aura.source_id = this.id
        queue_insert(store,a)
    end

    -- effects / decals
    if attack.hit_fx then
        local fx = E:create_entity(attack.hit_fx)
        fx.pos = V.vclone(hit_pos)
        for i=1,#fx.render.sprites do
            fx.render.sprites[i].ts = store.tick_ts
        end
        queue_insert(store,fx)
    end
    if attack.hit_decal then
        local fx = E:create_entity(attack.hit_decal)
        fx.pos = V.vclone(hit_pos)
        for i=1,#fx.render.sprites do
            fx.render.sprites[i].ts = store.tick_ts
        end
        queue_insert(store,fx)
    end

    -- area damage done
    attack_done = true
    
    -- end animation
    while not U.animation_finished(this) do
        if ( this.health.dead or 
             this.unit.is_stunned or 
             ( this.dodge and this.dodge.active and not this.dodge.silent ) or 
             ( this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    ::interrupt:: 
    S:stop(attack.sound)
    
    return attack_done
end


-- melee attacks

local function y_soldier_do_loopable_melee_attack(store,this,target,attack)
    local attack_done = false
    local start_ts = store.tick_ts
    local an,af
    
    S:queue(attack.sound,attack.sound_args)

    -- start animation
    if attack.animations[1] then 
        an,af = U.animation_name_facing_point(this,attack.animations[1],target.pos)
        U.y_animation_play(this,an,af,store.tick_ts,1)
    end
    

    -- loop animation
    for i=1,attack.loops do
        if attack.interrupt_loop_on_dead_target and target.health.dead then
            log.debug('interrupt_loop_on_dead_target')
            goto interrupt
        end

        local loop_ts = store.tick_ts
        S:queue(attack.sound_loop,attack.sound_loop_args)
        an,af = U.animation_name_facing_point(this,attack.animations[2],target.pos)
        U.animation_start(this,an,af,store.tick_ts,1)

        -- multi hit times
        local hit_times = attack.hit_times and attack.hit_times or { attack.hit_time }
        for _,ht in pairs(hit_times) do
            -- hit time
            while store.tick_ts - loop_ts < ht do
                if this.unit.is_stunned then
                    goto finish
                end
                if attack.interrupt_on_dead_target and target.health.dead then
                    log.debug('interrupt_on_dead_target')
                    goto interrupt
                end
                if ( this.health.dead or 
                     ( this.nav_rally and this.nav_rally.new )  )
                then
                    goto interrupt
                end
                coroutine.yield()
            end

            S:queue(attack.sound_hit,attack.sound_hit_args)

            -- cooldown
            attack.ts = start_ts
            if attack.shared_cooldown then
                -- shared cooldown between melees
                for _,aa in pairs(this.melee.attacks) do
                    if aa ~= attack and aa.shared_cooldown then aa.ts = attack.ts end
                end
            end                            
            if attack.forced_cooldown then
                -- mark when the last attack happened
                this.melee.forced_ts = attack.ts
            end
            if attack.cooldown_group then
                for _,aa in pairs(this.melee.attacks) do
                    if aa ~= attack and aa.cooldown_group == attack.cooldown_group then
                        aa.ts = attack.ts
                    end
                end
            end
           
            -- hit position offset
            local hit_pos = V.vclone(this.pos)
            if attack.hit_offset then
                hit_pos.x = hit_pos.x + ( af and -1 or 1 ) * attack.hit_offset.x
                hit_pos.y = hit_pos.y + attack.hit_offset.y
            end
            if attack.type == 'area' then
                -- area attack type     
                
                -- do damage (using damage_flags/bans)
                local targets = table.filter(
                    store.entities,
                    function(k,v)
                        return(
                            v.enemy and v.vis and
                            v.health and not v.health.dead and
                            band(v.vis.flags, attack.damage_bans) == 0 and
                            band(v.vis.bans, attack.damage_flags) == 0 and
                            U.is_inside_ellipse(v.pos, hit_pos, attack.damage_radius)
                        )
                    end
                )
                if (attack.include_blocked and target and this.soldier
                    and this.soldier.target_id == target.id
                    and not table.contains(targets, target))
                then
                    table.insert(targets, target)
                end
                for _,e in pairs(targets) do
                    local d = E:create_entity('damage')
                    d.source_id = this.id
                    d.target_id = e.id
                    d.damage_type = attack.damage_type
                    d.value = math.random(attack.damage_min, attack.damage_max)
                    d.track_kills = (this.track_kills ~= nil)
                    d.track_damage = attack.track_damage
                    d.xp_gain_factor = attack.xp_gain_factor
                    d.xp_dest_id = attack.xp_dest_id
                    d.pop = attack.pop
                    d.pop_chance = attack.pop_chance
                    d.pop_conds = attack.pop_conds

                    if IS_KR5 then
                        d.reduce_armor = attack.reduce_armor
                        d.reduce_magic_armor = attack.reduce_magic_armor
                    end
                    
                    queue_damage(store,d)

                    -- apply mod if any
                    if attack.mod then
                        local mod = E:create_entity(attack.mod)
                        mod.modifier.ts = store.tick_ts
                        mod.modifier.target_id = e.id
                        mod.modifier.source_id = this.id
                        mod.modifier.level = attack.level
                        queue_insert(store,mod)
                    end
                end

                -- effects / decals
                if attack.hit_fx then
                    local fx = E:create_entity(attack.hit_fx)
                    fx.pos = V.vclone(hit_pos)
                    for i=1,#fx.render.sprites do
                        fx.render.sprites[i].ts = store.tick_ts
                    end
                    queue_insert(store,fx)
                end
                if attack.hit_decal then
                    local fx = E:create_entity(attack.hit_decal)
                    fx.pos = V.vclone(hit_pos)
                    for i=1,#fx.render.sprites do
                        fx.render.sprites[i].ts = store.tick_ts
                    end
                    queue_insert(store,fx)
                end                

            else
                -- melee type attack                
                -- damage only in blockers list
                if this.soldier and this.soldier.target_id == target.id then 
                    local d = E:create_entity('damage')
                    if attack.instakill then 
                        d.damage_type = DAMAGE_INSTAKILL
                    elseif attack.fn_damage then
                        d.damage_type = attack.damage_type
                        d.value = attack.fn_damage(this,store,attack,target)
                    else
                        d.damage_type = attack.damage_type
                        d.value = math.ceil(this.unit.damage_factor * math.random(attack.damage_min,
                                                                                  attack.damage_max))
                    end
                    d.source_id = this.id 
                    d.target_id = target.id
                    d.xp_gain_factor = attack.xp_gain_factor
                    d.xp_dest_id = attack.xp_dest_id
                    d.pop = attack.pop
                    d.pop_chance = attack.pop_chance
                    d.pop_conds = attack.pop_conds                       
                    queue_damage(store, d)

                    -- apply mod if any
                    if attack.mod then
                        local mod = E:create_entity(attack.mod)
                        mod.modifier.ts = store.tick_ts
                        mod.modifier.target_id = target.id
                        mod.modifier.source_id = this.id
                        queue_insert(store,mod)
                    end

                    -- effects / decals
                    if attack.hit_fx then
                        local fx = E:create_entity(attack.hit_fx)
                        fx.pos = V.vclone(hit_pos)
                        for i=1,#fx.render.sprites do
                            fx.render.sprites[i].ts = store.tick_ts
                        end
                        queue_insert(store,fx)
                    end

                    if attack.hit_decal then
                        local fx = E:create_entity(attack.hit_decal)
                        fx.pos = V.vclone(hit_pos)
                        for i=1,#fx.render.sprites do
                            fx.render.sprites[i].ts = store.tick_ts
                        end
                        queue_insert(store,fx)
                    end
                end
            end

            -- at least one hit
            attack_done = true
        end

        while not U.animation_finished(this) do
            if this.unit.is_stunned then goto finish end  -- stunned                
            if ( this.health.dead or 
                 ( this.nav_rally and this.nav_rally.new ) )
            then
                goto interrupt
            end
            coroutine.yield()
        end
    end

    if attack.signal then
        signal.emit('soldier-attack',this,attack,attack.signal)
    end

    ::finish::    
    -- end animation
    S:queue(attack.sound_end)
    if attack.animations[3] then 
        an,af = U.animation_name_facing_point(this,attack.animations[3],target.pos)
        U.animation_start(this,an,af,store.tick_ts,1)
        while not U.animation_finished(this) do
            if ( this.health.dead or 
                     ( this.nav_rally and this.nav_rally.new )  )
            then
                goto interrupt
            end
            coroutine.yield()
        end
    end

    ::interrupt::
    S:stop(attack.sound)
    return attack_done
end


local function y_soldier_do_single_melee_attack(store,this,target,attack)
    local attack_done = false
    local start_ts = store.tick_ts

    -- start animation
    local an,af = U.animation_name_facing_point(this,attack.animation,target.pos)
    U.animation_start(this,an,af,store.tick_ts,1)

    S:queue(attack.sound,attack.sound_args)
    
    -- wait for hit time
    while (store.tick_ts - start_ts < attack.hit_time) do
        if ( this.health.dead or 
             this.unit.is_stunned or 
             ( this.dodge and this.dodge.active and not this.dodge.silent ) or 
             ( not attack.ignore_rally_change and this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    S:queue(attack.sound_hit,attack.sound_hit_args)

    -- cooldown
    attack.ts = start_ts
    if attack.shared_cooldown then
        -- shared cooldown resets all ts when one works, so no attacks
        -- are made back to back
        for _,aa in pairs(this.melee.attacks) do
            if aa ~= attack and aa.shared_cooldown then aa.ts = attack.ts end
        end
    end
    if attack.forced_cooldown then
        -- mark when the last attack happened
        this.melee.forced_ts = attack.ts
    end
    if attack.cooldown_group then
        for _,aa in pairs(this.melee.attacks) do
            if aa ~= attack and aa.cooldown_group == attack.cooldown_group then
                aa.ts = attack.ts
            end
        end
    end
            
    if attack.signal then
        signal.emit('soldier-attack',this,attack,attack.signal)
    end
    
    if not unit_dodges(store,target,false,attack,this) and table.contains(target.enemy.blockers, this.id) then
        if attack.damage_type ~= DAMAGE_NONE then 
            -- apply damage only in blockers list
            local d = E:create_entity('damage')
            d.source_id = this.id
            d.target_id = target.id
            d.xp_gain_factor = attack.xp_gain_factor
            d.xp_dest_id = attack.xp_dest_id
            d.track_kills = (this.track_kills ~= nil)
            d.track_damage = attack.track_damage
            d.pop = attack.pop
            d.pop_chance = attack.pop_chance
            d.pop_conds = attack.pop_conds   
            
            if IS_KR5 then
                d.reduce_armor = attack.reduce_armor
                d.reduce_magic_armor = attack.reduce_magic_armor
            end

            if attack.instakill then 
                d.damage_type = DAMAGE_INSTAKILL
            elseif attack.fn_damage then
                d.damage_type = attack.damage_type
                d.value = attack.fn_damage(this,store,attack,target)
            elseif attack.damage_min then
                d.damage_type = attack.damage_type
                d.value = math.ceil(this.unit.damage_factor * math.random(attack.damage_min, attack.damage_max))
            end
            queue_damage(store,d)
        end
        
        -- apply mod if any
        if attack.mod then
            local mod = E:create_entity(attack.mod)
            mod.modifier.ts = store.tick_ts
            mod.modifier.target_id = target.id
            mod.modifier.source_id = this.id
            mod.modifier.level = attack.level
            queue_insert(store,mod)
        end

        -- hit position offset
        local hit_pos = V.vclone(this.pos)
        if attack.hit_offset then
            hit_pos.x = hit_pos.x + ( af and -1 or 1 ) * attack.hit_offset.x
            hit_pos.y = hit_pos.y + attack.hit_offset.y
        end
        
        -- apply aura if any
        if attack.hit_aura then
            local a = E:create_entity(attack.hit_aura)
            a.pos = V.vclone(hit_pos)
            a.aura.target_id = target.id
            a.aura.source_id = this.id
            queue_insert(store,a)
        end

        -- effects / decals
        if attack.hit_fx then
            local fx = E:create_entity(attack.hit_fx)
            fx.pos = V.vclone(hit_pos)
            for i=1,#fx.render.sprites do
                fx.render.sprites[i].ts = store.tick_ts
            end
            queue_insert(store,fx)
        end
        if attack.hit_decal then
            local fx = E:create_entity(attack.hit_decal)
            fx.pos = V.vclone(hit_pos)
            for i=1,#fx.render.sprites do
                fx.render.sprites[i].ts = store.tick_ts
            end
            queue_insert(store,fx)
        end        
    end

    -- pickpocket
    if ( this.pickpocket and
         ( not this.pickpocket.power_name or ( this.powers and this.powers[this.pickpocket.power_name].level > 0 ) ) and
         math.random() < this.pickpocket.chance )
    then
        local pi = this.pickpocket
        if target.enemy and target.enemy.gold_bag > 0 then
            local q = km.clamp(0,target.enemy.gold_bag,
                               math.floor(0.5 + U.frandom(pi.steal_min,pi.steal_max)))
            if q > 0 then
                S:queue(this.pickpocket.sound)
                signal.emit('soldier-pickpocket',this,q)
            end
            target.enemy.gold_bag = target.enemy.gold_bag - q
            store.player_gold = store.player_gold + q 
            if pi.fx then
                local fx = E:create_entity(pi.fx)
                fx.pos.x,fx.pos.y = target.pos.x,target.pos.y
                fx.render.sprites[1].ts = store.tick_ts
                queue_insert(store,fx)
            end
        end
    end

    -- attack done
    attack_done = true
    
    -- end animation
    while not U.animation_finished(this) do
        if ( this.health.dead or 
             this.unit.is_stunned or 
             ( this.dodge and this.dodge.active and not this.dodge.silent ) or 
             ( not attack.ignore_rally_change and this.nav_rally and this.nav_rally.new ) )
        then
            goto interrupt
        end
        coroutine.yield()
    end

    ::interrupt::
    S:stop(attack.sound)
    return attack_done
end

local function soldier_pick_melee_target(store,this)
    local target

    if U.blocker_rank(store,this) ~= nil then
        if not U.is_blocked_valid(store,this) then
            U.unblock_target(store,this)
        else
            target = store.entities[this.soldier.target_id]
        end
    end
    
    local center = this.nav_rally and this.nav_rally.center or this.pos
    if not target then
        if this.hero then
            target = U.find_nearest_enemy(
                store.entities, center,
                0,this.melee.range,F_BLOCK,bit.bor(F_CLIFF),
                function(e)
                    return (
                        ( not e.enemy.max_blockers or #e.enemy.blockers == 0 ) and
                            band(GR:cell_type(e.pos.x,e.pos.y), TERRAIN_NOWALK) == 0 and
                            (not this.melee.fn_can_pick or this.melee.fn_can_pick(this,e))
                    )
                end
            )
        else
            target = U.find_foremost_enemy(
                store.entities, center,
                0,this.melee.range,false,F_BLOCK,bit.bor(F_CLIFF),
                function(e)
                    return (
                        (not e.enemy.max_blockers or #e.enemy.blockers == 0) and
                            band(GR:cell_type(e.pos.x,e.pos.y), TERRAIN_NOWALK) == 0 and
                            (not this.melee.fn_can_pick or this.melee.fn_can_pick(this,e))
                    )
                end
            )
        end
    elseif U.blocker_rank(store,this) ~= 1 then 
        -- not first blocker, so look around for unblocked enemies
        local alt_target = U.find_foremost_enemy(
            store.entities,center,
            0,this.melee.range,false,F_BLOCK,bit.bor(F_FLYING, F_CLIFF),
            function(e) return (
                    #e.enemy.blockers == 0 and
                    band(GR:cell_type(e.pos.x,e.pos.y), TERRAIN_NOWALK) == 0
                )
            end
        )
        if alt_target then target = alt_target end
    end
    
    return target
end

local function soldier_move_to_slot_step(store,this,target)
    -- returns true while on route (or exception if no slot was found)
    U.block_enemy(store,this,target)  -- unblocks previous, if different
    -- move to slot
    local slot_pos,slot_flip,enemy_flip = U.melee_slot_position(this,target)
    if not slot_pos then return true end
    if V.veq(slot_pos,this.pos) then
        this.motion.arrived = true
        return false
    else
        U.set_destination(this, slot_pos)
        local an,af = U.animation_name_facing_point(
            this, 'walk', this.motion.dest)
        U.animation_start(this, an, af, store.tick_ts, -1)
        if U.walk(this,store.tick_length) then
            -- it might arrive but not be yet ready to fire
            -- (cooldown), so idle it so it does not show as
            -- running in the same place.
            local ani = this.melee and this.melee.arrived_slot_animation or 'idle' 
            an,af = U.animation_name_facing_point(this,ani,target.pos)
            U.animation_start(this,an,af,store.tick_ts,-1)
        end
        return true  -- restart the loop to give chance to find another target 
    end
end

local function soldier_pick_melee_attack(store,this,target)
    -- TODO: if two shared cooldown attacks have same cooldown, and
    -- chance, always the first one is picked. Make a pool with all of
    -- them and random one by changing the format of the melee.order
    -- array
    -- TODO: differentiate concepts of shared_cooldown and the
    -- resetting of the cooldown counter.

    if this.dodge and this.dodge.counter_attack_pending then
        -- dodge pending
        this.dodge.counter_attack_pending = false
        this.melee.last_attack = { target_id=target.id, attack=this.dodge.counter_attack }
        return this.dodge.counter_attack
    else
        for _,i in pairs(this.melee.order) do
            local a = this.melee.attacks[i]
            local cooldown = a.cooldown
            if this.melee.cooldown and a.shared_cooldown then
                -- use shared cooldown
                cooldown = this.melee.cooldown
            end
            local forced_cooldown_ok = true
            if this.melee.forced_cooldown and a.forced_cooldown then
                forced_cooldown_ok = store.tick_ts - this.melee.forced_ts > this.melee.forced_cooldown
            end            
            if ( not a.disabled and
                     store.tick_ts - a.ts > cooldown and
                     forced_cooldown_ok and
                     band(a.vis_flags, target.vis.bans) == 0 and
                     band(a.vis_bans, target.vis.flags) == 0 and
                     ( not a.fn_can or a.fn_can(this,store,a,target) ) and
                     ( not a.not_first or ( this.melee.last_attack and this.melee.last_attack.target_id == target.id ) )
            )
            then
                if (not a.fn_chance and math.random() >= a.chance) or (a.fn_chance and not a.fn_chance(this,store,a,target)) then
                    -- to avoid trying the same attack multiple times when chance fails 
                    a.ts = store.tick_ts
                    goto next
                end
                if a.min_count and a.type == 'area' and a.damage_radius then
                    -- check min_count for area attacks
                    local targets = U.find_enemies_in_range(store.entities, this.pos, 0, a.damage_radius, a.vis_flags, a.vis_bans)
                    if not targets or #targets < a.min_count then
                        goto next
                    end
                end
                -- found attack
                this.melee.last_attack = { target_id=target.id, attack=a }
                return a
            end
            ::next::
        end
    end
    return nil
end

local function y_soldier_melee_block_and_attacks(store,this)
    -- returns brk,sta
    --   brk == true: interrupt main loop
    --       - on route to slot, dead, change rally, stun
    --   brk == false: continue main loop
    --       - sta: A_NO_TARGET, A_IN_COOLDOWN, A_DONE
    local target = soldier_pick_melee_target(store,this)
    if not target then 
        return false, A_NO_TARGET
    end
        
    if soldier_move_to_slot_step(store,this,target) then
        -- on route
        return true
    end

    local attack = soldier_pick_melee_attack(store,this,target)
    if not attack then 
        return false, A_IN_COOLDOWN
    end

    -- gain xp (for heroes)
    if attack.xp_from_skill then
        hero_gain_xp_from_skill(this,this.hero.skills[attack.xp_from_skill])
    end
    -- perform attack
    local attack_done
    if attack.loops then  -- both area and melee
        attack_done = y_soldier_do_loopable_melee_attack(store,this,target,attack)
    elseif attack.type == 'area' then
        attack_done = y_soldier_do_single_area_attack(store,this,target,attack)
    else
        attack_done = y_soldier_do_single_melee_attack(store,this,target,attack)                
    end
    -- result
    if attack_done then
        return false, A_DONE
    else
        return true
    end
end

local function soldier_go_back_step(store,this)
    -- returns true if on route
    local dest = this.nav_rally.pos
    if V.veq(this.pos, dest) then
        this.motion.arrived = true
        return false
    else
        U.set_destination(this,dest)
        if U.walk(this,store.tick_length) then
            return false
        else
            local an,af = U.animation_name_facing_point(this, 'walk', this.motion.dest)
            U.animation_start(this, an, af, store.tick_ts, -1)            
            return true
        end
    end
end

local function soldier_idle(store,this,force_ts)
    U.animation_start(this,this.idle_flip.last_animation,nil,store.tick_ts,this.idle_flip.loop,nil,force_ts)

    if this.unit.is_stunned then
        return
    end

    -- reset the counter if more than two ticks passed since the last idle
    if store.tick_ts - this.idle_flip.ts > 2 * store.tick_length then
        this.idle_flip.ts_counter = 0
    end
    this.idle_flip.ts = store.tick_ts
    
    -- accumulate during idle before flipping    
    this.idle_flip.ts_counter = this.idle_flip.ts_counter + store.tick_length
    if this.idle_flip.ts_counter > this.idle_flip.cooldown then
        this.idle_flip.ts_counter = 0
        -- flip
        if math.random() < this.idle_flip.chance then 
            this.render.sprites[1].flip_x = not this.render.sprites[1].flip_x
        end
        -- animation
        if this.idle_flip.animations then
            this.idle_flip.last_animation = table.random(this.idle_flip.animations)
        end
    end
end

local function soldier_regen(store,this)
    if ( this.regen and
         store.tick_ts - this.regen.last_hit_ts > this.regen.last_hit_standoff_time )
    then
        -- accumulate idling time (when idle and not hit for standoff time)
        this.regen.ts_counter = this.regen.ts_counter + store.tick_length
        if this.regen.ts_counter > this.regen.cooldown
        then
            if this.health.hp < this.health.hp_max then 
                this.health.hp = km.clamp(0,this.health.hp_max, this.health.hp + this.regen.health)
                signal.emit('health-regen',this,this.regen.health)
            end
            this.regen.ts_counter = 0
        end
    end

end

local function soldier_power_upgrade(this,power_name)
    -- upgrade one level to the power with the specified name
    local pn = power_name
    local pow = this.powers and this.powers[pn]
    
    -- health
    if this.health.power_name == pn and this.health.hp_inc then
        this.health.hp_max = this.health.hp_max + this.health.hp_inc
        this.health.hp = this.health.hp_max
    end

    -- armor
    if this.health.armor_power_name == pn and this.health.armor_inc then
        armor_inc(this, this.health.armor_inc)
    end

    -- ranged attacks
    if this.ranged then 
        for _,a in pairs(this.ranged.attacks) do
            if a.power_name == pn then
                a.level = a.level + 1
                if a.disabled then a.disabled = nil end
                if a.cooldown_inc then
                    a.cooldown = a.cooldown + a.cooldown_inc
                end
                if a.range_inc then
                    a.max_range = a.max_range + a.range_inc
                end
            end
        end
    end    

    -- melee attacks
    if this.melee then
        local min_cooldown = 1e99
        local cooldown_changed = false
        for _,a in pairs(this.melee.attacks) do
            if a.power_name == pn then
                a.level = a.level + 1
                if a.disabled then a.disabled = nil end
                if a.chance_inc then
                    a.chance = a.chance + a.chance_inc
                    this.melee.order = U.attack_order(this.melee.attacks)  -- re-sort due to chance change
                end
                if a.damage_inc then
                    a.damage_min = a.damage_min + a.damage_inc
                    a.damage_max = a.damage_max + a.damage_inc
                end
                if a.damage_min_inc and a.damage_max_inc then
                    a.damage_min = a.damage_min + a.damage_min_inc
                    a.damage_max = a.damage_max + a.damage_max_inc
                end
                if a.cooldown_inc then
                    a.cooldown = a.cooldown + a.cooldown_inc
                    cooldown_changed = true
                end
            end
            if a.cooldown then min_cooldown = math.min(a.cooldown, min_cooldown) end
        end
        if cooldown_changed and this.melee.forced_cooldown and min_cooldown < this.melee.forced_cooldown then
            this.melee.forced_cooldown = min_cooldown
        end
    end

    -- timed_actions
    if this.timed_actions then
        for _,a in pairs(this.timed_actions.list) do
            if a.power_name == pn then 
                if a.level then a.level = a.level + 1 end
                if a.disabled then a.disabled = nil end
            end
        end
    end
    
    -- revive
    if this.revive and this.revive.power_name == pn then
        this.revive.disabled = nil
        if this.revive.chance_inc then this.revive.chance = this.revive.chance + this.revive.chance_inc end
        if this.revive.health_recover then this.revive.health_recover = this.revive.health_recover + this.revive.health_recover_inc end
    end

    -- dodge
    if this.dodge and this.dodge.power_name == pn then
        local d = this.dodge
        if d.chance_inc then d.chance = d.chance + d.chance_inc end
    end                    
    if this.dodge and this.dodge.counter_attack and this.dodge.counter_attack.power_name == pn then
        local d = this.dodge
        if d.counter_attack.damage_inc then
            d.counter_attack.damage_min = d.counter_attack.damage_min + d.counter_attack.damage_inc
            d.counter_attack.damage_max = d.counter_attack.damage_max + d.counter_attack.damage_inc
        end
    end

    -- pickpocket
    if this.pickpocket and this.pickpocket.power_name == pn then
        local pi = this.pickpocket
        if pi.chance_inc then pi.chance = pi.chance + pi.chance_inc end
    end

    -- custom handler
    local fn = pow and pow.on_power_upgrade
    if fn then
        fn(this,power_name,pow)
    end
    
end

local function soldier_courage_upgrade(store,this)
    local upg = UP:get_upgrade('barrack_courage')
    if ( upg and this.soldier and this.health
             and store.tick_ts - this.soldier.courage_ts > upg.regen_cooldown )
    then
        this.soldier.courage_ts = store.tick_ts
        this.health.hp = km.clamp(0,this.health.hp_max,
                                  km.round(this.health.hp + this.health.hp_max * upg.regen_factor))
    end
end

local function heroes_desperate_effort_upgrade(store,this)
    local upg = UP:get_upgrade('heroes_desperate_effort')
    if (upg and this.hero and this.health) then
        if this.health.hp < this.health.hp_max * upg.health_trigger then
            if not U.has_modifiers(store, this, upg.modifier) then
                local m = E:create_entity(upg.modifier)
                m.modifier.source_id = this.id
                m.modifier.target_id = this.id
                queue_insert(store,m)
            end
        end
    end
end

local function heroes_visual_learning_upgrade(store, this)
    local upg = UP:get_upgrade('heroes_visual_learning')
    if (upg and this.hero) then

        if not this._upgrade_data then this._upgrade_data = {} end
        if not this._upgrade_data.heroes_visual_learning then this._upgrade_data.heroes_visual_learning = {} end
        if not this._upgrade_data.heroes_visual_learning.other_hero then
            local other_hero = table.filter(store.entities, function (k,e)
                return e.hero and e.template_name ~= this.template_name
            end)
            if other_hero and #other_hero > 0 then
                other_hero = other_hero[1]
            end
            this._upgrade_data.heroes_visual_learning.other_hero = other_hero
        end

        if not this._upgrade_data.heroes_visual_learning.ts or (store.tick_ts - this._upgrade_data.heroes_visual_learning.ts >= upg.check_cooldown) then
            local other_hero = this._upgrade_data.heroes_visual_learning.other_hero
            if V.dist(other_hero.pos.x,other_hero.pos.y, this.pos.x,this.pos.y) < upg.distance_to_trigger then
                local m = E:create_entity(upg.modifier)
                m.modifier.source_id = this.id
                m.modifier.target_id = this.id
                queue_insert(store,m)
            end
            this._upgrade_data.heroes_visual_learning.ts = store.tick_ts
        end
    end
end

local function heroes_lone_wolves_upgrade(store, this)
    local upg = UP:get_upgrade('heroes_lone_wolves')
    if (upg and this.hero) then

        if not this._upgrade_data then this._upgrade_data = {} end
        if not this._upgrade_data.heroes_lone_wolves then this._upgrade_data.heroes_lone_wolves = {} end
        if not this._upgrade_data.heroes_lone_wolves.other_hero then
            local other_hero = table.filter(store.entities, function (k,e)
                return e.hero and e.template_name ~= this.template_name
            end)
            if other_hero and #other_hero > 0 then
                other_hero = other_hero[1]
            end
            this._upgrade_data.heroes_lone_wolves.other_hero = other_hero
        end

        if not this._upgrade_data.heroes_lone_wolves.ts or (store.tick_ts - this._upgrade_data.heroes_lone_wolves.ts >= upg.check_cooldown) then
            local other_hero = this._upgrade_data.heroes_lone_wolves.other_hero
            if V.dist(other_hero.pos.x,other_hero.pos.y, this.pos.x,this.pos.y) > upg.distance_to_trigger then
                if not U.has_modifiers(store, this, upg.modifier) then
                    local m = E:create_entity(upg.modifier)
                    m.modifier.source_id = this.id
                    m.modifier.target_id = this.id
                    queue_insert(store,m)
                end
            end
            this._upgrade_data.heroes_lone_wolves.ts = store.tick_ts
        end
    end
end

local function alliance_merciless_upgrade(store,this)
    local upg = UP:get_upgrade('alliance_merciless')
    if (upg and this.hero) then

        if not this._upgrade_data then this._upgrade_data = {} end
        if not this._upgrade_data.alliance_merciless then this._upgrade_data.alliance_merciless = {} end

        if not this._upgrade_data.alliance_merciless.ts or (store.tick_ts - this._upgrade_data.alliance_merciless.ts >= upg.check_cooldown) then
            local towers_dark_army = table.filter(store.entities, function (k, e)
                return e.tower and not e.tower_holder and e.tower.team == TEAM_DARK_ARMY
            end)

            if not this._merciless_factor then this._merciless_factor = 0.0 end

            local merciless_factor = #towers_dark_army * upg.damage_factor_per_tower
            local merciless_dif = merciless_factor - this._merciless_factor

            this.unit.damage_factor = this.unit.damage_factor + merciless_dif
            this._merciless_factor = merciless_factor

            this._upgrade_data.alliance_merciless.ts = store.tick_ts
        end
    end
end

local function alliance_corageous_upgrade(store,this)
    local upg = UP:get_upgrade('alliance_corageous_stand')
    if (upg and this.hero and this.health) then

        if not this._upgrade_data then this._upgrade_data = {} end
        if not this._upgrade_data.alliance_corageous_stand then this._upgrade_data.alliance_corageous_stand = {} end

        if not this._upgrade_data.alliance_corageous_stand.ts or (store.tick_ts - this._upgrade_data.alliance_corageous_stand.ts >= upg.check_cooldown) then
            local towers_linirea = table.filter(store.entities, function (k, e)
                return e.tower and not e.tower_holder and e.tower.team == TEAM_LINIREA
            end)

            if not this._base_hp_max then this._base_hp_max = this.health.hp_max end

            local corageous_hp_factor = 1 + #towers_linirea * upg.hp_factor_per_tower

            local old_hp_max = this.health.hp_max
            this.health.hp_max = this._base_hp_max * corageous_hp_factor

            if old_hp_max < this.health.hp_max then
                this.health.hp = this.health.hp + (this.health.hp_max - old_hp_max)
            end
            this.health.hp = km.clamp(0, this.health.hp_max, this.health.hp)

            this._upgrade_data.alliance_corageous_stand.ts = store.tick_ts
        end
    end
end

------------------------------------------------------------
-- enemy helper functions

local function can_melee_blocker(store,this,blocker)
    -- WARNING: return false for dead blocker, which can lead to an infinite loop.
    -- so *always* should go through a yield on false. 
    return (
        not this.health.dead and
        not this.unit.is_stunned and 
        blocker and
        not blocker.health.dead and 
        table.contains(this.enemy.blockers, blocker.id) and
        store.entities[blocker.id]
    )
end

local function can_range_soldier(store,this,soldier)
    for _,ar in pairs(this.ranged.attacks) do
        if ( ( ar.hold_advance or store.tick_ts - ar.ts > ar.cooldown ) and
             not this.health.dead and 
             not this.unit.is_stunned and 
             not soldier.health.dead and
             store.entities[soldier.id] and
             band(soldier.vis.bans, ar.vis_flags) == 0 and
             band(soldier.vis.flags, ar.vis_bans) == 0 and
             U.is_inside_ellipse(soldier.pos, this.pos, ar.max_range) and
             ( ar.min_range == 0 or not U.is_inside_ellipse(soldier.pos, this.pos, ar.min_range) ) )
        then
            return true
        end
    end
    return false
end

local function enemy_interrupted(this)
    return (this.health.dead or this.unit.is_stunned)
end

local function y_enemy_wait(store,this,time)
    return U.y_wait(store,time,
                    function(store,time) return enemy_interrupted(this) end
    )
end

local function y_enemy_animation_wait(this)
    -- returns true if interrupted
    while not U.animation_finished(this) do
        if enemy_interrupted(this) then return true end
        coroutine.yield()
    end
    return false
end

local function enemy_water_change(store,this)
    local terrain_type = band(GR:cell_type(this.pos.x,this.pos.y), TERRAIN_TYPES_MASK)
    local w = this.water 
    
    if terrain_type ~= w.last_terrain_type then
        log.paranoid('terrain changed from %s to %s', w.last_terrain_type, terrain_type)

        -- ignore change
        if w.ignore_pi == this.nav_path.pi then
            log.debug('Enemy %s ignored path %s for water change', this.id, w.ignore_pi)
            w.last_terrain_type = terrain_type
            return
        end
        
        if w.last_terrain_type and bor(w.last_terrain_type, terrain_type) == bor(TERRAIN_WATER,TERRAIN_LAND) then
            -- splash if changing land/water
            local fx = E:create_entity(w.splash_fx)
            fx.render.sprites[1].name = fx.render.sprites[1].size_names[this.unit.size]
            fx.render.sprites[1].ts = store.tick_ts
            fx.pos = V.vclone(this.pos)
            queue_insert(store,fx)

            if this.sound_events and this.sound_events.water_splash then
                S:queue(this.sound_events.water_splash)
            end
        end
                    
        if terrain_type == TERRAIN_WATER then
            -- water
            this.vis.flags = bor(this.vis.flags, F_WATER)
            w._pushed_bans = U.push_bans(this.vis, this.water.vis_bans)
            this.motion.max_speed = this.motion.max_speed * this.water.speed_factor

            -- health bar offset/hidden
            if this.health_bar then 
                if this.water.health_bar_offset then
                    this.health_bar._orig_offset = this.health_bar.offset
                    this.health_bar.offset = this.water.health_bar_offset
                end
                if this.water.health_bar_hidden then
                    this.health_bar.hidden = true
                end
            end

            -- remove mods
            if this.water.remove_modifiers then
                remove_modifiers(store,this)
            end
            if this.water.remove_modifier_templates then
                for _,n in pairs(this.water.remove_modifier_templates) do
                    remove_modifiers(store,this,n)
                end
            end
            
            -- hit/mod offsets
            if this.water.hit_offset then 
                this.unit._orig_hit_offset = this.unit.hit_offset
                this.unit.hit_offset = this.water.hit_offset
            end
            if this.water.mod_offset then 
                this.unit._orig_mod_offset = this.unit.mod_offset
                this.unit.mod_offset = this.water.mod_offset
            end

            -- blood/explode
            this.unit._orig_can_explode = this.unit.can_explode
            this.unit._orig_show_blood_pool = this.unit.show_blood_pool
            this.unit.can_explode= false
            this.unit.show_blood_pool = false
            
            -- sprite prefix suffix
            for i=1,#this.render.sprites do
                local s = this.render.sprites[i]
                if not string.match(s.prefix, this.water.sprite_suffix .. '$') then
                    s.prefix = s.prefix .. this.water.sprite_suffix
                end
                s._orig_angles_flip_vertical = s.angles_flip_vertical
                s.angles_flip_vertical = this.water.angles_flip_vertical
            end
            if w.hide_sprites_range then
                local r = w.hide_sprites_range
                local f,t = r and r[1],r and r[2]
                U.sprites_hide(this,f,t,true)
            end
            
            -- TODO: debo limpiar los blockers? el caso seria
            -- que se meta en el agua ya con bloquers, pero no
            -- se como se puede dar.
            
        elseif w.last_terrain_type == TERRAIN_WATER and terrain_type == TERRAIN_LAND then
            -- land (restore values)
            this.vis.flags = band(this.vis.flags, bnot(F_WATER))
            if w._pushed_bans then
                U.pop_bans(this.vis, w._pushed_bans)
                w._pushed_bans = nil
            end
            this.motion.max_speed = this.motion.max_speed / this.water.speed_factor
            if this.water.health_bar_offset then 
                this.health_bar.offset = this.health_bar._orig_offset
            end
            if this.water.health_bar_hidden then
                this.health_bar.hidden = false
            end
            if this.water.hit_offset then 
                this.unit.hit_offset = this.unit._orig_hit_offset
            end
            if this.water.mod_offset then 
                this.unit.mod_offset = this.unit._orig_mod_offset
            end
            this.unit.can_explode = this.unit._orig_can_explode
            this.unit.show_blood_pool = this.unit._orig_show_blood_pool

            for i=1,#this.render.sprites do
                local s = this.render.sprites[i]
                if string.match(s.prefix, this.water.sprite_suffix .. '$') then
                    s.prefix = string.gsub(s.prefix, this.water.sprite_suffix .. '$', '')
                end
                s.angles_flip_vertical = s._orig_angles_flip_vertical
            end
            if w.hide_sprites_range then
                local r = w.hide_sprites_range
                local f,t = r and r[1],r and r[2]
                U.sprites_show(this,f,t,true)
            end

        end
        
        w.last_terrain_type = terrain_type
    end
    return terrain_type
end

local function enemy_cliff_change(store,this)
    local terrain_type = band(GR:cell_type(this.pos.x,this.pos.y), TERRAIN_TYPES_MASK)
    local c = this.cliff

    if terrain_type ~= c.last_terrain_type then
        if terrain_type == TERRAIN_CLIFF then
            local next_pos = P:next_entity_node(this,store.tick_length)
            if next_pos and next_pos.y < this.pos.y then
                -- descending, find first non cliff cell down in y axis
                local i,j = GR:get_coords(this.pos.x,this.pos.y)
                while j > 1 and bit.band(GR:get_cell(i,j),TERRAIN_CLIFF) ~= 0 do
                    j = j-1
                end
                c.fall_to_pos = V.v(GR:cell_pos(i,j))
            else
                -- climbing, store this pos to fall to 
                c.fall_to_pos = V.v(this.pos.x,this.pos.y)
            end

            this.vis.flags = bor(this.vis.flags, F_CLIFF)
            c._pushed_bans = U.push_bans(this.vis, c.vis_bans)
            this.motion.max_speed = this.motion.max_speed * c.speed_factor
            this.health.dead_lifetime = 3   -- falling animation + death visible

            -- sprite prefix suffix
            for i=1,#this.render.sprites do                
                local s = this.render.sprites[i]
                if c.hide_sprite_ids and table.contains(c.hide_sprite_ids, i) then
                    s.hidden = true
                else                
                    s.z = Z_BACKGROUND_BETWEEN
                    if not string.match(s.prefix, c.sprite_suffix .. '$') then
                        s.prefix = s.prefix .. c.sprite_suffix
                    end
                end
            end
            -- for corner dot to work, draw enemy first and then the health bars
            this.health_bar.z = Z_BACKGROUND_BETWEEN+1  

        elseif c.last_terrain_type == TERRAIN_CLIFF and terrain_type == TERRAIN_LAND then
            this.vis.flags = band(this.vis.flags, bnot(F_CLIFF))
            if c._pushed_bans then
                U.pop_bans(this.vis, c._pushed_bans)
                c._pushed_bans = nil
            end
            this.motion.max_speed = this.motion.max_speed / c.speed_factor
            this.health.dead_lifetime = 2

            -- sprite
            for i=1,#this.render.sprites do                
                local s = this.render.sprites[i]
                if c.hide_sprite_ids and table.contains(c.hide_sprite_ids, i) then
                    s.hidden = false
                else
                    s.z = Z_OBJECTS
                    if string.match(s.prefix, c.sprite_suffix .. '$') then
                        s.prefix = string.gsub(s.prefix, c.sprite_suffix .. '$', '')
                    end
                end
            end
            this.health_bar.z = Z_OBJECTS

        end
        c.last_terrain_type = terrain_type
    end

    return terrain_type
end

---

local function y_enemy_death(store,this)
    -- NOTE: a possible refactoring is separating the concepts of
    -- damage type (to calculate the hp) and the death effect
    -- (explosion, eat, etc.) in case of death
    
    local function show_blood_pool(e,terrain_type)
        if ( e.unit.show_blood_pool and
             e.unit.blood_color ~= BLOOD_NONE and
             band(terrain_type, TERRAIN_WATER) == 0 )
        then
            -- blood pool
            local decal = E:create_entity('decal_blood_pool')
            decal.pos = V.vclone(e.pos)
            decal.render.sprites[1].ts = store.tick_ts
            decal.render.sprites[1].name = e.unit.blood_color
            decal.render.sprites[1].z = e.render.sprites[1].z
            decal.render.sprites[1].sort_y_offset = 1
            queue_insert(store,decal)                                
        end
    end

    local can_spawn = this.death_spawns and band(this.health.last_damage_types,
                                                 bor(DAMAGE_EAT,
                                                     DAMAGE_NO_SPAWNS,
                                                     (this.death_spawns.no_spawn_damage_types or 0))) == 0
    
    -- death spawns concurrent
    if can_spawn and this.death_spawns.concurrent_with_death then 
        do_death_spawns(store,this)
        coroutine.yield()  -- insert before hiding
        can_spawn = false
    end
    
    local terrain_type = band(GR:cell_type(this.pos.x,this.pos.y), TERRAIN_TYPES_MASK)

    if ( band(this.health.last_damage_types,
              bor(DAMAGE_EXPLOSION,DAMAGE_INSTAKILL,DAMAGE_FX_EXPLODE) ) ~= 0 and
         band(this.health.last_damage_types,
              bor(DAMAGE_FX_NOT_EXPLODE, DAMAGE_DISINTEGRATE)) == 0 and
         this.unit.can_explode and this.unit.explode_fx and 
         band(terrain_type, TERRAIN_WATER) == 0 )
    then 
        -- explode
        S:queue(this.sound_events.death_by_explosion)
        local fx = E:create_entity(this.unit.explode_fx)
        fx.pos = V.vclone(this.pos)
        fx.render.sprites[1].ts = store.tick_ts
        fx.render.sprites[1].name = fx.render.sprites[1].size_names[this.unit.size]
        queue_insert(store,fx)                

        show_blood_pool(this,terrain_type)
        -- hide but dont remove it, so necromancer can see it
        this.unit.hide_during_death = true
        
    elseif band(this.health.last_damage_types, bor(DAMAGE_DISINTEGRATE)) ~= 0 and
        this.unit.can_disintegrate and this.unit.disintegrate_fx
    then
        -- disintegrate
        local fx = E:create_entity(this.unit.disintegrate_fx)
        fx.pos.x,fx.pos.y = this.pos.x,this.pos.y
        fx.render.sprites[1].ts = store.tick_ts
        if fx.render.sprites[1].size_names then 
            fx.render.sprites[1].name = fx.render.sprites[1].size_names[this.unit.size]
        end
        if band(this.vis.flags, F_FLYING) ~= 0 and this.unit.hit_offset then
            fx.render.sprites[1].offset.y = this.unit.hit_offset.y
        end
        queue_insert(store,fx)
        this.unit.hide_during_death = true
        this.unit.show_blood_pool = false
        
    elseif band(this.health.last_damage_types, bor(DAMAGE_EAT)) ~= 0 then
        this.unit.hide_during_death = true
        this.unit.show_blood_pool = false

    elseif band(terrain_type, TERRAIN_CLIFF) ~= 0 and band(this.vis.flags, F_FLYING) == 0 then
        -- fall
        if IS_TRILOGY then
            S:queue('WilhemScream')
        end
        S:queue(this.sound_events.death, this.sound_events.death_args)
        U.animation_start(this,'fall',nil,store.tick_ts,true)
        U.set_destination(this, this.cliff.fall_to_pos)
        while not U.walk(this,store.tick_length,this.cliff.fall_accel) do
            coroutine.yield()
        end
        -- die
        show_blood_pool(this,terrain_type)
        U.y_animation_play(this,'death',nil,store.tick_ts,1)

    elseif band(terrain_type, TERRAIN_WATER) ~= 0 and band(this.vis.flags, F_FLYING) == 0 then
        S:queue(this.sound_events.death_water, this.sound_events.death_water_args)
        U.y_animation_play(this,this.unit.death_animation,nil,store.tick_ts,1)

    elseif this.unit.death_animation then
        S:queue(this.sound_events.death, this.sound_events.death_args)
        show_blood_pool(this,terrain_type)
        local an,af = this.unit.death_animation,nil
        if this.heading and this.heading.angle then
            -- looks toward the x component of the speed
            an,af = U.animation_name_for_angle(this,this.unit.death_animation,this.heading.angle)
        end
        U.animation_start(this,an,af,store.tick_ts,false)
        if can_spawn and this.death_spawns.delay then
            -- delayed death spawns
            U.y_wait(store, this.death_spawns.delay)
            do_death_spawns(store,this)
            can_spawn = false
        end
        while not U.animation_finished(this) do
            coroutine.yield()
        end
    end

    this.health.death_finished_ts = store.tick_ts

    -- death spawns after (delayed will fall here if not already executed)
    if can_spawn then 
        do_death_spawns(store,this)
        coroutine.yield()  -- insert before hiding
        can_spawn = false
    end

    -- hide
    if this.unit.hide_during_death or this.unit.hide_after_death then
        U.sprites_hide(this, nil, nil, true)
    end

    -- ui
    if this.ui then
        this.ui.can_click = not (this.unit.hide_after_death or this.unit.hide_during_death)
        this.ui.z = -1
    end

    -- fade
    if this.unit.fade_time_after_death then
        fade_out_entity(store, this, this.unit.fade_time_after_death, this.unit.fade_duration_after_death, true)
    end
end

local function y_enemy_walk_step(store,this,animation_name,sprite_id)
    -- return: true - could walk step
    --         false - ran out of nodes or reached the forced_waypoint
    
    if not animation_name then animation_name = 'walk' end

    local next,new = nil,nil
    local use_path 
    if this.motion.forced_waypoint then
        local w = this.motion.forced_waypoint
        next = w
        if V.dist(w.x,w.y, this.pos.x,this.pos.y) < 2 * this.motion.max_speed * store.tick_length then
            this.pos.x,this.pos.y = w.x,w.y
            this.motion.forced_waypoint = nil
            return false
        end
    else
        use_path = true  -- TODO: review this, because it can force an incorrect animation angle when too far from the path.
        next,new = P:next_entity_node(this,store.tick_length)
        if not next then
            log.debug("enemy %s ran out of nodes to walk", this.id)
            coroutine.yield()   -- let systems process it
            return false
        end        
    end
    U.set_destination(this,next)
    local an, af = U.animation_name_facing_point(this,animation_name,this.motion.dest,sprite_id,nil,use_path)
    if this.sound_events and new then
        S:queue(this.sound_events.new_node, this.sound_events.new_node_args)
    end
    U.animation_start(this,an,af,store.tick_ts,true,sprite_id)
    U.walk(this,store.tick_length)
    coroutine.yield()
    this.motion.speed.x,this.motion.speed.y = 0,0
    return true
end

local function y_enemy_walk_until_blocked(store,this,ignore_soldiers,func)
    -- walk until blocked, dead, change terrain, or func evals true
    -- returns:  ok,blocker,ranged
    --   ok: true means we can continue with the enemy loop normally
    --       false means enemy is dead or there are no more nodes in the path, so break loop
    local ranged = nil
    local blocker = nil
    local terrain_type = band(GR:cell_type(this.pos.x,this.pos.y), bor(TERRAIN_WATER, TERRAIN_LAND))
    while ignore_soldiers or ( not blocker and not ranged ) do
        -- stunned
        if this.unit.is_stunned then
            return false
        end

        -- custom break function 
        if func and func(store,this) then
            return false,nil,nil
        end
        
        -- dead? 
        if this.health.dead then
            return false
        end

        -- ignore soliders in invalid nodes (mostly for ranged attacks)
        local node_valid = P:is_node_valid(this.nav_path.pi,this.nav_path.ni)
        
        -- find ranged attacks
        if node_valid and not ignore_soldiers and this.ranged then
            --local ranged = nil
            for _,a in pairs(this.ranged.attacks) do
                if ( not a.disabled and
                     (not a.requires_magic or this.enemy.can_do_magic) and
                     (a.hold_advance or store.tick_ts - a.ts > a.cooldown) )
                then 
                    ranged = U.find_nearest_soldier(
                        store.entities, this.pos, a.min_range, a.max_range, a.vis_flags, a.vis_bans
                    )
                    if ranged ~= nil then break end
                end
            end
        end
        -- find blockers
        if node_valid and not ignore_soldiers and #this.enemy.blockers > 0 then
            -- NOTE: returns dead blockers to allow for revive to happen while still blocked
            U.cleanup_blockers(store,this)
            blocker = store.entities[this.enemy.blockers[1]]
        end

        -- walk one step
        if ignore_soldiers or ( not blocker and not ranged ) then 
            y_enemy_walk_step(store,this)
        else
            U.animation_start(this,'idle',nil,store.tick_ts,true)
        end

        -- exit when cell type changed, to allow for other things
        if terrain_type ~= band(GR:cell_type(this.pos.x,this.pos.y), bor(TERRAIN_WATER, TERRAIN_LAND)) then
            return false,nil,nil
        end

    end
    return true,blocker,ranged
end

local function y_wait_for_blocker(store,this,blocker)
    -- face blocker destination or its position if arrived already
    local pos = blocker.motion.arrived and blocker.pos or blocker.motion.dest
    local an,af = U.animation_name_facing_point(this,'idle',pos)
    U.animation_start(this, an, af, store.tick_ts, true) -- loop on idle

    -- wait for him to arrive
    while ( not blocker.motion.arrived ) do
        coroutine.yield()
        if ( this.health.dead or
             this.unit.is_stunned or
             not table.contains(this.enemy.blockers, blocker.id) or
             blocker.health.dead or
             not store.entities[blocker.id])
        then
            return false  -- it was removed or this died
        end
        if blocker.unit.is_stunned then
            -- soldier stunned on route unblocks enemy
            U.unblock_target(store,blocker)
            return false
        end
    end
    return true
end

local function y_enemy_do_ranged_attack(store,this,target,attack)
    -- face enemy and start shooting
    local an,af,ai = U.animation_name_facing_point(this,attack.animation,target.pos)        
    U.animation_start(this,an,af,store.tick_ts,false)
    
    -- wait for shoot time
    while (store.tick_ts - attack.ts < attack.shoot_time) do
        if this.health.dead or (this.unit.is_stunned and not attack.ignore_stun) then
            return false
        end
        coroutine.yield()
    end

    -- vis check again for teleporters
    if ( band(target.vis.bans, attack.vis_flags) == 0 and
         band(target.vis.flags, attack.vis_bans) == 0 )
    then                
        -- launch bullet
        local bullet = E:create_entity(attack.bullet)
        bullet.pos = V.vclone(this.pos)
        if attack.bullet_start_offset then
            local offset  = attack.bullet_start_offset[ai]
            bullet.pos.x,bullet.pos.y = bullet.pos.x + ( af and -1 or 1 ) * offset.x, bullet.pos.y + offset.y
        end
        bullet.bullet.from = V.vclone(bullet.pos)
        bullet.bullet.to = V.vclone(target.pos)
        if not attack.ignore_hit_offset then
            bullet.bullet.to.x = bullet.bullet.to.x + target.unit.hit_offset.x
            bullet.bullet.to.y = bullet.bullet.to.y + target.unit.hit_offset.y
        end
        bullet.bullet.target_id = target.id
        bullet.bullet.source_id = this.id
        if attack.damage_factor then
            bullet.bullet.damage_factor = attack.damage_factor
        end
        queue_insert(store,bullet)

    end
    
    -- wait animation
    while not U.animation_finished(this) do
        if this.health.dead or (this.unit.is_stunned and not attack.ignore_stun) then
            return false
        end
        coroutine.yield()
    end
    U.animation_start(this,'idle',nil,store.tick_ts,true)
    
    return true  -- attack completed    
end

local function y_enemy_do_loopable_ranged_attack(store,this,target,attack)
    local attack_done = false
    local b
    
    -- pre animation
    S:queue(attack.sound)
    local an,af,ai = U.animation_name_facing_point(this,attack.animations[1],target.pos)
    U.y_animation_play(this,an,af,store.tick_ts,1)

    -- loop animation
    for i=1,attack.loops do
        an,af,ai = U.animation_name_facing_point(this,attack.animations[2],target.pos)
        U.animation_start(this,an,af,store.tick_ts,false)

        local shoot_times = attack.shoot_times or {attack.shoot_time}
        for si,st in pairs(shoot_times) do
            -- shoot time
            while store.tick_ts - this.render.sprites[1].ts < st do
                if this.unit.is_stunned and not attack.ignore_stun then goto finish end
                if this.health.dead then goto interrupt end
                coroutine.yield()
            end
            
            -- fire
            b = E:create_entity(attack.bullet)
            b.pos = V.vclone(this.pos)
            if attack.bullet_start_offset then
                local offset = attack.bullet_start_offset[ai]
                b.pos.x,b.pos.y = b.pos.x + ( af and -1 or 1 ) * offset.x, b.pos.y + offset.y
            end
            b.bullet.from = V.vclone(b.pos)
            b.bullet.to = V.v(target.pos.x + target.unit.hit_offset.x,target.pos.y + target.unit.hit_offset.y)
            b.bullet.target_id = target.id
            b.bullet.source_id = this.id
            if attack.damage_factor then
                b.bullet.damage_factor = attack.damage_factor
            end
            queue_insert(store, b)
            
            -- at least one shot went through
            attack_done = true
        end
        
        while not U.animation_finished(this) do
            if this.unit.is_stunned and not attack.ignore_stun then goto finish end
            if this.health.dead then goto interrupt end
            coroutine.yield()
        end
    end

    ::finish::
    an,af,ai = U.animation_name_facing_point(this,attack.animations[3],target.pos)
    U.animation_start(this,an,af,store.tick_ts,1)

    while not U.animation_finished(this) do
        if this.health.dead then goto interrupt end
        coroutine.yield()
    end

    ::interrupt::
    return attack_done    
end

local function y_enemy_range_attacks(store,this,target)
    for _,i in ipairs(this.ranged.order) do
        local ar = this.ranged.attacks[i]
        -- shared cooldown
        local cooldown = ar.cooldown
        if this.ranged.cooldown and ar.shared_cooldown then
            cooldown = this.ranged.cooldown
        end

        if ( not ar.disabled and
             store.tick_ts - ar.ts >= cooldown and
             band(ar.vis_flags, target.vis.bans) == 0 and
             band(ar.vis_bans, target.vis.flags) == 0 and
             ( not ar.sync_animation or this.render.sprites[1].sync_flag ) )
        then
            -- to avoid trying multiple times when chance fails, set the ts now
            ar.ts = store.tick_ts
            if math.random() >= ar.chance then
                goto skip_attack
            end

            -- one attack went through for shared cooldowns
            for _,aa in pairs(this.ranged.attacks) do
                if aa ~= ar and aa.shared_cooldown then aa.ts = ar.ts end
            end

            local attack_done
            if ar.loops then
                attack_done = y_enemy_do_loopable_ranged_attack(store,this,target,ar)
            else
                attack_done = y_enemy_do_ranged_attack(store,this,target,ar)
            end

            return attack_done                
        end
        ::skip_attack::
    end
    return true
end

local function y_enemy_melee_attacks(store,this,target)
    -- return: true - everything is ok, the attack went ok
    --         false - this died, so interrupt the loop 
    for _,i in ipairs(this.melee.order) do
        local ma = this.melee.attacks[i]

        local cooldown = ma.cooldown
        if ma.shared_cooldown then
            cooldown = this.melee.cooldown
        end
        
        if ( not ma.disabled and 
             store.tick_ts - ma.ts >= cooldown and
             band(ma.vis_flags, target.vis.bans) == 0 and
             band(ma.vis_bans, target.vis.flags) == 0 and
             ( not ma.fn_can or ma.fn_can(this,store,ma,target) )
         )
        then
            -- to avoid trying multiple times when chance fails, set the ts now
            ma.ts = store.tick_ts
            
            if math.random() >= ma.chance then
                goto skip_attack
            end

            log.paranoid('attack %i selected for entity %s', i, this.template_name)
            
            -- one attack went through for shared cooldowns
            for _,aa in pairs(this.melee.attacks) do
                if aa ~= ma and aa.shared_cooldown then aa.ts = ma.ts end
            end

            -- start attack animation
            ma.ts = store.tick_ts
            S:queue(ma.sound,ma.sound_args)
            local an,af = U.animation_name_facing_point(this, ma.animation, target.pos)
            for i=1,#this.render.sprites do
                if this.render.sprites[i].animated then 
                    U.animation_start(this, an, af, store.tick_ts, 1, i)
                end
            end
            
            -- hit position offset
            local hit_pos = V.vclone(this.pos)
            if ma.hit_offset then
                hit_pos.x = hit_pos.x + ( af and -1 or 1 ) * ma.hit_offset.x
                hit_pos.y = hit_pos.y + ma.hit_offset.y
            end
            
            -- multi hit
            local hit_times = ma.hit_times and ma.hit_times or { ma.hit_time }
            for i=1,#hit_times do
                local hit_time = hit_times[i]

                -- wait for dodge
                local dodged = false
                if ma.dodge_time and target.dodge then
                    local dodge_time = ma.dodge_time
                    if target.dodge and target.dodge.time_before_hit then
                        dodge_time = hit_time - target.dodge.time_before_hit
                    end
                    while (store.tick_ts - ma.ts < dodge_time) do
                        if (this.health.dead or
                                (this.unit.is_stunned and not ma.ignore_stun) or
                                (this.dodge and this.dodge.active and not this.dodge.silent) )
                        then
                            return false
                        end
                        coroutine.yield()
                    end
                    dodged = unit_dodges(store,target,false,ma,this)
                end
                
                -- wait for hit
                while (store.tick_ts - ma.ts < hit_time) do
                    if ( this.health.dead or
                             (this.unit.is_stunned and not ma.ignore_stun) or
                             (this.dodge and this.dodge.active and not this.dodge.silent ) )
                    then
                        return false
                    end
                    coroutine.yield()
                end

                -- sound
                S:queue(ma.sound_hit,ma.sound_hit_args)

                -- damage
                if ma.type == 'melee' and not dodged and table.contains(this.enemy.blockers, target.id)
                then
                    -- melee / apply damage only if still blocked                    
                    local d = E:create_entity('damage')
                    d.source_id = this.id
                    d.target_id = target.id
                    d.track_kills = (this.track_kills ~= nil)
                    d.track_damage = ma.track_damage
                    d.pop = ma.pop
                    d.pop_chance = ma.pop_chance
                    d.pop_conds = ma.pop_conds                       

                    if ma.instakill then
                        d.damage_type = DAMAGE_INSTAKILL
                        queue_damage(store, d)
                        
                    elseif ma.damage_min then
                        d.damage_type = ma.damage_type
                        local hit_factor = ma.hit_damage_factor and ma.hit_damage_factor[i] or 1
                        d.value = math.ceil(this.unit.damage_factor * hit_factor * math.random(ma.damage_min, ma.damage_max))
                        queue_damage(store, d)
                    end

                    -- apply mod if any
                    if ma.mod then
                        local mod = E:create_entity(ma.mod)
                        mod.modifier.target_id = target.id
                        mod.modifier.source_id = this.id
                        queue_insert(store,mod)
                    end

                elseif ma.type == 'area' then
                    -- area
                    local targets = table.filter(
                        store.entities,
                        function(_,e)
                            return (
                                e.soldier and e.vis and
                                    e.health and not e.health.dead and
                                    band(e.vis.flags, ma.vis_bans) == 0 and
                                    band(e.vis.bans, ma.vis_flags) == 0 and
                                    U.is_inside_ellipse(e.pos, hit_pos, ma.damage_radius) and
                                    ( not ma.fn_filter or ma.fn_filter(this,store,ma,e) )
                            )
                        end
                    )
                    for i,e in ipairs(targets) do
                        if e == target and dodged then goto next end
                        if ma.count and i > ma.count then break end
                        local d = E:create_entity('damage')
                        d.source_id = this.id
                        d.target_id = e.id
                        d.damage_type = ma.damage_type
                        d.value = math.ceil( this.unit.damage_factor * math.random(ma.damage_min, ma.damage_max) )
                        d.pop = ma.pop
                        d.pop_chance = ma.pop_chance
                        d.pop_conds = ma.pop_conds
                        queue_damage(store,d)
                        -- apply mod if any
                        if ma.mod then
                            local mod = E:create_entity(ma.mod)
                            mod.modifier.target_id = e.id
                            mod.modifier.source_id = this.id
                            queue_insert(store,mod)
                        end
                        ::next::
                    end
                end
                            
                -- effects / decals
                if ma.hit_fx and (not ma.hit_fx_once or i == 1) then
                    local fx = E:create_entity(ma.hit_fx)
                    fx.pos = V.vclone(hit_pos)
                    if ma.hit_fx_offset then
                        fx.pos.x = fx.pos.x + (af and -1 or 1) * ma.hit_fx_offset.x
                        fx.pos.y = fx.pos.y + ma.hit_fx_offset.y
                    end
                    for i=1,#fx.render.sprites do
                        if ma.hit_fx_flip then
                            fx.render.sprites[i].flip_x = af 
                        end
                        fx.render.sprites[i].ts = store.tick_ts
                    end
                    queue_insert(store,fx)
                end
                if ma.hit_decal then
                    local fx = E:create_entity(ma.hit_decal)
                    fx.pos = V.vclone(hit_pos)
                    for i=1,#fx.render.sprites do
                        fx.render.sprites[i].ts = store.tick_ts
                    end
                    queue_insert(store,fx)
                end
            end
            
            -- finish animation
            while not U.animation_finished(this) do
                if ( this.health.dead or
                         (ma.ignore_stun and this.unit.is_stunned )or
                         (this.dodge and this.dodge.active and not this.dodge.silent ) )
                then
                    return false
                end
                coroutine.yield()
            end
            U.animation_start(this, 'idle', nil, store.tick_ts, true)
            return true -- attack completed, so break loop, without interrupting 
        end
        ::skip_attack::
    end
    return true
end

local function y_enemy_stun(store,this)
    local flip_x = this.motion and (this.motion.dest.x < this.pos.x) or nil
    U.animation_start(this, 'idle', flip_x, store.tick_ts, true)
    coroutine.yield()
end

local function y_enemy_mixed_walk_melee_ranged(store, this, ignore_soldiers, walk_break_fn, melee_break_fn, ranged_break_fn)
    -- returns:
    --   - true: flow was not interrupted
    --   - false: flow was interrupted

    -- fallback for attack breaks
    ranged_break_fn = ranged_break_fn or melee_break_fn
    
    local cont,blocker,ranged = y_enemy_walk_until_blocked(store,this,ignore_soldiers,walk_break_fn)
    if not cont then return false end
    
    if blocker then
        -- fight blocker
        if not y_wait_for_blocker(store,this,blocker) then return false end
        while can_melee_blocker(store,this,blocker) and (not melee_break_fn or not melee_break_fn(store,this)) do
            if not y_enemy_melee_attacks(store,this,blocker) then return false end
            coroutine.yield()
        end

    elseif ranged then
        -- attack ranged (breaking if blocked)
        while ( can_range_soldier(store,this,ranged)
                    and #this.enemy.blockers == 0
                    and (not ranged_break_fn or not ranged_break_fn(store,this)) )
        do
            if not y_enemy_range_attacks(store,this,ranged) then return false end
            coroutine.yield()
        end
    end

    return true
end

------------------------------------------------------------

local function y_show_taunt_set(store,taunts,set_name,index,pos,duration,wait,decal)
    local set = taunts.sets[set_name]
    index = index or (set.idxs and table.random(set.idxs)) or math.random(set.start_idx,set.end_idx)    
    duration = duration or taunts.duration
    pos = pos or set.pos or taunts.pos
    local offset = set.offset or taunts.offset or v(0,0)
    local t = E:create_entity(decal or set.decal_name or taunts.decal_name)
    t.texts.list[1].text = _(string.format(set.format,index))
    t.pos.x,t.pos.y = pos.x + offset.x, pos.y + offset.y
    t.tween.ts = store.tick_ts
    t.duration = duration
    t.start_ts = store.tick_ts
    queue_insert(store,t)
    if wait then 
        U.y_wait(store,duration)
    end
    return t
end

-- create and insert spawner spawns
local function y_spawner_spawn(store,this)
    -- returns: interrupt,{entities}
    local sp = this.spawner
    sp._last_subpath = sp._last_subpath or math.random(1,3)
    local spawns = {} 
    for i=1,sp.count do
        if sp.interrupt then return true,spawns end
        -- spawn
        local spawn = E:create_entity(sp.entity)
        spawn.nav_path.pi = sp.pi
        if sp.random_subpath then 
            spawn.nav_path.spi = sp.allowed_subpaths[math.random(1,#sp.allowed_subpaths)]
        else
            sp._last_subpath = km.zmod(sp._last_subpath + 1, #sp.allowed_subpaths)
            spawn.nav_path.spi = sp.allowed_subpaths[sp._last_subpath]
        end
        if sp.forced_waypoint_offset then 
            spawn.motion.forced_waypoint = V.v(this.pos.x + sp.forced_waypoint_offset.x,
                                               this.pos.y + sp.forced_waypoint_offset.y)
        elseif sp.forced_waypoint then
            spawn.motion.forced_waypoint = V.v(sp.forced_waypoint.x, sp.forced_waypoint.y)
        end
        if spawn.motion.forced_waypoint then
            local fw = spawn.motion.forced_waypoint
            local pis = P:get_connected_paths(sp.pi)
            local nodes = P:nearest_nodes(fw.x,fw.y, pis, {spawn.nav_path.spi}, true)
            if #nodes < 1 then
                log.error('(%s) could not find point to spawn near %s,%s',this.id,fw.x,fw.y)
            else
                spawn.nav_path.pi = nodes[1][1]
                spawn.nav_path.ni = nodes[1][3] + sp.node_offset
            end
        else
            spawn.nav_path.ni = sp.ni + sp.node_offset
            spawn.motion.forced_waypoint = P:node_pos(spawn.nav_path)  -- to calculate proper angle
        end
        spawn.pos.x,spawn.pos.y = this.pos.x + sp.pos_offset.x, this.pos.y + sp.pos_offset.y
        spawn.render.sprites[1].name = sp.initial_spawn_animation
        spawn.unit.spawner_id = this.id
        if sp.patch_props then
            spawn = table.deepmerge(spawn, sp.patch_props)
        end
        queue_insert(store,spawn)
        table.insert(spawns,spawn)

        local spawn_ts = store.tick_ts
        while store.tick_ts - spawn_ts < sp.cycle_time do
            if sp.interrupt then return true,spawns end
            coroutine.yield()
        end                
    end
    return false,spawns
end

------------------------------------------------------------

local function deck_shuffle(deck)
    deck.trigger_list = table.random_order(deck.trigger_list)
    local trigger_indexes = {}
    for i = #deck.trigger_list - deck.trigger_cards + 1, #deck.trigger_list, 1 do
        table.insert(trigger_indexes, deck.trigger_list[i])
    end
    deck.trigger_indexes = trigger_indexes
end

local function deck_new(trigger_cards, total_cards, dont_shuffle)
    if trigger_cards >= total_cards then
        log.error("Deck cant have more or equal trigger cards than total cards")
        return nil
    end

    local trigger_list = {}
    for i = 1, total_cards, 1 do
        table.insert(trigger_list, i)
    end

    local deck = {
        trigger_cards = trigger_cards,
        total_cards = total_cards,
        trigger_indexes = {},
        trigger_list = trigger_list,
        index = 1,
        dont_shuffle = false
    }

    if dont_shuffle then 
        deck.dont_shuffle = true
        for i = #deck.trigger_list - deck.trigger_cards + 1, #deck.trigger_list, 1 do
            table.insert(deck.trigger_indexes, deck.trigger_list[i])
        end
        return deck 
    end

    deck_shuffle(deck)

    return deck
end

local function deck_draw(deck)
    local is_trigger = false
    if deck.index and deck.trigger_indexes and table.contains(deck.trigger_indexes, deck.index) then
        is_trigger = true
    end
    deck.index = km.zmod(deck.index+1,deck.total_cards)

    if deck.index == 1 and not deck.dont_shuffle then
        deck_shuffle(deck)
    end

    return is_trigger
end

local function towers_keen_accuracy_upgrade(store, this, damaging_obj)
    local upg = UP:get_upgrade('towers_keen_accuracy')
    if (upg and this.tower) then
        if not this._keen_accuracy_deck then
            this._keen_accuracy_deck = deck_new(upg.trigger_cards, upg.total_cards)
        end

        local is_trigger = deck_draw(this._keen_accuracy_deck)

        if is_trigger then
            if damaging_obj.bullet then
                damaging_obj.bullet.pop = {'pop_crit'}
                damaging_obj.bullet.pop_conds = DR_DAMAGE
                damaging_obj.bullet.damage_min = damaging_obj.bullet.damage_min * upg.damage_factor
                damaging_obj.bullet.damage_max = damaging_obj.bullet.damage_max * upg.damage_factor
            elseif damaging_obj.template_name == 'damage' then
                damaging_obj.value = damaging_obj.value * upg.damage_factor
            end
        end
    end
end

local function towers_swaped(store, this, attacks)
    if this.tower_upgrade_persistent_data.swaped then
        for _, a in pairs(attacks) do
            a.ts = store.tick_ts
        end
        if this.powers then
            for _,pow in pairs(this.powers) do
                if pow.level > 0 and pow.show_rally then
                    this.tower.show_rally = true
                end
            end
        end
        this.tower_upgrade_persistent_data.swaped = nil
    end
end

------------------------------------------------------------

local SU = {
    has_modifiers = U.has_modifiers,
    ui_click_proxy_add = ui_click_proxy_add,
    ui_click_proxy_remove = ui_click_proxy_remove,
    remove_modifiers = remove_modifiers,
    remove_modifiers_by_type = remove_modifiers_by_type,
    remove_auras = remove_auras,
    hide_modifiers = hide_modifiers,
    show_modifiers = show_modifiers,
    hide_auras = hide_auras,
    show_auras = show_auras,
    unit_dodges = unit_dodges,
    stun_inc = stun_inc,
    stun_dec = stun_dec,
    armor_inc = armor_inc,
    armor_dec = armor_dec,    
    magic_armor_inc = magic_armor_inc,
    magic_armor_dec = magic_armor_dec,
    spiked_armor_inc = spiked_armor_inc,
    spiked_armor_dec = spiked_armor_dec,    
    tower_block_inc = tower_block_inc,
    tower_block_dec = tower_block_dec,
    tower_update_silenced_powers = tower_update_silenced_powers,
    do_death_spawns = do_death_spawns,
    delay_attack = delay_attack,
    insert_sprite = insert_sprite,
    fade_out_entity = fade_out_entity, 
    create_pop = create_pop,
    create_bullet_pop = create_bullet_pop,
    create_bullet_damage = create_bullet_damage,
    create_attack_damage = create_attack_damage,
    initial_parabola_speed = initial_parabola_speed,
    position_in_parabola = position_in_parabola,
    parabola_y = parabola_y,
    y_hero_wait = y_soldier_wait,
    y_soldier_wait = y_soldier_wait,
    y_hero_animation_wait = y_soldier_animation_wait,
    y_soldier_animation_wait = y_soldier_animation_wait,
    hero_interrupted = soldier_interrupted,
    soldier_interrupted = soldier_interrupted,
    hero_will_teleport = hero_will_teleport,
    y_hero_walk_waypoints = y_hero_walk_waypoints,
    y_hero_new_rally = y_hero_new_rally,
    hero_gain_xp_from_skill = hero_gain_xp_from_skill,
    hero_gain_xp = hero_gain_xp,
    hero_level_up = hero_level_up,
    y_hero_death_and_respawn = y_hero_death_and_respawn,
    y_hero_death_and_respawn_kr5 = y_hero_death_and_respawn_kr5,
    y_reinforcement_fade_in = y_reinforcement_fade_in,
    y_reinforcement_fade_out = y_reinforcement_fade_out,
    y_soldier_new_rally = y_soldier_new_rally,
    y_soldier_revive = y_soldier_revive,
    y_soldier_death = y_soldier_death,
    y_soldier_do_loopable_ranged_attack = y_soldier_do_loopable_ranged_attack,
    y_soldier_do_ranged_attack = y_soldier_do_ranged_attack,
    soldier_pick_ranged_target_and_attack = soldier_pick_ranged_target_and_attack,
    y_soldier_ranged_attacks = y_soldier_ranged_attacks,
    y_soldier_do_timed_action = y_soldier_do_timed_action,
    y_soldier_timed_actions = y_soldier_timed_actions,
    y_soldier_do_timed_attack = y_soldier_do_timed_attack,
    y_soldier_timed_attacks = y_soldier_timed_attacks,
    y_soldier_do_single_area_attack = y_soldier_do_single_area_attack,
    y_soldier_do_loopable_melee_attack = y_soldier_do_loopable_melee_attack,
    y_soldier_do_single_melee_attack = y_soldier_do_single_melee_attack,
    soldier_pick_melee_target = soldier_pick_melee_target,
    soldier_move_to_slot_step = soldier_move_to_slot_step,
    soldier_pick_melee_attack = soldier_pick_melee_attack,
    y_soldier_melee_block_and_attacks = y_soldier_melee_block_and_attacks,
    soldier_go_back_step = soldier_go_back_step,
    soldier_idle = soldier_idle,
    soldier_regen = soldier_regen,
    soldier_power_upgrade = soldier_power_upgrade,
    soldier_courage_upgrade = soldier_courage_upgrade,
    can_melee_blocker = can_melee_blocker,
    can_range_soldier = can_range_soldier,
    enemy_interrupted = enemy_interrupted,
    y_enemy_wait = y_enemy_wait,
    y_enemy_animation_wait = y_enemy_animation_wait, 
    enemy_water_change = enemy_water_change,
    enemy_cliff_change = enemy_cliff_change,
    y_enemy_death = y_enemy_death,
    y_enemy_walk_step = y_enemy_walk_step,
    y_enemy_walk_until_blocked = y_enemy_walk_until_blocked,
    y_wait_for_blocker = y_wait_for_blocker,
    y_enemy_do_ranged_attack = y_enemy_do_ranged_attack,
    y_enemy_do_loopable_ranged_attack = y_enemy_do_loopable_ranged_attack,    
    y_enemy_range_attacks = y_enemy_range_attacks,
    y_enemy_melee_attacks = y_enemy_melee_attacks,
    y_enemy_stun = y_enemy_stun, 
    y_enemy_mixed_walk_melee_ranged = y_enemy_mixed_walk_melee_ranged,
    y_show_taunt_set = y_show_taunt_set,
    y_spawner_spawn = y_spawner_spawn,
    hero_will_transfer = hero_will_transfer,
    hero_will_teleport = hero_will_teleport,
    heroes_desperate_effort_upgrade = heroes_desperate_effort_upgrade,
    heroes_visual_learning_upgrade = heroes_visual_learning_upgrade,
    heroes_lone_wolves_upgrade = heroes_lone_wolves_upgrade,
    alliance_merciless_upgrade = alliance_merciless_upgrade,
    alliance_corageous_upgrade = alliance_corageous_upgrade,
    towers_keen_accuracy_upgrade = towers_keen_accuracy_upgrade,
    towers_swaped = towers_swaped,
    deck_new = deck_new,
    deck_draw = deck_draw,
}
return SU

