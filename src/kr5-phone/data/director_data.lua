local d = {}

d.item_props = {
    splash  = { type='screen', src='screen_splash',    next='china_advise' },
    china_advise = { type='screen', src='screen_china_advise',    next='consent',  skip_check='check_skip_china_advise'},
    consent = { type='screen', src='screen_consent',   next='slots',   skip_check='check_skip_consent'},
    slots   = { type='screen', src='screen_slots',                     show_loading=true },
    credits = { type='screen', src='screen_credits',   next='map',     show_loading=true},
    map     = { type='screen', src='screen_map',                       show_loading=true },
    game    = { type='game',                           next='map',     show_loading=true },
    kr5_end = { type='screen', src='screen_kr5_end',   next='map' },
    tutorial_end = { type='screen', src='screen_tutorial_end',   next='map' },
    boss_fight_1_end = {type='screen', src='screen_boss_fight_1_end', next='map'},
    boss_fight_2_end = {type='screen', src='screen_boss_fight_2_end', next='map'},
    boss_fight_3_end = {type='screen', src='screen_boss_fight_3_end', next='map'},
    boss_fight_5_end = {type='screen', src='screen_boss_fight_5_end', next='map'},
    boss_fight_6_end = {type='screen', src='screen_boss_fight_6_end', next='map'},
    boss_fight_7_end = {type='screen', src='screen_boss_fight_7_end', next='map'},
    boss_fight_8_end = {type='screen', src='screen_boss_fight_8_end', next='map'},
    boss_fight_9_end = {type='screen', src='screen_boss_fight_9_end', next='map'},

    -- debug
    comic   = { type='comic',                          next='map',     show_loading=false },
    game_editor = { type='screen', src='game_editor',  show_loading=false, scissor=false },
    tester = { type='screen', src='screen_tester', show_loading=false },
}    

d.loading_image_name = {
    -- image name,  levels array
    { 'loading_01_2', {1,2,3,4,5,6}},    
    { 'loading_02_1', {7,8,9,10,11}},
    { 'loading_03_1', {12,13,14,15,16}},
    { 'loading_04_1', {17,18,19}},
    { 'loading_05_1', {20,21,22}},
    { 'loading_06_1', {23,24,25,26,27}},
    { 'loading_07_1', {28,29,30}},
    { 'loading_08_1', {31,32,33,34,35}},
    ['default'] = 'loading_00_1', 
}

return d
