--
-- KR3 - GUI data / Notifications
--

local V = require 'klua.vector'
local v = V.v
local i18n = require 'i18n'
local function CJK(default,zh,ja,kr) return i18n.cjk(i18n,default,zh,ja,kr) end
local base_offset =0
if KR_PLATFORM =='ios' then --take into account that in ios the hud is a little bit lower to ignore home bar
    base_offset = 6
end
local ring_scale = 0.52
local tower_offset =v(135,135)
return {
    notifications = {
        -- by template name
        enemy_hog_invader             = { template='enemy', image='encyclopedia_creeps_0001', icon='notification_enemies_0001', },
        enemy_tusked_brawler          = { template='enemy', image='encyclopedia_creeps_0002', icon='notification_enemies_0002', },
        enemy_turtle_shaman           = { template='enemy', image='encyclopedia_creeps_0005', icon='notification_enemies_0005', },
        enemy_bear_vanguard           = { template='enemy', image='encyclopedia_creeps_0003', icon='notification_enemies_0004', },
        enemy_cutthroat_rat           = { template='enemy', image='encyclopedia_creeps_0004', icon='notification_enemies_0007', },
        enemy_dreadeye_viper          = { template='enemy', image='encyclopedia_creeps_0007', icon='notification_enemies_0006', },
        enemy_surveyor_harpy          = { template='enemy', image='encyclopedia_creeps_0006', icon='notification_enemies_0010', },
        enemy_skunk_bombardier        = { template='enemy', image='encyclopedia_creeps_0009', icon='notification_enemies_0008', },
        enemy_hyena5                  = { template='enemy', image='encyclopedia_creeps_0008', icon='notification_enemies_0003', },
        enemy_rhino                   = { template='enemy', image='encyclopedia_creeps_0010', icon='notification_enemies_0009', },
        boss_pig                      = { template='enemy', image='encyclopedia_creeps_0011' },
        enemy_acolyte                 = { template='enemy', image='encyclopedia_creeps_0012', icon='notification_enemies_0011', },
        enemy_acolyte_tentacle        = { template='enemy', image='encyclopedia_creeps_0013' },
        enemy_lesser_sister           = { template='enemy', image='encyclopedia_creeps_0014', icon='notification_enemies_0012', },
        enemy_lesser_sister_nightmare = { template='enemy', image='encyclopedia_creeps_0015', icon='notification_enemies_0013', },
        enemy_blinker                 = { template='enemy', image='encyclopedia_creeps_0016', icon='notification_enemies_0014', },
        enemy_unblinded_priest        = { template='enemy', image='encyclopedia_creeps_0017', icon='notification_enemies_0015', },
        enemy_unblinded_abomination   = { template='enemy', image='encyclopedia_creeps_0018', icon='notification_enemies_0016', },
        enemy_unblinded_abomination_stage_8 = { template='enemy', image='encyclopedia_creeps_0050'},
        enemy_unblinded_shackler      = { template='enemy', image='encyclopedia_creeps_0019', icon='notification_enemies_0017', },
        enemy_spiderling              = { template='enemy', image='encyclopedia_creeps_0023', icon='notification_enemies_0020', },
        enemy_armored_nightmare       = { template='enemy', image='encyclopedia_creeps_0022', icon='notification_enemies_0021', },
        enemy_corrupted_stalker       = { template='enemy', image='encyclopedia_creeps_0020', icon='notification_enemies_0018', },
        enemy_small_stalker           = { template='enemy', image='encyclopedia_creeps_0024', icon='notification_enemies_0029', },
        enemy_crystal_golem           = { template='enemy', image='encyclopedia_creeps_0021', icon='notification_enemies_0019', },
        boss_corrupted_denas          = { template='enemy', image='encyclopedia_creeps_0026' },
        enemy_glareling               = { template='enemy', image='encyclopedia_creeps_0025', icon='notification_enemies_0022', },
        enemy_mindless_husk           = { template='enemy', image='encyclopedia_creeps_0027', icon='notification_enemies_0023', },
        enemy_vile_spawner            = { template='enemy', image='encyclopedia_creeps_0028', icon='notification_enemies_0024', },
        enemy_lesser_eye              = { template='enemy', image='encyclopedia_creeps_0031', icon='notification_enemies_0025', },
        enemy_hardened_horror         = { template='enemy', image='encyclopedia_creeps_0029', icon='notification_enemies_0026', },
        enemy_noxious_horror          = { template='enemy', image='encyclopedia_creeps_0030', icon='notification_enemies_0027', },
        enemy_evolving_scourge        = { template='enemy', image='encyclopedia_creeps_0032', icon='notification_enemies_0028', },
        enemy_amalgam                 = { template='enemy', image='encyclopedia_creeps_0033', icon='notification_enemies_0030', },
        enemy_stage_11_cult_leader_illusion = { template='enemy', image='encyclopedia_creeps_0035' },
        boss_cult_leader              = { template='enemy', image='encyclopedia_creeps_0036' },
        enemy_tower_ray_sheep         = { template='enemy', image='encyclopedia_creeps_0037' },
        enemy_tower_ray_sheep_flying  = { template='enemy', image='encyclopedia_creeps_0037' },
        controller_stage_16_overseer  = { template='enemy', image='encyclopedia_creeps_0038' },
        enemy_bear_woodcutter         = { template='enemy', image='encyclopedia_creeps_0039' },
        enemy_corrupted_elf           = { template='enemy', image='encyclopedia_creeps_0040', icon='notification_enemies_0031', },
        enemy_specter                 = { template='enemy', image='encyclopedia_creeps_0041', icon='notification_enemies_0032', },
        enemy_dust_cryptid            = { template='enemy', image='encyclopedia_creeps_0042', icon='notification_enemies_0034', },
        enemy_bane_wolf               = { template='enemy', image='encyclopedia_creeps_0043', icon='notification_enemies_0033', },
        enemy_deathwood               = { template='enemy', image='encyclopedia_creeps_0044', icon='notification_enemies_0035', },
        enemy_animated_armor          = { template='enemy', image='encyclopedia_creeps_0045', icon='notification_enemies_0037', },
        enemy_revenant_soulcaller     = { template='enemy', image='encyclopedia_creeps_0046', icon='notification_enemies_0036', },
        enemy_pumpkin_witch           = { template='enemy', image='encyclopedia_creeps_0047'},
        enemy_pumpkin_witch_flying    = { template='enemy', image='encyclopedia_creeps_0047'},
        enemy_revenant_harvester      = { template='enemy', image='encyclopedia_creeps_0048', icon='notification_enemies_0038', },
        boss_navira                   = { template='enemy', image='encyclopedia_creeps_0049' },
        enemy_crocs_basic             = { template='enemy', image='encyclopedia_creeps_0054', icon='notification_enemies_0041', },
        enemy_crocs_basic_egg         = { template='enemy', image='encyclopedia_creeps_0053', icon='notification_enemies_0040', },
        enemy_quickfeet_gator         = { template='enemy', image='encyclopedia_creeps_0056', icon='notification_enemies_0043', },
        enemy_quickfeet_gator_chicken_leg   = { template='enemy', image='encyclopedia_creeps_0056' },
        enemy_killertile              = { template='enemy', image='encyclopedia_creeps_0055', icon='notification_enemies_0042', },
        enemy_crocs_flier             = { template='enemy', image='encyclopedia_creeps_0057', icon='notification_enemies_0047', },
        enemy_crocs_ranged            = { template='enemy', image='encyclopedia_creeps_0058', icon='notification_enemies_0044', },
        enemy_crocs_shaman            = { template='enemy', image='encyclopedia_creeps_0062', icon='notification_enemies_0045', },
        enemy_crocs_tank              = { template='enemy', image='encyclopedia_creeps_0059', icon='notification_enemies_0046', },
        enemy_crocs_egg_spawner       = { template='enemy', image='encyclopedia_creeps_0060', icon='notification_enemies_0039', },
        enemy_crocs_hydra             = { template='enemy', image='encyclopedia_creeps_0061', icon='notification_enemies_0048', },
        boss_crocs_lvl1               = { template='enemy', image='encyclopedia_creeps_0063' },
        boss_crocs_lvl2               = { template='enemy', image='encyclopedia_creeps_0063' },
        boss_crocs_lvl3               = { template='enemy', image='encyclopedia_creeps_0063' },
        boss_crocs_lvl4               = { template='enemy', image='encyclopedia_creeps_0063' },
        boss_crocs_lvl5               = { template='enemy', image='encyclopedia_creeps_0063' },
        enemy_darksteel_hammerer      = { template='enemy', image='encyclopedia_creeps_0064', icon='notification_enemies_0055', },
        enemy_darksteel_shielder      = { template='enemy', image='encyclopedia_creeps_0065', icon='notification_enemies_0056', },
        enemy_surveillance_sentry     = { template='enemy', image='encyclopedia_creeps_0068', icon='notification_enemies_0059', },
        enemy_rolling_sentry          = { template='enemy', image='encyclopedia_creeps_0067', icon='notification_enemies_0058', },
        enemy_mad_tinkerer            = { template='enemy', image='encyclopedia_creeps_0072', icon='notification_enemies_0062', },
        enemy_scrap_drone             = { template='enemy', image='encyclopedia_creeps_0073', icon='notification_enemies_0063', },
        enemy_brute_welder            = { template='enemy', image='encyclopedia_creeps_0069', icon='notification_enemies_0060', },
        enemy_scrap_speedster         = { template='enemy', image='encyclopedia_creeps_0066', icon='notification_enemies_0057', },
        enemy_darksteel_fist          = { template='enemy', image='encyclopedia_creeps_0071', icon='notification_enemies_0061', },
        enemy_common_clone            = { template='enemy', image='encyclopedia_creeps_0075', icon='notification_enemies_0054', },
        enemy_darksteel_guardian      = { template='enemy', image='encyclopedia_creeps_0070', icon='notification_enemies_0066', },
        enemy_darksteel_anvil         = { template='enemy', image='encyclopedia_creeps_0074', icon='notification_enemies_0064', },
        enemy_darksteel_hulk          = { template='enemy', image='encyclopedia_creeps_0076', icon='notification_enemies_0065', },
        enemy_machinist               = { template='enemy', image='encyclopedia_creeps_0077', icon='notification_enemies_0067', },
        boss_machinist                = { template='enemy', image='encyclopedia_creeps_0078' },
        enemy_deformed_grymbeard_clone= { template='enemy', image='encyclopedia_creeps_0079', icon='notification_enemies_0068', },
        boss_grymbeard                = { template='enemy', image='encyclopedia_creeps_0080' },
        enemy_spider_priest           = { template='enemy', image='encyclopedia_creeps_0083', icon='notification_enemies_0071', },
        enemy_ballooning_spider       = { template='enemy', image='encyclopedia_creeps_0082', icon='notification_enemies_0070', },
        enemy_ballooning_spider_flyer = { template='enemy', image='encyclopedia_creeps_0082', },
        enemy_spider_sister           = { template='enemy', image='encyclopedia_creeps_0084', icon='notification_enemies_0072', },
        enemy_glarenwarden            = { template='enemy', image='encyclopedia_creeps_0085', icon='notification_enemies_0074', },
        enemy_cultbrood               = { template='enemy', image='encyclopedia_creeps_0086', icon='notification_enemies_0075', },
        enemy_drainbrood              = { template='enemy', image='encyclopedia_creeps_0087', icon='notification_enemies_0076', },
        enemy_spidead                 = { template='enemy', image='encyclopedia_creeps_0089', icon='notification_enemies_0077', },
        boss_spider_queen             = { template='enemy', image='encyclopedia_creeps_0088' },
        enemy_fire_phoenix            = { template='enemy', image='encyclopedia_creeps_0090', icon='notification_enemies_0078', },
        enemy_blaze_raider            = { template='enemy', image='encyclopedia_creeps_0092', icon='notification_enemies_0080', },
        enemy_flame_guard             = { template='enemy', image='encyclopedia_creeps_0093', icon='notification_enemies_0081', },
        enemy_fire_fox                = { template='enemy', image='encyclopedia_creeps_0091', icon='notification_enemies_0079', },
        enemy_nine_tailed_fox         = { template='enemy', image='encyclopedia_creeps_0097', icon='notification_enemies_0085', },
        enemy_burning_treant          = { template='enemy', image='encyclopedia_creeps_0095', icon='notification_enemies_0083', },
        enemy_wuxian                  = { template='enemy', image='encyclopedia_creeps_0094', icon='notification_enemies_0082', },
        enemy_ash_spirit              = { template='enemy', image='encyclopedia_creeps_0096', icon='notification_enemies_0084', },
        boss_redboy_teen              = { template='enemy', image='encyclopedia_creeps_0098' },
        enemy_water_spirit            = { template='enemy', image='encyclopedia_creeps_0099', icon='notification_enemies_0086', },
        enemy_water_spirit_spawnless  = { template='enemy', image='encyclopedia_creeps_0099', },
        enemy_storm_spirit            = { template='enemy', image='encyclopedia_creeps_0100', icon='notification_enemies_0087', },
        enemy_qiongqi                 = { template='enemy', image='encyclopedia_creeps_0101', icon='notification_enemies_0088', },
        enemy_gale_warrior            = { template='enemy', image='encyclopedia_creeps_0102', icon='notification_enemies_0089', },
        enemy_water_sorceress         = { template='enemy', image='encyclopedia_creeps_0103', icon='notification_enemies_0090', },
        enemy_storm_elemental         = { template='enemy', image='encyclopedia_creeps_0104', icon='notification_enemies_0091', },
        enemy_citizen                 = { template='enemy', image='encyclopedia_creeps_0105', },
        enemy_citizen_1               = { template='enemy', image='encyclopedia_creeps_0105',  icon='notification_enemies_0092',},
        enemy_citizen_2               = { template='enemy', image='encyclopedia_creeps_0105', },
        enemy_citizen_3               = { template='enemy', image='encyclopedia_creeps_0105', },
        enemy_citizen_4               = { template='enemy', image='encyclopedia_creeps_0105', },
        enemy_fan_guard               = { template='enemy', image='encyclopedia_creeps_0106', icon='notification_enemies_0094', },
        enemy_palace_guard            = { template='enemy', image='encyclopedia_creeps_0107', icon='notification_enemies_0093', },
        boss_princess_iron_fan        = { template='enemy', image='encyclopedia_creeps_0108' },


        TIP_ARMOR               = { template='armored_enemies',         icon='alert_tip_0002', always=true, ach_id='ART_OF_WAR', ach_flag=2^0},
        TIP_RALLY               = { template='rally_point',             icon='alert_tip_0001', always=true, ach_id='ART_OF_WAR', ach_flag=2^1},
        TIP_ARMOR_MAGIC         = { template='magic_resistant_enemies', icon='alert_tip_0003', always=true, ach_id='ART_OF_WAR', ach_flag=2^2},
        TIP_BOTTOM_INFO         = { template='bottom_info',             icon='alert_tip_0004', always=true, ach_id='ART_OF_WAR', ach_flag=2^3},
        TIP_GLARE               = { template='glare',                   icon='alert_tip_0005', always=true, ach_id='ART_OF_WAR', ach_flag=2^4},
        -- TIP_STRATEGY            = { template='tip_strategy',    tall=true, icon='alert_tip_0004', always=true, ach_id='ART_OF_WAR', ach_flag=2^3},
        -- TIP_HEROES              = { template='tip_heroes',      tall=true, icon='alert_tip_0006', always=true},
        -- TIP_UPGRADES            = { template='tip_upgrades',    tall=true, icon='alert_tip_0005', always=false},
        -- TIP_SURVIVAL            = { template='tip_survival',    tall=true,                        always=false},
        -- TODO: tip elite
        
        -- custom names
        -- TOWER_ARCHER_ARCANE            = { template='tower_1', image='encyclopedia_towers_0017', prefix='TOWER_ARCANE',          sub='TOWER_ARCHERS_SUBTITLE',      always=true, seen={'tower_arcane'}},
        -- TOWER_BARRACK_BLADE            = { template='tower_1', image='encyclopedia_towers_0020', prefix='TOWER_BARRACKS_BLADE',  sub='TOWER_BARRACKS_SUBTITLE',     always=true, seen={'tower_blade'}},
        -- TOWER_MAGE_WILD_MAGUS          = { template='tower_1', image='encyclopedia_towers_0016', prefix='TOWER_MAGE_WILD_MAGUS', sub='TOWER_MAGES_SUBTITLE',        always=true, seen={'tower_wild_magus'}},
        -- TOWER_ROCK_THROWER_STONE_DRUID = { template='tower_1', image='encyclopedia_towers_0013', prefix='TOWER_STONE_DRUID',     sub='TOWER_ROCK_THROWER_SUBTITLE', always=true, seen={'tower_druid'}},
        -- TOWER_MAGE_HIGH_ELVEN          = { template='tower_1', image='encyclopedia_towers_0015', prefix='TOWER_MAGE_HIGH_ELVEN', sub='TOWER_MAGES_SUBTITLE',        always=true, seen={'tower_high_elven'}},
        -- TOWER_ROCK_THROWER_ENTWOOD     = { template='tower_1', image='encyclopedia_towers_0014', prefix='TOWER_ENTWOOD',         sub='TOWER_ROCK_THROWER_SUBTITLE', always=true, seen={'tower_entwood'}},

        -- TOWER_ARCHER_SILVER            = { template='tower_2', tall=true, images={'encyclopedia_towers_0018','encyclopedia_towers_0019'}, prefixes={'TOWER_SILVER','TOWER_FOREST_KEEPERS'}, subs={'TOWER_ARCHERS_SUBTITLE', 'TOWER_BARRACKS_SUBTITLE'}, always=true, seen={'tower_silver','tower_forest'} },

        -- TOWER_LEVEL2            = { template='tower_4', level=2, images={'encyclopedia_towers_0006', 'encyclopedia_towers_0005', 'encyclopedia_towers_0007', 'encyclopedia_towers_0008'}, always=true, seen={'tower_barrack_2','tower_archer_2','tower_mage_2','tower_rock_thrower_2'} },
        -- TOWER_LEVEL3            = { template='tower_4', level=3, images={'encyclopedia_towers_0010', 'encyclopedia_towers_0009', 'encyclopedia_towers_0011', 'encyclopedia_towers_0012'}, always=true, seen={'tower_barrack_3','tower_archer_3','tower_mage_3','tower_rock_thrower_3'} },
        -- TOWER_LEVEL4            = { template='tower_4', level=4, images={'encyclopedia_towers_0010', 'encyclopedia_towers_0009', 'encyclopedia_towers_0011', 'encyclopedia_towers_0012'}, always=true, seen={'tower_barrack_3','tower_archer_3','tower_mage_3','tower_rock_thrower_3'} },
        
        -- PLANT_MAGIC_BLOSSOM     = { template='power_special', icon='alert_tip_0007', image='tutorial_magicBlosom',    prefix='PLANT_1', },
        -- PLANT_VENOM             = { template='power_special', icon='alert_tip_0008', image='tutorial_venomPlant',     prefix='PLANT_2', },
        -- ARCANE_CRYSTAL          = { template='power_special', icon='alert_tip_0009', image='tutorial_arcaneCrystal',  prefix='PLANT_3', },
        -- PARALYZING_TREE         = { template='power_special', icon='alert_tip_0010', image='tutorial_paralyzingTree', prefix='PLANT_4', },
        
        --POWER_THUNDERBOLT       = { template='power', image='tutorial_powers_polaroids_0002', prefix='POWER_THUNDER', always=true, signals={{'unlock-user-power',1}} },
        -- POWER_REINFORCEMENT     = { template='power', image='tutorial_powers_polaroids_0001', prefix='POWER_SUMMON',  always=true, signals={{'unlock-user-power',1}} },
        POWER_REINFORCEMENT     = { template='specials', image='tutorial_powers_polaroids_0001', prefix='POWER_SUMMON',  always=true, signals={{'unlock-user-power',1}} },
        -- POWER_HERO              = { template='power', image='tutorial_powers_polaroids_0003', prefix='POWER_HERO',    always=true, signals={{'unlock-user-power',2}} },
        
        -- tutorials
        --TUTORIAL_1              = { template='tutorial_1', tall=true, next='TUTORIAL_2', always=true},
        --TUTORIAL_2              = { template='tutorial_2', tall=true, next='TUTORIAL_3', always=true},
        --TUTORIAL_3              = { template='tutorial_3', tall=true, always=true},
        TUTORIAL_HERO           = { template='hero', always=true},
    },   

    text_balloons = {
        TB_BUILD  = {size=v(200,58), padding=v(100.0, 8.0), hide_cond='tap_twice',      origin='world',                        offset=v(645,610),    text='INGAME_BALLOON_TAP_TWICE_BUILD',prefix='tutorial_text_background',          flags='yellow_text centered', scale_world=true },
        -- TB_BUILD2 = {size=v(200,58), padding=v(60.0, 8.0),  hide_cond='tower_built',    origin='world',                        offset=v(860,522+50), text='INGAME_BALLOON_BUILD_HERE', prefix='tutorial_text_background',              flags='yellow_text',          scale_world=true },
        TB_POWER1 = {size=v(250,58), hide_cond='power_selected_1',                      origin='id:power_button_1:top-center', offset=v(0,-10), text='INGAME_BALLOON_NEW_POWER',                                                       flags='callout-bottom-add_as_child',  scale_world=false },
        TB_POWER2 = {size=v(250,58), hide_cond='power_selected_2',                      origin='id:power_button_2:top-center', offset=v(0,-10), text='INGAME_BALLOON_NEW_POWER',                                                       flags='callout-bottom-add_as_child',  scale_world=false },
        TB_POWER3 = {size=v(250,58), hide_cond='custom_event_wait',                     origin='id:power_button_3:top-center', offset=v(0,-10), text='INGAME_BALLOON_NEW_POWER',                                                       flags='callout-bottom-add_as_child',  scale_world=false },       

        -- TB_ROAD   = {size=v(200,58), padding=v(60,8), hide_cond='power_used',         origin='world',                   offset=v(420,436),             text='INGAME_BALLOON_TAP_HERE', prefix='tutorial_text_background',              flags='yellow_text centered bottom',  scale_world=true },
        -- TB_NOTI   = {size=v(200,58), padding=v(60,8), hide_cond='noti_shown',         origin='id:notification_queue_view:top-right', offset=v(0,50),  text='INGAME_BALLOON_NOTIFICATION_TAP_HERE', prefix='tutorial_text_background',  flags='yellow_text centered left',  scale_world=true  },
        TB_WAVE   = {size=v(200,58), padding=v(60,8), hide_cond='wave_sent',          origin='world-middle-right-safe', offset=v(-90, 200),            text='INGAME_BALLOON_TAP_TO_CALL', prefix='tutorial_text_background',           flags='yellow_text centered right',  scale_world=true },
        TB_START  = {size=v(200,58), padding=v(60,8), hide_cond='wave_sent',          origin='world', offset=v(1050, 490),             text='INGAME_BALLOON_TAP_TWICE_WAVE',prefix='tutorial_text_background',         flags='yellow_text centered right',  scale_world=true},
        TB_GOLD   = {size=v(200,58), padding=v(60,8), hide_cond='custom_event_wait',  origin='world',                   offset=v(680,615),             text='INGAME_BALLOON_GOLD', prefix='tutorial_text_background',                  flags='yellow_text',  scale_world=true         },
        TB_GOAL   = {size=v(240,58), padding=v(60,8), hide_cond='custom_event_wait',  origin='world-center-middle', offset=v(-440,0),             text='INGAME_BALLOON_GOAL', prefix='tutorial_text_background',                  flags='yellow_text centered', scale_world=true },
        TB_HERO2  = {size=v(200,58), padding=v(60,8), hide_cond='custom_event_wait',  origin='id:hero_portrait_2:top-center', offset=v(0,-3),  text='INGAME_BALLOON_NEW_HERO',                                                          flags='callout-bottom-left-add_as_child',  scale_world=false},        

        -- COMENTARIO CIRO: entiendo la idea de reutilizar el dibujado de balloons de GG5BalloonView pero para mi deberian seguir siendo entities ingame. 
        LV01_ARBOREAN01    = {size=v(200,40),origin='world', offset=v(980,560),time=3, padding=v(40,15), text='TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-bottom-right centered dialog',},
        LV01_ARBOREAN02    = {size=v(200,40),origin='world', offset=v(980,560),time=3, padding=v(40,15), text='TAUNT_TUTORIAL_ARBOREAN_ALL_0001',       bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-right-bottom centered dialog',},

        LV02_VEZNAN01      = {size=v(200,40),origin='world', offset=v(320,395), time=4, padding=v(40,15), text='TAUNT_STAGE02_VEZNAN_0001',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV02_VEZNAN02      = {size=v(200,40),origin='world', offset=v(320,395), time=4, padding=v(40,15), text='TAUNT_STAGE02_VEZNAN_0002',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog',}, 
        LV02_RAELYN01      = {size=v(200,40),origin='world', offset=v(420,400), time=1, padding=v(40,15), text='TAUNT_STAGE02_RAELYN_0001',   bg_color={37,43,47,255}, text_color={250,50,80,255},  line_color={250,50,80,255},  flags='callout-right-bottom centered dialog',},
        
        LV06_CULTIST01     = {size=v(200,40),origin='world', offset=v(630,625), time=3, padding=v(40,15), text='TAUNT_STAGE06_CULTIST_GREETING_0001',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-left-bottom centered dialog'},
        LV06_CULTIST02     = {size=v(200,40),origin='world', offset=v(630,625), time=3, padding=v(40,15), text='TAUNT_STAGE06_CULTIST_GREETING_0002',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-left-bottom centered dialog',}, 
        LV06_BOSS01        = {size=v(200,40),origin='world', offset=v(545,625), time=3, padding=v(40,15), text='TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001',  bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-right-bottom centered dialog',},
        LV06_BOSS02        = {size=v(200,40),origin='world', offset=v(545,625), time=3, padding=v(40,15), text='TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001', bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-right-bottom centered dialog',},
        LV06_BOSS_TAUNT_01 = {size=v(200,40),origin='world', offset=v(545,650), time=3, padding=v(40,15), text='TAUNT_BOSS_PIG_FROM_POOL_0001',         bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog',},
        LV06_BOSS_TAUNT_02 = {size=v(200,40),origin='world', offset=v(545,650), time=3, padding=v(40,15), text='TAUNT_BOSS_PIG_FROM_POOL_0002',         bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog',},
        LV06_BOSS_TAUNT_03 = {size=v(200,40),origin='world', offset=v(545,650), time=3, padding=v(40,15), text='TAUNT_BOSS_PIG_FROM_POOL_0003',         bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog',},
        LV06_BOSS_TAUNT_04 = {size=v(200,40),origin='world', offset=v(545,650), time=3, padding=v(40,15), text='TAUNT_BOSS_PIG_FROM_POOL_0004',         bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog',},
        LV06_BOSS_TAUNT_05 = {size=v(200,40),origin='world', offset=v(545,650), time=3, padding=v(40,15), text='TAUNT_BOSS_PIG_FROM_POOL_0005',         bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog',},
        LV06_BOSS_TAUNT_06 = {size=v(200,40),origin='world', offset=v(545,650), time=3, padding=v(40,15), text='TAUNT_BOSS_PIG_FROM_POOL_0006',         bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog',},

        LV11_CULTIST01     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_0001',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST02     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_0002',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST03     = {size=v(200,40),origin='world', offset=v(730,504), time=2, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_0003',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST04     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_0004',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_VEZNAN01      = {size=v(200,40),origin='world', offset=v(113,600), time=3, padding=v(40,15), text='TAUNT_STAGE11_VEZNAN_0001',             bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left centered dialog',},
        LV11_CULTIST05_ESCAPE = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_0005',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},

        LV11_CULTIST_TAUNT_01     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_02     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_03     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_04     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_05     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_06     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_IN_BOSSFIGHT_01     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_IN_BOSSFIGHT_02     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_IN_BOSSFIGHT_03     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_IN_BOSSFIGHT_04     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_IN_BOSSFIGHT_05     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},
        LV11_CULTIST_TAUNT_IN_BOSSFIGHT_06     = {size=v(200,40),origin='world', offset=v(730,504), time=3, padding=v(40,15), text='TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006',     bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-center-top centered dialog',},

        LV15_CULTIST01     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_0001',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST02     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_0002',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST03     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_0003',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST04     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_0004',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},

        LV15_DENAS01     = {size=v(200,40),origin='world', offset=v(560,490), time=3, padding=v(40,15), text='TAUNT_STAGE15_DENAS_0001',   bg_color={37,43,47,255}, text_color={0,204,204,255}, line_color={0,204,204,255}, flags='callout-left-bottom centered dialog'},

        LV15_CULTIST01_BOSSFIGHT_01     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST01_BOSSFIGHT_02     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST01_BOSSFIGHT_03     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST01_BOSSFIGHT_04     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST01_BOSSFIGHT_05     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},
        LV15_CULTIST01_BOSSFIGHT_06     = {size=v(200,40),origin='world', offset=v(915,590), time=3, padding=v(40,15), text='TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006',   bg_color={37,43,47,255}, text_color={250,50,80,255}, line_color={250,50,80,255}, flags='callout-right-bottom centered dialog'},

        LV16_DENAS01_BOSSFIGHT_01     = {size=v(200,40),origin='world', offset=v(510,595), time=2.5, padding=v(40,15), text='TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001',   bg_color={37,43,47,255}, text_color={0,204,204,255}, line_color={0,204,204,255}, flags='callout-right-bottom centered dialog'},
    
        LV18_ERIDAN_PREPARATION_TAUNT_01 = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_PREPARATION_0001',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_PREPARATION_TAUNT_02 = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_PREPARATION_0002',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_PREPARATION_TAUNT_03 = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_PREPARATION_0003',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_PREPARATION_TAUNT_04 = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_PREPARATION_0004',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        
        LV18_ERIDAN_FIGHT_TAUNT_01     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0001',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_FIGHT_TAUNT_02     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0002',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_FIGHT_TAUNT_03     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0003',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_FIGHT_TAUNT_04     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0004',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_FIGHT_TAUNT_05     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0005',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_FIGHT_TAUNT_06     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0006',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_FIGHT_TAUNT_07     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0007',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},
        LV18_ERIDAN_FIGHT_TAUNT_08     = {size=v(200,40),origin='world', offset=v(225,610), time=3, padding=v(40,15), text='TAUNT_STAGE18_ERIDAN_FIGHT_0008',   bg_color={37,43,47,255}, text_color={20,220,180,255}, line_color={20,220,180,255},  flags='callout-left-bottom centered dialog'},

        LV19_NAVIRA_START_01     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_START_0001',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_START_02     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_START_0002',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_START_03     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_START_0003',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},

        LV19_NAVIRA_TAUNT_01     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_TAUNT_02     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_TAUNT_03     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_TAUNT_04     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_TAUNT_05     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_TAUNT_06     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},

        LV19_NAVIRA_BEFORE_BOSSFIGHT_01     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_BEFORE_BOSSFIGHT_02     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},
        LV19_NAVIRA_BEFORE_BOSSFIGHT_03     = {size=v(200,40),origin='world', offset=v(810,590), time=3, padding=v(40,15), text='TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003',   bg_color={37,43,47,255}, text_color={255,190,20,255}, line_color={255,190,20,255}, flags='callout-left-bottom centered dialog'},

        LV22_BOSS_BEFORE_FIGHT_EAT_01           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_01',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_BEFORE_FIGHT_EAT_02           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_02',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_BEFORE_FIGHT_EAT_03           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_03',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_BEFORE_FIGHT_EAT_04           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_04',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_BEFORE_FIGHT_EAT_05           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_05',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_BEFORE_FIGHT_EAT_06           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_03',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_BEFORE_FIGHT_EAT_07           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_04',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_BEFORE_FIGHT_EAT_08           = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_BEFORE_FIGHT_EAT_05',        bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_01      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_01',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_02      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_02',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_03      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_03',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_04      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_04',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_05      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_05',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_06      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_05',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_07      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_05',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_BEFORE_FIGHT_RESPONSE_08      = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_BEFORE_FIGHT_RESPONSE_05',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},

        LV22_BOSS_INTRO_01 = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_INTRO_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_BOSS_INTRO_02 = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='LV22_BOSS_INTRO_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV22_MAGE_INTRO_01 = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_INTRO_01',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        LV22_MAGE_INTRO_02 = {size=v(200,40),origin='world', offset=v(410,540), time=3, padding=v(40,15), text='LV22_MAGE_INTRO_02',   bg_color={37,43,47,255}, text_color={107,255,10,255}, line_color={107,255,10,255}, flags='callout-left-bottom centered dialog'},
        
        TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001 = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001', bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002 = {size=v(200,40),origin='world', offset=v(494,592), time=3, padding=v(40,15), text='TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002', bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
    
        LV24_MACHINIST_BEFORE_BOSSFIGHT_01     = {size=v(200,40),origin='world', offset=v(20,460), time=1.25, padding=v(40,15), text='TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV24_MACHINIST_BEFORE_BOSSFIGHT_02     = {size=v(200,40),origin='world', offset=v(0,430), time=3, padding=v(40,15), text='TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},

        LV25_MACHINIST_END_01     = {size=v(200,40),origin='world', offset=v(545,450), time=3, padding=v(40,15), text='TAUNT_STAGE25_BOSS_MACHINIST_END_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},
        LV25_MACHINIST_END_02     = {size=v(200,40),origin='world', offset=v(545,450), time=3, padding=v(40,15), text='TAUNT_STAGE25_BOSS_MACHINIST_END_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},

        LV26_GRYMBEARD_PREPARATION_TAUNT_01    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_PREPARATION_TAUNT_02    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_PREPARATION_TAUNT_03    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_PREPARATION_TAUNT_04    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},

        LV26_GRYMBEARD_FIGHT_TAUNT_01    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_FIGHT_TAUNT_02    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_FIGHT_TAUNT_03    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_FIGHT_TAUNT_04    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},

        LV26_GRYMBEARD_BEFORE_BOSSFIGHT_01     = {size=v(200,40),origin='world', offset=v(140,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_BEFORE_BOSSFIGHT_02     = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_BEFORE_BOSSFIGHT_03     = {size=v(200,40),origin='world', offset=v(130,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},

        LV26_GRYMBEARD_AFTER_BOSSFIGHT_01      = {size=v(200,40),origin='world', offset=v(130,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_AFTER_BOSSFIGHT_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV26_GRYMBEARD_AFTER_BOSSFIGHT_02      = {size=v(200,40),origin='world', offset=v(130,570), time=3, padding=v(40,15), text='TAUNT_STAGE26_BOSS_GRYMBEARD_AFTER_BOSSFIGHT_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},

        LV27_GRYMBEARD_PREPARATION_TAUNT_01    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_PREPARATION_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV27_GRYMBEARD_PREPARATION_TAUNT_02    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_PREPARATION_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV27_GRYMBEARD_PREPARATION_TAUNT_03    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_PREPARATION_0003',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},
        LV27_GRYMBEARD_PREPARATION_TAUNT_04    = {size=v(200,40),origin='world', offset=v(135,570), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_PREPARATION_0004',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-left-bottom centered dialog'},

        LV27_GRYMBEARD_FIGHT_TAUNT_01    = {size=v(200,40),origin='world', offset=v(590,630), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},
        LV27_GRYMBEARD_FIGHT_TAUNT_02    = {size=v(200,40),origin='world', offset=v(590,630), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},
        LV27_GRYMBEARD_FIGHT_TAUNT_03    = {size=v(200,40),origin='world', offset=v(590,630), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},
        LV27_GRYMBEARD_FIGHT_TAUNT_04    = {size=v(200,40),origin='world', offset=v(590,630), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},
        LV27_GRYMBEARD_FIGHT_TAUNT_05    = {size=v(200,40),origin='world', offset=v(590,630), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},
        LV27_GRYMBEARD_FIGHT_TAUNT_06    = {size=v(200,40),origin='world', offset=v(590,630), time=3, padding=v(40,15), text='TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006',   bg_color={37,43,47,255}, text_color={221,107,57,255}, line_color={221,107,57,255}, flags='callout-center-bottom centered dialog'},

        LV30_BOSS_INTRO_01 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_INTRO_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_INTRO_02 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_INTRO_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_INTRO_03 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_INTRO_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},

        LV30_BOSS_PREFIGHT_01 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_PREFIGHT_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_PREFIGHT_02 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_PREFIGHT_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_PREFIGHT_03 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_PREFIGHT_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},

        LV30_BOSS_ABILITY_01 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_ABILITY_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_ABILITY_02 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_ABILITY_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_ABILITY_03 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_ABILITY_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_ABILITY_04 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_ABILITY_04',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_ABILITY_05 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_ABILITY_05',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_ABILITY_06 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_ABILITY_06',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV30_BOSS_ABILITY_07 = {size=v(200,40),origin='world', offset=v(533,652), time=3, padding=v(40,15), text='TAUNT_LVL30_BOSS_ABILITY_07',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},

        LV32_BOSS_INTRO_01 =    {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_INTRO_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_INTRO_02 =    {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_INTRO_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_INTRO_03 =    {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_INTRO_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_PREFIGHT_01 = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_PREFIGHT_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_PREFIGHT_02 = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_PREFIGHT_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_PREFIGHT_03 = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_PREFIGHT_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_ABILITY_01_LOW = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_ABILITY_02_LOW = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_ABILITY_03_LOW = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_ABILITY_04_LOW = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_04',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_ABILITY_05_LOW = {size=v(200,40),origin='world', offset=v(512,490), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_05',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV32_BOSS_ABILITY_01_HIGH = {size=v(200,40),origin='world', offset=v(512,500), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV32_BOSS_ABILITY_02_HIGH = {size=v(200,40),origin='world', offset=v(512,500), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV32_BOSS_ABILITY_03_HIGH = {size=v(200,40),origin='world', offset=v(512,500), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV32_BOSS_ABILITY_04_HIGH = {size=v(200,40),origin='world', offset=v(512,500), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_04',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV32_BOSS_ABILITY_05_HIGH = {size=v(200,40),origin='world', offset=v(512,500), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_ABILITY_05',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV32_BOSS_DEATH = {size=v(200,40),origin='world', offset=v(512,500), time=3, padding=v(40,15), text='TAUNT_LVL32_BOSS_FINAL_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},

        LV34_BOSS_INTRO_01 =    {size=v(200,40),origin='world', offset=v(600,450), time=3, padding=v(40,15), text='TAUNT_LVL34_BOSS_INTRO_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV34_BOSS_BOSSFIGHT_01 =    {size=v(200,40),origin='world', offset=v(600,450), time=3, padding=v(40,15), text='TAUNT_LVL34_BOSS_BOSSFIGHT_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV34_BOSS_BOSS_WAVES_01 =    {size=v(200,40),origin='world', offset=v(600,450), time=3, padding=v(40,15), text='TAUNT_LVL34_BOSS_WAVES_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV34_BOSS_BOSS_WAVES_02 =    {size=v(200,40),origin='world', offset=v(600,450), time=3, padding=v(40,15), text='TAUNT_LVL34_BOSS_WAVES_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-bottom centered dialog'},
        LV34_BOSS_BOSS_DEATH_01_UP = {size=v(200,40),origin='world', offset=v(512,500), time=1.5, padding=v(40,15), text='TAUNT_LVL34_BOSS_DEATH_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-down centered dialog'},
        LV34_BOSS_BOSS_DEATH_01_DOWN = {size=v(200,40),origin='world', offset=v(512,500), time=1.5, padding=v(40,15), text='TAUNT_LVL34_BOSS_DEATH_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},

        LV35_BOSS_INTRO_01 = {size=v(200,40),origin='world', offset=v(512,550), time=3, padding=v(40,15), text='TAUNT_LVL35_BOSS_INTRO_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV35_BOSS_INTRO_02 = {size=v(200,40),origin='world', offset=v(512,550), time=3, padding=v(40,15), text='TAUNT_LVL35_BOSS_INTRO_02',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
        LV35_BOSS_INTRO_03 = {size=v(200,40),origin='world', offset=v(0,590), time=3, padding=v(40,15), text='TAUNT_LVL35_BOSS_INTRO_03',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-left-bottom centered dialog'},
        LV35_BOSS_DESTROY_HOUSE_01 = {size=v(200,40),origin='world', offset=v(512,550), time=3, padding=v(40,15), text='TAUNT_LVL35_BOSS_DESTROY_HOUSE_01',   bg_color={37,43,47,255}, text_color={255,50,80,255}, line_color={255,50,80,255}, flags='callout-center-top centered dialog'},
    },
    
    tower_menu_button_places = {
        v(-92*ring_scale, -146*ring_scale), -- TOP LEFT
        v( 92*ring_scale,-146*ring_scale), -- TOP RIGHT
        v( -153*ring_scale,31*ring_scale),  -- BOTTOM LEFT
        v( 153*ring_scale,31*ring_scale),  -- BOTTOM RIGHT
        v( 0*ring_scale, 155*ring_scale), -- BOTTOM CENTER
        v( -124*ring_scale, -123*ring_scale),  -- AUX TOP LEFT
        v( 124*ring_scale, -123*ring_scale),  -- AUX TOP RIGHT
        v( 149*ring_scale, 75*ring_scale),  -- AUX BOTTOM RIGHT - RALLY POINT
        v(0*ring_scale, 155*ring_scale),   -- SELL - BOTTOM CENTER       
        v(-92*ring_scale, -146*ring_scale), -- RIGHT --Looks like this position is never use?
        v(92*ring_scale, -146*ring_scale), -- LEFT --Looks like this position is never use?
        v( 0*ring_scale, -155*ring_scale), -- TOP CENTER
        v( -145*ring_scale, 78*ring_scale),  -- AUX BOTTOM LEFT - TOWER MODES
        v( -140*ring_scale, -100*ring_scale),  -- AUX TOP LEFT
        v( 140*ring_scale, -100*ring_scale),  -- AUX TOP RIGHT
    },

    tower_menu_power_places = {
        v(30.0,0.8),
        v(50.8,9.6),
        v(58.8,29.6)
    },

    range_center_offset = v(0,-12),

    damage_icons = {
        default             = 'icon_0007',
        -- by damage_type
        [DAMAGE_TRUE      ] = 'icon_0007',
        [DAMAGE_PHYSICAL  ] = 'icon_0007',
        [DAMAGE_MAGICAL   ] = 'icon_0010',
        [DAMAGE_EXPLOSION ] = 'icon_0007',
        -- by damage_icon
        sword               = 'icon_0007',
        magic               = 'icon_0010',
        arrow               = 'icon_0011',
        shot                = 'icon_0012',
        fireball            = 'icon_0013',
    },

    power_button_block_styles = {
        drow_queen = {
            image = 'malicia_powerNet_0001',
            animations = {
                block   = {prefix='malicia_powerNet',from=1,to=14},
                unblock = {prefix='malicia_powerNet',from=15,to=20},
            }
        },
        eb_spider = {
            image = 'spiderQueen_powerNet_0001',
            animations = {
                block   = {prefix='spiderQueen_powerNet',from=1,to=14},
                unblock = {prefix='spiderQueen_powerNet',from=15,to=20},
            }            
        },
        dragon_boss = {
            image = 'ui_redboy_image_0001',
            animations = {
                block   = {prefix='ui_redboy_image',from=1,to=23},
                unblock = {prefix='ui_redboy_image',from=24,to=40},
            }            
        }
    },
    hero_portrait_block_styles = {
        boss_princess = {
            image = 'hero_stun_fire_0001',
            animations = {
                block   = {prefix='hero_stun_fire',from=1,to=16},
                loop    = {prefix='hero_stun_fire',from=17,to=26},
                unblock = {prefix='hero_stun_fire',from=27,to=43},
            }
        }
    }
}
