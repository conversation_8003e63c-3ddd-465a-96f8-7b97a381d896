--
--  game settings - constants for the game
--

local GS = {}

------------------------------------------------------------

-- misc
GS.gameplay_tips_count = 2 -- TODO FIX FIX FIX
GS.early_wave_reward_per_second = 1
GS.early_wave_reward_per_second_default = 1 -- default when upgrade not purchased
GS.claimable_achievements = true

-- difficulty
-- GS.max_difficulty = (KR_TARGET == 'phone' or KR_TARGET == 'tablet') and DIFFICULTY_HARD or DIFFICULTY_IMPOSSIBLE
GS.max_difficulty = DIFFICULTY_IMPOSSIBLE -- always available
GS.difficulty_enemy_hp_max_factor = {0.7, 0.8, 1.0, 1.1}
GS.difficulty_enemy_speed_factor  = {1.0, 1.0, 1.0, 1.1}

-- levels
GS.main_campaign_levels = 16
GS.expansions_unlock_level = {default=6, arachnophobia=11}
GS.dlcs_unlock_level = 1
GS.last_level = 35 -- TODO: keep updated
GS.endless_levels_count = 1
GS.level_ranges = { {1,16}, {17,19}, {20,22}, {23,27}, {28,30}, {31,35}} -- TODO: keep updated
GS.level_range_names = {'base', 'undying_fury', 'ancient_hunger', 'colossal_dwarfare', 'arachnophobia', 'wukong'} -- TODO: keep updated
GS.dlc_names = { {id='dlc_1', name='colossal_dwarfare'}, {id='dlc_2', name='wukong'} } -- TODO: keep updated
GS.debug_levels ={}
GS.level_areas = {{1, 4}, {5, 6}, {7, 11}, {12, 16}, {17,19}, {20,22}, {23,27}, {28,30}, {31,35}} -- TODO: keep updated
GS.campaign_only_levels = {16}

GS.max_stars = GS.last_level * 3
GS.stars_per_mode = 0

GS.seasons = {'halloween', 'christmas'}


------------------------------------------------------------
-- hero
GS.default_hero = "hero_elves_archer"
GS.default_team = {"hero_vesper", "hero_raelyn"}

GS.hero_xp_thresholds = {
    1300,
    5300,
    11300,
    19300,
    31800,
    46800,
    64300,
    88300,
    115300,
}

-- hero level expected for the stage
GS.hero_level_expected = {
    1, 2, 3, 3, 4, 4,   --T1
    5, 5, 6, 7, 8,      --T2
    9, 9, 10, 10, 10,   --T3
    9, 9, 10,           --UF (UP1)
    9, 9, 10,           --AH (UP2)
    9, 9, 10, 10, 10,   --CD (DLC1)
    9, 9, 10,           --AP (UP3)
    9, 9, 10, 10, 10,   --CD (DLC2)
}

GS.hero_level_expected[81] = 1   -- endless
GS.hero_level_expected[82] = 1

GS.hero_level_expected_multipliers_below = {1, 2} -- XP multiplier when hero is underleveled by 1 or 2
GS.hero_level_expected_multipliers_above = {0.5, 0.25} -- XP multiplier when hero is overleveled by 1 or 2

GS.hero_xp_gain_per_difficulty_mode = {
    [DIFFICULTY_EASY] = 1.9,
    [DIFFICULTY_NORMAL] = 1.0,
    [DIFFICULTY_HARD] = 0.9,
    [DIFFICULTY_IMPOSSIBLE] = 0.9,
}

GS.skill_points_for_hero_level = { 0, 4, 8, 12, 16, 20, 24, 28, 32, 36}

GS.default_hero_ultimate_level = 1
GS.max_hero_ultimate_level = 4

-- towers
GS.default_towers = {'royal_archers', 'paladin_covenant', 'arcane_wizard'}

-- items
GS.default_items = {'cluster_bomb', 'deaths_touch', 'winter_age'}

-- relics  TODO ELIMINAR
GS.relic_lvl_steps = {[1]=1, [2]=1, [3]=2, [4]=2, [5]=2, [6]=3, [7]=3, [8]=3, [9]=3, [10]=4}
GS.relic_order = {"relic_none", "relic_banner_of_command", "relic_locket_of_the_unforgiven", "relic_guardian_orb", "relic_hammer_of_the_blessed"} --"relic_mirror_of_inversion", 


GS.endless_gems_for_wave = 1  -- GameSettings.endlessGemsPerLevel
GS.gems_factor_per_mode = { 1, 1.2, 1.2 }  -- campaign, etc.
GS.gems_per_level = {
    40,   -- 1| Tutorial
    70,   -- 2|
    80,   -- 3|
    100,  -- 4|
    120,  -- 5|
    180,  -- 6| BF1
    120,  -- 7|
    120,  -- 8|
    130,  -- 9|
    150, -- 10|
    230, -- 11| BF2
    160, -- 12| 
    160, -- 13|
    180, -- 14|
    300, -- 15| BF3 
    250, -- 16| BF4 (Does not give gems)
    
    150, -- 17| Update 1 (WIP here onwards- 24 jun-)
    150, -- 18| 
    200, -- 19| Update 1 BF
    150, -- 20| Update 2 (Crocs)
    150, -- 21| 
    200, -- 22| Update 2 BF
    100, -- 23| DLC 1 (Dwarves)
    150, -- 24| DLC 1 Boss Grymbeard Machinist
    100, -- 25| 
    150, -- 26| DLC 1 Boss Deformed Grymbeard
    200, -- 27| DLC 1 Boss Grymbeard Mecha
    150, -- 28| Update 3 (Spiders)
    150, -- 29| 
    200, -- 30| Update 3 BF

    150, -- 31| DLC 2 Stage 1
    200, -- 32| DLC 2 Stage 2 - Bossfight
    150, -- 33| DLC 2 Stage 3
    200, -- 34| DLC 2 Stage 4 - Bossfight
    300, -- 35| DLC 2 Stage 5 - Bossfight Final

}

------------------------------------------------------------
-- encyclopedia

GS.encyclopedia_tower_fmt = 'encyclopedia_towers_00%02i'
GS.encyclopedia_tower_thumb_fmt = 'encyclopedia_tower_thumbs_00%02i'
GS.encyclopedia_enemy_fmt = 'encyclopedia_creeps_00%02i'
GS.encyclopedia_enemy_thumb_fmt = 'encyclopedia_creep_thumbs_00%02i'
GS.encyclopedia_enemies = {
    { name = 'enemy_hog_invader', always_shown=true},
    { name = 'enemy_tusked_brawler', always_shown=true},
    { name = 'enemy_turtle_shaman', always_shown=true},
    { name = 'enemy_bear_vanguard', always_shown=true},
    { name = 'enemy_cutthroat_rat', always_shown=true},
    { name = 'enemy_dreadeye_viper', always_shown=true},
    { name = 'enemy_surveyor_harpy', always_shown=true},
    { name = 'enemy_skunk_bombardier', always_shown=true},
    { name = 'enemy_hyena5', always_shown=true},
    { name = 'enemy_rhino', always_shown=true},
    { name = 'enemy_acolyte', always_shown=false},
    { name = 'enemy_lesser_sister', always_shown=false},
    { name = 'enemy_corrupted_stalker', always_shown=false},
    { name = 'enemy_crystal_golem', always_shown=false},
}

------------------------------------------------------------
-- filter by target
for i=#GS.encyclopedia_enemies,1,-1 do
    if GS.encyclopedia_enemies[i].target and GS.encyclopedia_enemies[i].target ~= KR_TARGET then
        table.remove(GS.encyclopedia_enemies,i)
    end
end

------------------------------------------------------------
-- required exoskeletons

GS.items_required_exoskeletons = {}
GS.items_required_exoskeletons.portable_coil = {
    'item_portable_coilDef', 
    'item_portable_coil_hitDef', 
}
GS.items_required_exoskeletons.veznan_wrath = {'veznan_wrath_exoskeleton'}

-- GS.tower_order = {
--     'royal_archers',
--     'paladin_covenant',
--     'arcane_wizard',
--     'tricannon',
--     'arborean_emissary',
--     'demon_pit',
--     'ballista',
--     'elven_stargazers',
--     'rocket_gunners',
--     'necromancer',
--     -- 'flamespitter',
-- }

return GS
