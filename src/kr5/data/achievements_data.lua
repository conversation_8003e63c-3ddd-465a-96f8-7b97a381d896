local features = require 'features'

local t = {
    { order="01", name="LEARNING_THE_ROPES",        icon=4,  reward=30                          },
    { order="02", name="TIPPING_THE_SCALES",        icon=15, reward=30,                         },
    { order="03", name="FIELD_TRIP_RUINER",         icon=38, reward=30,                         },
    { order="04", name="ITS_A_SECRET_TO_EVERYONE",  icon=16, reward=50, goal=5, censored_cn = true},
    { order="05", name="CIRCLE_OF_LIFE",            icon=28, reward=30,                         },
    { order="06", name="PLAYFUL_FRIENDS",           icon=29, reward=30,                         },
    { order="07", name="MOST_DELICIOUS",            icon=31, reward=30,                         },
    { order="08", name="NATURES_WRATH",             icon=25, reward=50,  goal=30                },
    { order="09", name="MIGHTY_I",                  icon=5,  reward=50,  goal=500               },
    { order="10", name="GREENLIT_ALLIES",           icon=12, reward=30,  goal=10                },
    { order="11", name="OVER_THE_EDGE",             icon=39, reward=30,                         },
    { order="12", name="CLEANUP_IS_OPTIONAL",       icon=34, reward=30,                         },
    { order="13", name="RUNEQUEST",                 icon=13, reward=30,  goal=0x003F            }, -- all 6 runes collected
    { order="14", name="NONE_SHALL_PASS",           icon=41, reward=50,                         },
    { order="15", name="CRAFTING_IN_THE_MINES",     icon=14, reward=30,                         },
    { order="16", name="PORKS_OFF_THE_MENU",        icon=2,  reward=50                          },
    { order="17", name="OUTBACK_BARBEQUICK",        icon=54, reward=50,                         },
    { order="18", name="SAVIOUR_OF_THE_GREEN",      icon=3,  reward=50,  goal=6                 },
    { order="19", name="NOT_A_MOMENT_TO_WASTE",     icon=9,  reward=30,  goal=15                },
    { order="20", name="SILVER_FOR_MONSTERS",       icon=30, reward=30, censored_cn = true    },
    { order="21", name="CROW_SCARER",               icon=33, reward=30,                         },
    { order="22", name="WE_RE_NOT_GONNA_TAKE_IT",   icon=42, reward=50, goal=15                 },
    { order="23", name="BREAKER_OF_CHAINS",         icon=35, reward=50,                         },
    { order="24", name="GEM_SPILLER",               icon=40, reward=30,                         },
    { order="25", name="UNBOUND_VICTORY",           icon=43, reward=50                          },
    { order="26", name="GET_THE_PARTY_STARTED",     icon=36, reward=30, censored_cn = true    },
    { order="27", name="WAR_MASONRY",               icon=8,  reward=50,  goal=100               },
    { order="28", name="PROMOTION_DENIED",          icon=44, reward=50,  goal=30                },
    { order="29", name="STARLIGHT",                 icon=32, reward=30,                         },
    { order="30", name="CLEANSE_THE_KING",          icon=11, reward=100                         },
    { order="31", name="YOU_SHALL_NOT_CAST",        icon=53, reward=50,                         },
    { order="32", name="CRYSTAL_CLEAR",             icon=21, reward=100,  goal=5                },
    { order="33", name="MIGHTY_II",                 icon=6,  reward=100, goal=3000              },
    { order="34", name="ALL_THE_SMALL_THINGS",      icon=10, reward=50,  goal=182               },
    { order="35", name="THE_CAVALRY_IS_HERE",       icon=22, reward=30,  goal=1000              },
    { order="36", name="WEIRDER_THINGS",            icon=60,  reward=30,                        },
    { order="37", name="OVINE_JOURNALISM",          icon=63,  reward=50, goal=0x7               }, -- 3 stages
    { order="38", name="ONE_SHOT_TOWER",            icon=45,  reward=50,                        },
    { order="39", name="CROWD_CONTROL",             icon=46,  reward=50                         },
    { order="40", name="WOBBA_LUBBA_DUB_DUB",       icon=62,  reward=30, censored_cn = true     },
    { order="41", name="PEST_CONTROL",              icon=58,  reward=30, goal=300               },
    { order="42", name="TURN_A_BLIND_EYE",          icon=55,  reward=50, goal=100               },
    { order="43", name="TAKE_ME_HOME",              icon=61,  reward=30,                        },
    { order="44", name="BUTTERTENTACLES",           icon=47,  reward=50                         },
    { order="45", name="BYE_BYE_BEAUTIFUL",         icon=56,  reward=100,                       },
    { order="46", name="CONJUNTIVICTORY",           icon=57,  reward=150,                       },
    { order="47", name="CONQUEROR_OF_THE_VOID",     icon=48,  reward=150, goal=5                },
    { order="48", name="LINIREAN_RESISTANCE",       icon=51,  reward=50,                        },
    { order="49", name="DARK_RUTHLESSNESS",         icon=52,  reward=50,                        },
    { order="50", name="UNENDING_RICHES",           icon=59,  reward=50, goal=150000            },
    { order="51", name="SIGNATURE_TECHNIQUES",      icon=23, reward=30,  goal=500               },
    { order="52", name="ROYAL_CAPTAIN",             icon=18, reward=50,                         },
    { order="53", name="DARK_LIEUTENANT",           icon=17, reward=50,                         },
    { order="54", name="FOREST_PROTECTOR",          icon=20, reward=50,                         },
    { order="55", name="UNTAMED_BEAST",             icon=19, reward=50,                         },
    { order="56", name="MIGHTY_III",                icon=7,  reward=150, goal=10000             },
    { order="57", name="AGE_OF_HEROES",             icon=24,  reward=500, goal=0x7FFF           },-- all 16 stages completed
    { order="58", name="IRONCLAD",                  icon=26,  reward=500, goal=0x7FFF           },-- all 16 stages completed
    { order="59", name="SEASONED_GENERAL",          icon=49,  reward=100, goal=0xFFFF           },
    { order="60", name="MASTER_TACTICIAN",          icon=50,  reward=250, goal=0xFFFF           },
    { order="61", name="TREE_HUGGER",               icon=65,  reward=30,                        }, -- update1
    { order="62", name="RUST_IN_PEACE",             icon=67,  reward=30,                        }, -- update1
    { order="63", name="WE_ARE_ALL_MAD_HERE",       icon=66,  reward=50, goal=0x7, censored_cn = true }, -- update1 /  3 stages
    { order="64", name="ROCK_BEATS_ROCK",           icon=68,  reward=30,                        }, -- update1
    { order="65", name="SPECTRAL_FURY",             icon=64,  reward=50,                        }, -- update1
    { order="66", name="SAVIOUR_OF_THE_FOREST",     icon=69,  reward=50,                        }, -- update2 crocs
    { order="67", name="SMOOTH_OPER_GATOR",         icon=70,  reward=30,                        }, -- update2 crocs
    { order="68", name="SEE_YA_LATER_ALLIGATOR",    icon=71,  reward=50,                        }, -- update2 crocs
    { order="69", name="HAIL_TO_THE_K_BABY",        icon=72,  reward=50,                        }, -- update2 crocs
    { order="70", name="SCRAMBLED_EGGS",            icon=73,  reward=30, goal=50                }, -- update2 crocs
    { order="71", name="MECHANICAL_BURNOUT",        icon=74,  reward=30, dlc='dlc_1'            }, -- dlc1 dwarfs
    { order="72", name="FACTORY_STRIKE",            icon=75,  reward=50, dlc='dlc_1'            }, -- dlc1 dwarfs
    { order="73", name="DOMO_ARIGATO",              icon=76,  reward=30, goal=20, dlc='dlc_1'   }, -- dlc1 dwarfs
    { order="74", name="KEPT_YOU_WAITING",          icon=77,  reward=30, dlc='dlc_1'            }, -- dlc1 dwarfs
    { order="75", name="GIFT_OF_LIFE",              icon=78,  reward=30, dlc='dlc_1'            }, -- dlc1 dwarfs
    { order="76", name="GARBAGE_DISPOSAL",          icon=79,  reward=50, goal=10, dlc='dlc_1'   }, -- dlc1 dwarfs
    { order="77", name="DISTURBING_THE_PEACE",      icon=80,  reward=30, dlc='dlc_1'            }, -- dlc1 dwarfs
    { order="78", name="OBLITERATE",                icon=81,  reward=50, goal=0x1F, dlc='dlc_1' }, -- dlc1 dwarfs
    { order="79", name="SHUT_YOUR_MOUTH",           icon=82,  reward=50, dlc='dlc_1'            }, -- dlc1 dwarfs
    { order="80", name="DLC1_WIN_BOSS",             icon=83,  reward=50, dlc='dlc_1'            }, -- dlc1 dwarfs
    { order="81", name="INTO_THE_OGREVERSE",        icon=84,  reward=30,                      }, -- update3 spiders
    { order="82", name="A_COON_OF_SURPRISES",       icon=85,  reward=50,                      }, -- update3 spiders
    { order="83", name="LUCAS_SPIDER",              icon=86,  reward=50,                      }, -- update3 spiders
    { order="84", name="NO_FLY_ZONE",               icon=87,  reward=30, goal=50              }, -- update3 spiders
    { order="85", name="ARACHNED",                  icon=88,  reward=50,                      }, -- update3 spiders
    { order="86", name="DLC2_WIN_BOSS_REDBOY",      icon=89,  reward=50, dlc='dlc_2'            }, -- dlc2 wukong
    { order="87", name="DLC2_GATHER_ENVELOPS",      icon=92,  reward=30, dlc='dlc_2'            }, -- dlc2 wukong
    { order="88", name="DLC2_WIN_BOSS_PRINCESS",    icon=90,  reward=50, dlc='dlc_2'            }, -- dlc2 wukong
    { order="89", name="DLC2_WIN_BOSS_KING",        icon=91,  reward=50, dlc='dlc_2'            }, -- dlc2 wukong
}

-- filter
for i=#t,1,-1 do
    local targets = t[i].targets
    if targets then        
        for _,target in pairs(targets) do
            if target == KR_TARGET then
                goto found
            end
        end
        table.remove(t,i)        
    end

    if features.censored_cn and t[i].censored_cn then
        table.remove(t,i)        
    end
    ::found::
end
return t
