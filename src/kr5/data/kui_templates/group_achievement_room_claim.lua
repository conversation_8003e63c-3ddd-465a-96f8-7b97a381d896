-- DO NOT EDIT | file generated by kui_export.jsfl from achievements_room.fla
return
{class='KView', children={
     {class='GG5Button', id='achievement_room_achievement_claim_button', pos=v(0.05,-0.25), default_image_name='achievements_room_button_achievements_room_achievement_bg_claim_0001', focus_image_name='achievements_room_button_achievements_room_achievement_bg_claim_0003', image_offset=v(-273.65,-111), hit_rect=r(-273.65,-111,553,230), children={
          {class='KImageView', id='image_achievements_room_achievement_icon', pos=v(-184.15,-19.05), anchor=v(58.3,58.05), image_name='achievements_room_image_achievements_room_achievement_icon_'},
          {class='GG5Label', id='label_achievement_room_claim', pos=v(-110.65,-70.35), scale=v(1,1), size=v(334.45,42.2), text='Claim Reward!', text_key='ACHIEVEMENT_ROOM_achievement_claim', font_name='fla_body', font_size=31, colors={text={255,226,0}}, text_align='center', vertical_align='top', line_height_extra='2', fit_size=true},
          {class='GG5Label', id='label_achievement_room_claim_gems', pos=v(35.15,-19), scale=v(1,1), size=v(149.5,60.7), text='30', font_name='fla_numbers', font_size=46, colors={text={222,247,255}}, text_align='left', vertical_align='top', line_height_extra='0', fit_size=true},
          {class='KView', id='image_achievement_room_claim_glow_fx', pos=v(-0.45,3.55), children={
               {class='KImageView', pos=v(-135.05,-18.6), scale=v(1,1), anchor=v(121.95,74.25), image_name='achievements_room_image_achievement_room_claim_glow_fx_'},
          }},
          {class='KImageView', id='image_dlc_1_flag', pos=v(-218.9,-68.45), anchor=v(23.9,26.3), image_name='achievements_room_image_dlc_dwarf_flag_'},
          {class='KImageView', id='image_dlc_2_flag', pos=v(-218.9,-68.45), anchor=v(23.9,26.3), image_name='achievements_room_image_dlc_wukong_flag_'},
     }},
     {class='GGAni', id='animation_achievement_room_claim_gems', pos=v(38.75,30.95), anchor=v(144.95,182), animation={ prefix='achievements_room_animation_achievement_room_claim_gems', from=1, to=22 }, loop=true},
}}
