return {
    default_page_for_terrain = {
        [TERRAIN_STYLE_SEA_OF_TREES] = 1,
    },
    default_page_for_level = {
        [7] = 2,
        [8] = 2,
        [9] = 2,
        [10] = 2,
        [11] = 2,
        [12] = 3,
        [13] = 3,
        [14] = 3,
        [15] = 3,
        [16] = 3,
        [17] = 4,
        [18] = 4,
        [19] = 4,
        [20] = 5,
        [21] = 5,
        [22] = 5,
        [23] = 6,
        [24] = 6,
        [25] = 6,
        [26] = 6,
        [27] = 6,
        [28] = 7,
        [29] = 7,
        [30] = 7,
        [31] = 8,
        [32] = 8,
        [33] = 9,
        [34] = 9,
        [35] = 11,
    },
    enemy_pages = {  -- 10 per page 
        -- 1. enemy_werebeasts
        {
            'enemy_hog_invader',
            'enemy_tusked_brawler',
            'enemy_turtle_shaman',
            'enemy_bear_vanguard',
            'enemy_cutthroat_rat',
            'enemy_dreadeye_viper',
            'enemy_surveyor_harpy',
            'enemy_skunk_bombardier',
            'enemy_hyena5',
            'enemy_rhino',
        },
        -- 2. enemy_terrain_2
        {
            'enemy_acolyte',
            'enemy_lesser_sister',
            'enemy_lesser_sister_nightmare',
            'enemy_small_stalker',
            'enemy_unblinded_priest',
            'enemy_unblinded_abomination',
            'enemy_spiderling',
            'enemy_unblinded_shackler',
            'enemy_armored_nightmare',
            'enemy_corrupted_stalker',
        },
        -- 3. enemy_terrain_3
        {
            'enemy_crystal_golem',
            'enemy_glareling',
            'enemy_blinker',
            'enemy_mindless_husk',
            'enemy_vile_spawner',
            'enemy_lesser_eye',
            'enemy_noxious_horror',
            'enemy_hardened_horror',
            'enemy_evolving_scourge',
            'enemy_amalgam',
        },
        -- 4. enemy_terrain_4 (halloween)
        {
            'enemy_corrupted_elf',
            'enemy_specter',
            'enemy_dust_cryptid',
            'enemy_bane_wolf',
            'enemy_deathwood',
            'enemy_animated_armor',
            'enemy_revenant_soulcaller',
            'enemy_revenant_harvester',
        },
        -- 5. enemy_terrain_5 (crocs)
        {
            'enemy_crocs_basic_egg',
            'enemy_crocs_basic',
            'enemy_quickfeet_gator_chicken_leg',
            'enemy_killertile',
            'enemy_crocs_flier',
            'enemy_crocs_ranged',
            'enemy_crocs_shaman',
            'enemy_crocs_tank',
            'enemy_crocs_egg_spawner',
            'enemy_crocs_hydra',
        },
        -- 6. enemy_terrain_6 (dlc1, dwarfs)
        {
            'enemy_darksteel_hammerer',
            'enemy_darksteel_shielder',
            'enemy_surveillance_sentry',
            'enemy_mad_tinkerer',
            'enemy_brute_welder',
            'enemy_scrap_speedster',
            'enemy_common_clone',
            'enemy_darksteel_fist',
            'enemy_darksteel_anvil',
            'enemy_darksteel_hulk',
        },
        -- 7. enemy_terrain_7 (update 2, spiders)
        {
            'enemy_spiderling',
            'enemy_spider_priest',
            'enemy_glarenwarden',
            'enemy_ballooning_spider',
            'enemy_ballooning_spider_flyer',
            'enemy_spider_sister',
            'enemy_cultbrood',
            'enemy_drainbrood',
            'enemy_spidead',
            'boss_spider_queen',
        },
        -- 8. enemy_terrain_8 (dlc2, wukong) -- page 1
        {
            'enemy_fire_phoenix',
            'enemy_blaze_raider',
            'enemy_flame_guard',
            'enemy_fire_fox',
            'enemy_nine_tailed_fox',
            'enemy_burning_treant',
            'enemy_wuxian',
            'enemy_ash_spirit',
        },
        -- 9. enemy_terrain_8 (dlc2, wukong) -- page 2
        {
            'enemy_storm_spirit',
            'enemy_qiongqi',
            'enemy_gale_warrior',
            'enemy_storm_elemental',
            'enemy_water_sorceress',
            'enemy_fan_guard',
            'enemy_water_spirit',
            'enemy_water_spirit_spawnless',
            'enemy_citizen',
            'enemy_palace_guard'
        },
        -- 10. enemy_terrain_8 (dlc2, wukong) -- page 3
        {
            'enemy_terracota',
            'enemy_big_terracota',
            'enemy_palace_guard',
            'boss_princess_iron_fan',
        },

        -- 11. enemy_terrain_8 (dlc2, wukong) -- page 4
        {
            'enemy_hellfire_warlock',
            'enemy_golden_eyed',
            'enemy_doom_bringer',
            'enemy_demon_minotaur',
            'boss_bull_king',
        },

        -- mods enemies
        {
            'mod_natures_vigor',
            'mod_wrath_of_the_fallen',
            'enemy_skunk_bombardier_poison_mod',
            'enemy_hyena5_feast_mod',
            'mod_stun',
            'mod_poison',
            'mod_blood',
            'mod_test_head_pos_kr5',
            'mod_test_hit_pos_kr5',
            'mod_test_mod_pos_kr5',
        },

        -- mods heroes
        {
            'mod_hero_king_denas_sybarite',
            'hero_alleria5_go_for_the_throat_bleed',
            'hero_alleria5_invisibility_modifier'
        },

        -- mods towers
        {
            'mod_hide_tower_test',
            'mod_boss_crocs_tower_eat',
            'mod_stage_22_tower_destroyed',
            'mod_boss_crocs_tower_timed_destroy',
            'boss_princess_iron_fan_tower_debuff_bossfight',
        },
    },
}
