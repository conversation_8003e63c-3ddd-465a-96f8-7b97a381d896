-- ------------------------------------------------------------
-- recommended new format
-- ------------------------------------------------------------
-- 
-- local b = {}
-- b.heroes.common = {}
-- 
-- b.heroes.common.xp_level_steps = {}
-- b.heroes.common.xp_level_steps[1] = 1
-- b.heroes.common.xp_level_steps[3] = 2
-- b.heroes.common.xp_level_steps[7] = 3
-- b.heroes.common.xp_level_steps[9] = 3
-- 
-- b.heroes.common.xp_level_steps_ulti = {}
-- b.heroes.common.xp_level_steps_ulti[1]=1
-- b.heroes.common.xp_level_steps_ulti[5]=2
-- b.heroes.common.xp_level_steps_ulti[10]=3
-- 
-- b.heroes.common.melee_attack_range = 72
-- 
-- b.heroes.hero_king_denas = {}
-- b.heroes.hero_king_denas.dead_lifetime = 18
-- b.heroes.hero_king_denas.speed = 75
-- b.heroes.hero_king_denas.regen_cooldown = 1
-- 
-- return b
--
-- ------------------------------------------------------------

local function v(v1,v2) return {x=v1,y=v2} end
local function fts(v) return v / FPS end

local heroes  = {
    common = {
        xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
        xp_level_steps_ulti = {[1]=1,[5]=2,[10]=3},
        melee_attack_range = 72,
    },
    hero_king_denas = {
        dead_lifetime = 18,
        speed = 75,
        regen_cooldown = 1,
        armor = {0.05, 0.06, 0.08, 0.1, 0.12, 0.15, 0.18, 0.22, 0.26, 0.31},
        hp_max = {200, 215, 230, 250, 270, 300, 320, 340, 370, 400},
        melee_damage_max = {7, 8, 9, 10, 11, 12, 13, 15, 16, 18},
        melee_damage_min = {5, 5, 6, 7, 8, 8, 9, 10, 11, 12},
        regen_health = {16, 17, 18, 20, 22, 24, 26, 27, 30, 32},
        basic_melee = {
            cooldown = 1.0,
            xp_gain_factor = 10.0,
        }, 
        mighty = {
            cooldown = 18,
            damage_radius = 75/2,
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {36, 47, 58, 72},
            damage_min = {24, 31, 38, 48},
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {810, 810, 810, 810},
            available = true
        },
        sybarite = {
            cooldown = {25, 25, 25, 25},
            lost_health = 0.65,
            heal_hp = {45, 75, 120, 175},
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {1125, 1125, 1125, 1125},
            available = true
        },
        kings_speech = {
            duration = {4, 6, 8, 10},
            cooldown_factor = {0.90, 0.85, 0.80, 0.75},
            max_range = 500/2,
            min_range = 0,
            cooldown = {30, 30, 30, 30},
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {1350, 1350, 1350, 1350},
            available = true
        },
        pounding_smash = {
            duration = {2, 3, 4, 5},
            radius = 330/2,
            cooldown = {22, 22, 22, 22},
            damage_max = {12, 16, 20, 24},
            damage_min = {8, 10, 14, 16},
            damage_type = DAMAGE_PHYSICAL,
            min_targets = 2,
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {990, 990, 990, 990},
            available = true
        },
        ultimate = {
            xp_level_steps = {[1]=1,[5]=2,[10]=3},
            duration = {4, 6, 8, 10},
            cooldown = {50, 50, 50, 50},
            damage_min = {8, 10, 14, 16},
            damage_max = {12, 16, 20, 24},
            damage_type = DAMAGE_PHYSICAL,
            damage_every = 0.5,
            radius = 150/2,
        }
    },
    hero_alleria5 = {
        dead_lifetime = 22,
        speed = 90,
        regen_cooldown = 1,
        armor = {0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.10, 0.11, 0.12},
        hp_max = {175, 185, 200, 210, 220, 235, 250, 260, 280, 300},
        melee_damage_max = {6, 7, 8, 9, 10, 11, 12, 14, 16, 18},
        melee_damage_min = {2, 3, 3, 3, 4, 4, 5, 6, 6, 7},
        regen_health = {14, 15, 16, 17, 18, 19, 20, 21, 22, 24},
        block_range = 135/2,
        basic_melee = {
            cooldown = 0.8,
            xp_gain_factor = 10.0,
        }, 
        basic_ranged = {
            min_range = 135/2,
            max_range = 430/2, 
            damage_max = {6, 7, 8, 9, 10, 11, 12, 14, 16, 18},
            damage_min = {2, 3, 3, 3, 4, 4, 5, 6, 6, 7},
            cooldown = 0.8,
            xp_gain_factor = 10.0
        },
        go_for_the_throat = {
            cooldown = 18,
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {42, 55, 68, 84},
            damage_min = {28, 36, 45, 55},
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {810, 810, 810, 810},
            bleed_duration = 5,
            bleed_damage_min = {3, 6, 9, 12},
            bleed_damage_max = {3, 6, 9, 12},
            bleed_every = 1, --seconds
            available = true
        },
        rangers_camouflage = {
            cooldown = {22, 20, 18, 16},
            distance = 150,
            duration = {3, 6, 9, 12},
            min_distance_from_end = 200,
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {990, 900, 810, 720},
            available = true
        },
        whistling_arrow = {
            cooldown = {20, 18, 16, 14},
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {6, 8, 10, 12},
            damage_min = {4, 5, 6, 8},
            stun_duration = {3, 3, 3, 3},
            max_range = 430/2,
            min_range = 135/2,
            bounces = {1, 2, 3, 4},
            bounce_range = 300,
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {900, 810, 720, 630},
            available = true
        },
        chilling_roar = {
            cooldown = {25, 25, 25, 25},
            damage_factor = {0.25,0.4,0.6,0.75},
            debuff_duration = {4, 6, 8, 10},
            min_targets = 2,
            max_range_trigger = 150,
            max_range_effect = 200,
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {1125, 1125, 1125, 1125},
            available = true
        },
        ultimate = {
            xp_level_steps = {[1]=1,[5]=2,[10]=3},
            cooldown = {30, 30, 30, 30},
            arrows = {5, 7, 9, 12},
            damage_min = {32, 42, 52, 65},
            damage_max = {40, 50, 64, 80},
            damage_type = DAMAGE_PHYSICAL,
            radius = 180/2,
            time_between_arrows = 0.1
        }
    },
    hero_velann = {
        dead_lifetime = 25,
        speed = 50,
        teleport_min_distance = 80,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {120, 125, 135, 145, 150, 160, 170, 180, 190, 200},
        regen_health = {12, 12, 13, 14, 15, 16, 17, 18, 19, 20},
        basic_melee = {
            cooldown = 2.0,
            xp_gain_factor = 20.0,
            damage_max = {8, 9, 10, 11, 12, 14, 15, 17, 20, 22},
            damage_min = {5, 6, 7, 7, 8, 9, 10, 11, 13, 14},
        },
        basic_ranged = {
            min_range = 135/2,
            max_range = 450/2,
            cooldown = 2,
            xp_gain_factor = 20.0,
            damage_max = {15, 18, 19, 21, 24, 27, 30, 34, 38, 44},
            damage_min = {10, 12, 13, 14, 16, 18, 20, 22, 25, 28},
        },
        imp_config = {
            duration = {10, 10, 10, 10},
            cooldown = {1, 1, 1, 1},
            hp_max = {20, 25, 30, 40},
            damage_max = {3, 3, 4, 5},
            damage_min = {1, 2, 2, 3},
            max_range = 160 * 1.28 / 2,
            xp_level_steps = {[1]=1,[2]=1,[3]=2,[4]=2,[5]=2,[6]=2,[7]=3,[8]=3,[9]=3,[10]=3},
        },
        void_prison = {
            cooldown = {22, 22, 22, 22},
            max_range_trigger = 300 / 2,
            max_range_effect = 350 / 2,
            min_targets = 1,
            max_targets = {2, 3, 4, 6},
            damage_type = DAMAGE_MAGICAL,
            damage = {30, 52, 64, 100},
            duration = {3, 4, 4, 5},
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {990, 990, 990, 990},
            available = true,
        },
        friends_on_the_other_side = {
            cooldown = {25, 25, 25, 25},
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {1125, 1125, 1125, 1125},
            available = true,
            imp = {
                cooldown = {1, 1, 1, 1},
                damage_max = {6, 7, 9, 12},
                damage_min = {4, 5, 6, 8},
                hp_max = {30, 40, 50, 60}
            }
        },
        voices_from_beyond = {
            duration = {5, 7, 8, 10},
            damage_max = {40, 50, 60, 80},
            damage_min = {20, 30, 35, 40},
            damage_type = DAMAGE_MAGICAL,
            inflicted_damage_factor = {0.8, 0.7, 0.6, 0.5},
            received_damage_factor = {1, 1, 1, 1},
            max_range = 300/2,
            cooldown = {30, 30, 30, 30},
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {1350, 1350, 1350, 1350},
            available = true,
        },
        void_rift = {
            duration = {4, 6, 8, 10},
            cooldown = {28, 28, 28, 28},
            damage_max = {10, 20, 30, 50},
            damage_min = {6, 12, 20, 32},
            min_damage_to_spawn = {100, 200, 300, 400},
            heal_factor = {0.3, 0.5, 0.7, 0.9},
            damage_type = DAMAGE_MAGICAL,
            damage_every = 0.2,
            max_range_trigger = 300 / 2,
            max_range_effect = 350 / 2,
            radius = 120/2,
            min_targets = 2,
            xp_level_steps = {[1]=1,[3]=2,[7]=3,[9]=3},
            xp_gain = {1260, 1260, 1260, 1260},
            available = true
        },
        ultimate = {
            xp_level_steps = {[1]=1,[5]=2,[10]=3},
            cooldown = {100, 90, 80, 70},
            entity = {
                speed = 4.6 * 30,
                cooldown = {0.5, 0.5, 0.5, 0.5},
                damage_min = {8, 10, 12, 12},
                damage_max = {12, 15, 20, 20},
                damage_type = DAMAGE_PHYSICAL,
                hp_max = {150, 170, 180, 180},
                regen_health = {30, 34, 36, 36},
            }
        }
    },
    --THERIEN
    hero_space_elf = {
        stats = {
            hp = 3,
            armor = 0,
            damage = 6,
            cooldown = 8,
        },
        dead_lifetime = 25,
        speed = 90,
        teleport_min_distance = 200,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {160, 170, 180, 190, 200, 210, 220, 230, 240, 250},
        regen_health = {13, 14, 14, 15, 16, 17, 18, 18, 19, 20},
        basic_melee = {
            cooldown = 1,
            xp_gain_factor = 2.36,
            damage_max = {7, 9, 10, 12, 13, 15, 16, 18, 20, 24},
            damage_min = {5, 6, 7, 8, 9, 10, 11, 12, 13, 15},
        },
        basic_ranged = {
            min_range = 68,
            max_range = 160,
            cooldown = 1.5,
            xp_gain_factor = 1.6,
            damage_max = {20, 22, 25, 28, 31, 34, 37, 40, 44, 48},
            damage_min = {11, 12, 14, 15, 17, 18, 20, 22, 24, 26},
            damage_type = DAMAGE_MAGICAL,
        },
        astral_reflection = {
            max_range = 175,
            cooldown = {25, 25, 25},
            xp_gain = {25, 50, 75},
            entity = {
                duration = 12,
                range = 90,
                basic_melee = {
                    cooldown = 1,
                    damage_type = DAMAGE_MAGICAL,
                    damage_min = {4, 8, 10},
                    damage_max = {8, 12, 16},
                },
                basic_ranged = {
                    cooldown = 1.5,
                    damage_type = DAMAGE_MAGICAL,
                    damage_min = {14, 18, 22},
                    damage_max = {25, 32, 40},
                    min_range = 68,
                    max_range = 160,
                }
            }
        },
        black_aegis = {
            range = 200,
            xp_gain = {18, 36, 54},
            cooldown = {18, 18, 18},
            duration = {6, 8, 10},
            shield_base = {60, 100, 140},
            explosion_damage = {30, 60, 90},
            explosion_range = 80,
            explosion_damage_type = DAMAGE_MAGICAL,
        },
        void_rift = {
            cooldown = {30, 30, 20},
            xp_gain = {30, 60, 90},
            duration = {6, 8, 10},
            cracks_amount = {1, 2, 3},
            damage_min = {2, 2, 2},
            damage_max = {5, 5, 5},
            s_damage_min = {8, 8, 8},
            s_damage_max = {20, 20, 20},
            damage_every = 0.25,
            damage_type = DAMAGE_MAGICAL,
            max_range_trigger = 200,
            max_range_effect = 300,
            min_targets = 2,
            radius = 100,
        },
        spatial_distortion = {
            cooldown = {25, 25, 25},
            duration = {6, 7, 8},
            xp_gain = {25, 50, 75},
            range_factor = {1.10, 1.12, 1.15},
            s_range_factor = {0.10, 0.12, 0.15},
        },
        ultimate = {
            cooldown = {45, 45, 45, 45},
            damage = {30, 90, 180, 270},
            damage_type = DAMAGE_TRUE,
            radius = 80,
            duration = {5, 6, 7, 8},
        }
    },
    -- NYRU
    hero_muyrn = {
        stats = {
            hp = 1,
            armor = 0,
            damage = 7,
            cooldown = 7,
        },
        dead_lifetime = 30,
        speed = 75,
        distance_to_treewalk = 150,
        treewalk_speed = 95,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {125, 135, 145, 155, 165, 175, 185, 195, 205, 215},
        regen_health = {10, 11, 12, 12, 13, 14, 15, 16, 16, 17},
        basic_melee = {
            cooldown = 1.0,
            xp_gain_factor = 2.0,
            damage_max = {10, 11, 12, 13, 14, 16, 17, 18, 19, 20},
            damage_min = {6, 7, 8, 9, 10, 10, 11, 12, 13, 14},
        },
        basic_ranged = {
            min_range = 68,
            max_range = 180,
            cooldown = 1.5,
            xp_gain_factor = 1.52,
            damage_max = {27, 30, 33, 36, 39, 42, 45, 47, 50, 53},
            damage_min = {9, 10, 11, 12, 13, 14, 15, 16, 17, 18},
            damage_type = DAMAGE_MAGICAL,
        },
        sentinel_wisps = {
            cooldown = {25, 25, 25},
            max_summons = {1, 2, 3},
            max_range_trigger = 400/2,
            min_targets = 1,
            wisp = {
                cooldown = 1,
                duration = {6, 6, 6},
                shoot_range = 150,
                hero_max_distance = 100,
                damage_min = {2, 5, 8},
                damage_max = {4, 10, 16},
                damage_type = DAMAGE_MAGICAL,
            },
            xp_gain = {25, 50, 75},
        },
        verdant_blast = {
            cooldown = {30, 30, 30},
            damage_max = {120, 240, 360},
            damage_min = {120, 240, 360},
            s_damage = {120, 240, 360},
            damage_type = DAMAGE_MAGICAL,
            max_range = 300,
            min_range = 100,
            xp_gain = {30, 60, 90},
        },
        leaf_whirlwind = {
            cooldown = {35, 35, 35},
            duration = {8, 8, 8},
            max_range_trigger = 60,
            min_targets = 1,
            radius = 50,
            damage_type = DAMAGE_MAGICAL,
            damage_every = 0.25,
            damage_max = {4, 8, 12},
            damage_min = {2, 4, 6},
            s_damage_min = {8, 16, 24},
            s_damage_max = {16, 32, 56},
            heal_every = 0.25,
            heal_max = {2, 3, 4},
            heal_min = {2, 3, 4},
            xp_gain = {40, 80, 120},
        },
        faery_dust = {
            cooldown = {20, 20, 20},
            duration = {5, 6, 8},
            damage_factor = {0.4, 0.25, 0.1},
            s_damage_factor = {0.6, 0.75, 0.9},
            max_range_trigger = 320/2,
            max_range_effect= 360/2,
            min_targets = 3,
            radius = 240/3,
            xp_gain = {25, 50, 75}
        },
        ultimate = {
            cooldown = {45, 45, 45, 45},
            slow_factor = {0.6, 0.6, 0.6, 0.6},
            roots_count = {10, 15, 20, 25},
            radius = 60,
            duration = {4, 5, 6, 7},
            damage_every = 0.25,
            damage_type = DAMAGE_TRUE,
            damage_min = {4, 5, 6, 7},
            damage_max = {6, 7, 9, 11},
            s_damage_min = {16, 20, 24, 27},
            s_damage_max = {24, 28, 36, 44},
        }
    },
    hero_vesper= {
        stats = {
            hp = 3,
            armor = 4,
            damage = 5,
            cooldown = 8,
        },
        dead_lifetime = 30,
        speed = 75,
        regen_cooldown = 1,
        armor = {0, 0.04, 0.08, 0.12, 0.16, 0.20, 0.24, 0.28, 0.34, 0.40},
        hp_max = {160, 175, 190, 205, 220, 235, 250, 265, 280, 300},
        regen_health = {12, 13, 14, 15, 16, 17, 18, 19, 20, 21},
        basic_melee = {
            cooldown = 1,
            xp_gain_factor = 2.0,
            damage_max = {10, 11, 13, 14, 16, 17, 19, 20, 22, 23},
            damage_min = {6, 7, 8, 9, 10, 11, 12, 13, 14, 15},
        },
        basic_ranged_short = {
            min_range = 70,
            max_range = 150,
            cooldown = 1,
            xp_gain_factor = 2.0,
            damage_max = {10, 11, 13, 14, 16, 17, 19, 20, 22, 23},
            damage_min = {6, 7, 8, 9, 10, 11, 12, 13, 14, 15},
        },
        basic_ranged_long = {
            min_range = 150,
            max_range = 200,
            cooldown = 2,
            xp_gain_factor = 2.0,
            damage_max = {14, 17, 19, 22, 24, 26, 29, 31, 34, 36},
            damage_min = {10, 11, 13, 14, 16, 18, 19, 21, 22, 24},
        },
        arrow_to_the_knee = {
            cooldown = {14, 12, 10},
            damage_min = {40, 70, 90},
            damage_max = {40, 70, 90},
            s_damage = {40, 70, 90},
            stun_duration = {0.5, 0.75, 1},
            min_range = 135/2,
            max_range = 450/2,
            xp_gain = {14, 24, 30},
        },
        ricochet = {
            cooldown = {20, 20, 20},
            damage_min = {35, 35, 35},
            damage_max = {35, 35, 35},
            s_damage = {35, 35, 35},
            bounces = {2, 4, 7},
            s_bounces = {3, 5, 8},
            min_range = 70,
            max_range = 180,
            max_range_trigger = 400/2,
            min_targets = 3,
            bounce_range = 120,
            damage_type = DAMAGE_PHYSICAL,
            xp_gain = {20 , 40, 60},
        },
        martial_flourish = {
            cooldown = {16, 16, 16},
            damage_min = {30, 60, 90},
            damage_max = {30, 60, 90},
            s_damage = {90, 180, 270},
            damage_type = DAMAGE_PHYSICAL,
            xp_gain = {16 , 32, 48},
        },
        disengage = {
            cooldown = {20, 20, 20},
            damage_min = {40, 80, 120},
            damage_max = {40, 80, 120},
            s_damage = {40, 80, 120},
            min_distance_from_end = 300,
            distance = 130,
            total_shoots = 3,
            hp_to_trigger = 0.4, -- percentage
            xp_gain = {20, 40, 60},
        },
        ultimate = {
            spread = {8, 10, 12, 14},  -- arrows count = nodes * 2
            s_spread = {16, 20, 24, 28},
            damage = {20, 25, 30, 35},
            duration = 0.8,
            node_prediction_offset = 0,
            enemies_range = 200/2,
            cooldown = {50, 50, 50, 50},
        }
    },
    hero_lumenir= {
        stats = {
            hp = 8,
            armor = 4,
            damage = 10,
            cooldown = 4,
        },
        dead_lifetime = 30,
        speed = 120,
        regen_cooldown = 1,
        armor = {0, 0.04, 0.08, 0.12, 0.16, 0.2, 0.24, 0.28, 0.32, 0.36},
        hp_max = {275, 300, 325, 350, 375, 400, 425, 450, 475, 500},
        regen_health = {22, 24, 26, 28, 30, 32, 34, 36, 38, 40},

        mini_dragon_death = {
            min_range = 68,
            max_range = 150,
            cooldown = 1,
            damage_type = DAMAGE_TRUE,
            damage_max = {12, 12, 12, 12, 24, 24, 24, 36, 36, 36},
            damage_min = {8, 8, 8, 8, 16, 16, 16, 24, 24, 24},
        },

        basic_ranged_shot = {
            min_range = 50,
            max_range = 240,
            damage_type = DAMAGE_TRUE,
            cooldown = 2,
            xp_gain_factor = 1.46,
            damage_max = {26, 31, 36, 42, 47, 52, 57, 62, 68, 74},
            damage_min = {14, 17, 20, 22, 25, 28, 31, 34, 36, 38},
        },
        fire_balls = {
            min_range = 50,
            max_range = 250,
            cooldown = {20, 20, 20},
            damage_type = DAMAGE_TRUE,
            min_targets = 3,
            xp_gain = {20, 40, 60},
            flames_count = {5, 5, 5},
            flame_damage_min = {3, 6, 10},
            flame_damage_max = {5, 10, 14},
            damage_rate = 0.5,
            damage_radius = 90/2,
            duration = 8, 
        },
        mini_dragon = {
            min_range = 68,
            max_range = 150,
            cooldown = {30, 30, 30},
            damage_type = DAMAGE_TRUE,
            xp_gain = {30, 60, 90},
            dragon = {
                max_speed = 75,
                duration = {10, 12, 15},
                ranged_attack = {
                    cooldown = 1,
                    min_range = 10, 
                    max_range = 120, 
                    damage_type = DAMAGE_PHYSICAL,
                    damage_min = {10, 16, 19},
                    damage_max = {14, 24, 29},
                }
            },
        },
        celestial_judgement = {
            stun_range = 40,
            range = 300,
            stun_duration = {2,2,2},
            cooldown = {35, 35, 35},
            damage = {180, 360, 540},
            damage_type = DAMAGE_TRUE,
            xp_gain = {35, 70, 105},
        },

        shield = {
            spiked_armor = {0.20, 0.40, 0.60},
            armor = {0.10, 0.20, 0.30},
            duration = {8, 8, 8},
            cooldown = {20, 20, 20},
            xp_gain = {20, 40, 60},
            min_targets = 2,
            range = 300,
        },

        ultimate = {
            max_attack_count = 2, --number of sword hits
            damage_max = {19, 29, 48, 77},
            damage_min = {13, 19, 32, 51},
            soldier_count ={3, 3, 3, 3},
            cooldown = {30, 30, 30, 30},
            damage_type = DAMAGE_TRUE,
            stun_range = 40,
            stun_duration = 1,
            stun_target_duration = 5,
            range = 200,
        }
    },
    hero_raelyn = {
        stats = {
            hp = 6,
            armor = 5,
            damage = 6,
            cooldown = 4,
        },
        dead_lifetime = 30,
        speed = 50,
        regen_cooldown = 1,
        armor = {0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50},
        hp_max = {220, 240, 260, 280, 300, 320, 340, 360, 380, 400},
        melee_damage_max = {14, 17, 19, 22, 24, 26, 29, 31, 34, 36},
        melee_damage_min = {10, 11, 13, 14, 16, 18, 19, 21, 22, 24},
        regen_health = {18, 19, 21, 22, 24, 26, 27, 29, 30, 32},
        basic_melee = {
            cooldown = 2.0,
            xp_gain_factor = 2.6,
        },
        unbreakable = {
            cooldown = {35, 35, 35},
            duration = {6, 6, 6},
            shield_base = {0, 0, 0},
            shield_per_enemy = {0.1, 0.2, 0.3},
            max_range_trigger = 72,
            max_range_effect = 140,
            min_targets = 2,
            max_targets = 4,
            xp_gain = {35, 70, 105},
        },
        inspire_fear = {
            cooldown = {30, 30, 30},
            damage_duration = {6, 6, 6},
            stun_duration = {2, 2, 2},
            inflicted_damage_factor = {0.6, 0.4, 0.2},
            s_inflicted_damage_factor = {0.4, 0.6, 0.8},
            max_range_trigger = 72,
            max_range_effect = 120,
            min_targets = 2,
            xp_gain = {30, 60, 90},
        },
        brutal_slash = {
            cooldown = {30, 30, 30},
            damage_max = {160, 320, 480},
            damage_min = {160, 320, 480},
            s_damage = {160, 320, 480},
            damage_type = DAMAGE_TRUE,
            xp_gain = {30 , 60, 90},
        },
        onslaught = {
            cooldown = {24, 20, 18},
            melee_cooldown = {1, 1, 1},
            duration = {6, 8, 10},
            damage_type = DAMAGE_PHYSICAL,
            damage_factor = {0.5, 0.75, 1},
            radius = 180/2,
            xp_gain_factor = 4.0,
            max_range_trigger = 150,
            min_targets = 2,
        },
        ultimate = {
            cooldown = {60, 60, 60, 60},
            entity = {
                speed = {60, 60, 60, 60},
                cooldown = {1.5, 1.5, 1.5, 1.5},
                damage_min = {8, 12, 16, 22},
                damage_max = {12, 18, 24, 34},
                damage_type = DAMAGE_TRUE,
                hp_max = {120, 200, 280, 360},
                regen_health = {5, 8, 12, 15},
                armor = {0.2, 0.3, 0.45, 0.6},
                range = 72,
                duration = 20
            },
        }
    },
    -- TORRES
    hero_builder = {
        stats = {
            hp = 7,
            armor = 2,
            damage = 4,
            cooldown = 4,
        },
        dead_lifetime = 30,
        speed = 50,
        regen_cooldown = 1,
        hp_max = {250, 280, 310, 340, 370, 400, 430, 460, 490, 520},
        armor = {0, 0, 0, 0, 0, 0.05, 0.10, 0.15, 0.20, 0.25},
        regen_health = {15, 22, 25, 27, 30, 32, 34, 37, 39, 42},
        melee_damage_max = {16, 19, 21, 23, 24, 26, 28, 30, 32, 34},
        melee_damage_min = {10, 12, 14, 15, 16, 18, 19, 20, 21, 22},
        basic_melee = {
            cooldown = 2,
            xp_gain_factor = 2.6,
        },
        overtime_work = {
            cooldown = {18, 18, 18},
            max_range = 120,
            min_targets = 2,
            xp_gain = {18, 36, 54},
            soldier = {
                hp_max = {75, 100, 125},
                armor = 0.15,
                max_speed = 60,
                duration = 12,
                melee_attack = {
                    cooldown = 1,
                    damage_min = {2, 4, 6},
                    damage_max = {4, 8, 12},
                    range = 80,
                }
            }
        },
        lunch_break = {
            cooldown = {30, 28, 26},
            lost_health = 0.4,
            heal_hp = {80, 120, 200},
            xp_gain = {30 , 60, 90},
        },

        demolition_man = {
            cooldown = {16, 16, 16},
            duration = {1.25, 1.25, 1.25},
            max_range = 75,
            damage_type = DAMAGE_PHYSICAL,
            damage_every = 0.1,
            damage_min = {4, 8, 12},
            damage_max = {6, 12, 18},
            s_damage_min = {28, 56, 84},
            s_damage_max = {42, 84, 126},
            radius = 100,
            min_targets = 2,
            xp_gain = {16, 32, 48},
        },

        defensive_turret = {
            cooldown = {25, 25, 25},
            duration = {15, 15, 15},
            xp_gain = {25, 50, 75},
            max_range = 180,
            min_targets = 2,
            build_speed = 42*4,
            attack = {
                range = 160,
                cooldown = {0.8, 0.8, 0.8},
                damage_min = {8, 16, 24},
                damage_max = {12, 24, 36},
            }
        },

        ultimate = {
            cooldown = {50, 50, 50, 50},
            stun_duration = {2, 2, 2, 2},
            damage = {100, 160, 240, 320},
            radius = 80,
            damage_type = DAMAGE_PHYSICAL,
        }
    },
    -- ONAGRO
    hero_mecha = {
        stats = {
            hp = 4,
            armor = 6,
            damage = 8,
            cooldown = 6,
        },
        dead_lifetime = 30,
        speed = 42,
        regen_cooldown = 1,
        hp_max = {200, 210, 220, 230, 240, 250, 260, 270, 280, 290},
        armor = {0.10, 0.16, 0.22, 0.28, 0.34, 0.40, 0.46, 0.52, 0.58, 0.64},
        regen_health = {16, 17, 18, 18, 19, 20, 21, 22, 22, 23},
        basic_ranged = {
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {17, 19, 22, 24, 26, 29, 31, 34, 37, 41},
            damage_min = {11, 13, 14, 16, 18, 19, 21, 22, 24, 27},
            min_range = 0,
            max_range = 250,
            cooldown = 2.0,
            xp_gain_factor = 2.35,
        },
        goblidrones = {
            cooldown = {25, 25, 25},
            units = 2,
            spawn_range = 110, -- range to detect nearby enemies to trigger the spawn
            min_targets = 1,
            drone = {
                max_speed = 60,
                duration = {8, 8, 8},
                ranged_attack = {
                    cooldown = 1,
                    min_range = 10, -- from onagro
                    max_range = 100, -- from onagro
                    damage_type = DAMAGE_PHYSICAL,
                    damage_min = {3, 6, 10},
                    damage_max = {5, 10, 14},
                }
            },
            xp_gain = {25, 50, 75},
        },
        tar_bomb = {
            cooldown = {30, 28, 26},
            duration = {5, 6, 7},
            min_range = 50,
            max_range = 200,
            min_targets = 1,
            node_prediction = 60,
            slow_factor = 0.5,
            xp_gain = {30, 56, 78},
        },
        power_slam = {
            cooldown = {20, 20, 20},
            damage_type = DAMAGE_PHYSICAL,
            damage_radius = 170/2,
            damage_max = {26, 52, 78},
            damage_min = {26, 52, 78},
            s_damage = {26, 52, 78},
            min_targets = 3,
            stun_time = {30, 30, 30}, -- in frames
            xp_gain = {20, 40, 60},
        },
        mine_drop = {
            cooldown = {10, 8, 6},
            max_mines = {2, 3, 4},
            min_range = 35,
            max_range = 80,
            damage_radius = 150/2,
            damage_max = {24, 36, 48},
            damage_min = {16, 24, 32},
            damage_type = DAMAGE_EXPLOSION,
            min_dist_between_mines = 30,
            xp_gain = {10, 16, 18},
        },
        ultimate = {
            cooldown = {45, 45, 45, 45},
            speed_out_of_range = 200,
            speed_in_range = 30,
            attack_radius = 125, -- dist to the trigger pos where the zeppelin can attack
            ranged_attack = {
                cooldown = 0.5,
                min_range = 10,
                max_range = 200,
                damage_type = DAMAGE_TRUE,
                damage_radius = 100/2,
                damage_max = {60, 72, 84, 96},
                damage_min = {40, 48, 56, 64},
            }
        }
    },
    --GRIMSON
    hero_venom = {
        stats = {
            hp = 5,
            armor = 0,
            damage = 6,
            cooldown = 8,
        },
        dead_lifetime = 30,
        speed = 75,
        distance_to_slimewalk = 150,
        slimewalk_speed = 95,
        regen_cooldown = 1,
        hp_max = {180, 205, 230, 255, 280, 305, 330, 355, 380, 400},
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        regen_health = {14, 16, 18, 20, 22, 24, 26, 28, 30, 32},
        shared_cooldown = 3,
        basic_melee = {
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {12, 14, 16, 17, 19, 21, 23, 24, 26, 28},
            damage_min = {8, 9, 10, 12, 13, 14, 15, 16, 18, 20},
            cooldown = 1.0,
            xp_gain_factor = 1.55,
        },
        ranged_tentacle = {
            cooldown = {10, 10, 10},
            min_range = 50,
            max_range = 150,
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {12, 36, 60},
            damage_min = {12, 36, 60},
            s_damage = {12, 36, 60},
            node_prediction = 60,
            bleed_chance = {0.6, 0.8, 1},
            bleed_damage_min = {2, 2, 2},
            bleed_damage_max = {2, 2, 2},
            s_bleed_damage = 8,
            bleed_every = {0.25, 0.25, 0.25}, -- seconds
            bleed_duration = {4, 4, 4}, -- seconds
            xp_gain = {10, 20, 30},
        },
        inner_beast = {
            cooldown = {40, 40, 40},
            trigger_hp = 0.3,
            duration = 8,
            basic_melee = {
                damage_type = DAMAGE_PHYSICAL,
                damage_factor = {1.4, 1.6, 1.8},
                s_damage_factor = {0.4, 0.6, 0.8},
                cooldown = 2.5,
                regen_health = 0.08,
                xp_gain_factor = 1.0,
            },
            xp_gain = {0, 0, 0},
        },
        floor_spikes = {
            cooldown = {30, 30, 30},
            damage_type = DAMAGE_TRUE,
            damage_radius = 70/2,
            damage_max = {18, 38, 58},
            damage_min = {18, 38, 58},
            s_damage = {18, 38, 58},
            range_trigger_min = 20,
            range_trigger_max = 120,
            spikes = {15, 15, 15}, -- must be multiple of 3
            min_targets = 3,
            xp_gain = {30, 60, 90},
        },
        eat_enemy = {
            cooldown = {38, 34, 30},
            damage_type = DAMAGE_INSTAKILL,
            hp_trigger = 0.2,
            regen = {0.25, 0.4, 0.6},
            xp_gain = {40, 70, 90},
        },
        ultimate = {
            cooldown = {50, 50, 50, 50},
            duration = {3, 3, 3, 3},
            slow_delay = 0.5,
            slow_factor = 0.5,
            radius = 140/2,
            damage_type = DAMAGE_TRUE,
            damage_max = {150, 200, 250, 300},
            damage_min = {150, 200, 250, 300},
            s_damage = {150, 200, 250, 300},
        }
    },
    -- WARHEAD
    hero_robot = {
        stats = {
            hp = 4,
            armor = 6,
            damage = 6,
            cooldown = 8,
        },
        dead_lifetime = 30,
        speed = 42,
        distance_to_flywalk = 150,
        flywalk_speed = 110,
        regen_cooldown = 1,
        hp_max = {200, 215, 230, 245, 260, 275, 290, 305, 320, 335},
        armor = {0.15, 0.21, 0.27, 0.33, 0.39, 0.45, 0.51, 0.57, 0.63, 0.69},
        regen_health = {16, 17, 18, 20, 21, 22, 23, 24, 26, 27},
        shared_cooldown = 3,
        basic_melee = {
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {10, 11, 12, 13, 14, 16, 17, 18, 20, 23},
            damage_min = {6, 7, 8, 9, 10, 10, 11, 12, 13, 15},
            cooldown = 1.0,
            xp_gain_factor = 2.2,
        },
        jump = {
            cooldown = {15, 15, 15},
            min_range = 0, -- to jump
            max_range = 100, -- to jump
            radius = 60, -- for stun aura
            stun_duration = {2, 2, 2},
            damage_radius = 100,
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {15, 30, 60},
            damage_min = {15, 30, 60},
            s_damage = {15, 30, 60},
            xp_gain = {15, 30, 45},
        },
        fire = {
            cooldown = {25, 25, 25},
            xp_gain = {25, 50, 75},
            min_range = 0,
            max_range = 180,
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {42, 84, 132},
            damage_min = {28, 56, 88},
            damage_radius = 30,
            slow_factor = 0.5,
            smoke_duration = {5, 5, 5},
            slow_duration = {1, 1, 1},
            s_slow_duration = {5, 5, 5},
            min_targets = 3,
        },
        uppercut = {
            life_threshold = {25, 30, 40}, -- % life to trigger
            s_life_threshold = {0.25, 0.3, 0.4},
            cooldown = {34, 30, 26},
        },
        explode = {
            cooldown = {20, 20, 20},
            damage_max = {36, 72, 108},
            damage_min = {12, 24, 36},
            damage_type = DAMAGE_EXPLOSION,
            damage_radius = 80,
            xp_gain = {20, 40, 60},
            min_range = 0,
            max_range = 90,
            burning_duration = 4,
            burning_damage_type = DAMAGE_TRUE,
            burning_damage_min = {1, 2, 3},
            burning_damage_max = {1, 2, 3},
            s_burning_damage = {1, 2, 3},
            damage_every = 0.25,
            min_targets = 3
        },
        ultimate = {
            cooldown = {50, 50, 50, 50},
            duration = 4,
            speed = 200,
            radius = 70,
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {40, 80, 160, 260},
            damage_min = {40, 80, 160, 260},
            s_damage = {40, 80, 160, 260},
            burning_duration = 4,
            burning_damage_min = {1, 1, 1, 1},
            burning_damage_max = {1, 1, 1, 1},
            s_burning_damage = 4,
            burning_damage_type = DAMAGE_TRUE,
            damage_every = 0.25,
        }
    },
    -- ANYA
    hero_hunter = {
        stats = {
            hp = 4,
            armor = 2,
            damage = 7,
            cooldown = 9,
        },
        dead_lifetime = 30,
        speed = 75,
        distance_to_flywalk = 150,
        flywalk_speed = 95,
        regen_cooldown = 1,
        hp_max = {150, 165, 180, 195, 210, 225, 240, 255, 270, 280},
        armor = {0, 0.05, 0.05, 0.1, 0.1, 0.15, 0.15, 0.2, 0.2, 0.25},
        regen_health = {14, 16, 17, 18, 19, 20, 22, 23, 24, 25},
        shared_cooldown = 3,
        basic_melee = {
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {6, 6, 7, 8, 8, 9, 10, 11, 11, 12},
            damage_min = {3, 4, 5, 5, 6, 6, 7, 7, 8, 8},
            cooldown = 0.8,
            xp_gain_factor = 1.45,
        },
        basic_ranged = {
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {24, 29, 35, 40, 45, 50, 56, 61, 66, 70},
            damage_min = {16, 20, 23, 27, 30, 34, 37, 41, 44, 48},
            max_range = 240,
            min_range = 80,
            cooldown = 2.5,
            xp_gain_factor = 1.8,
        },
        heal_strike = {
            damage_type = DAMAGE_TRUE,
            damage_max = {20, 32, 40},
            damage_min = {14, 22, 28},
            heal_factor = {0.1, 0.15, 0.2}, -- percentage of enemy total life
            xp_gain = {5, 10, 15},
        },
        ricochet = {
            cooldown = {15, 15, 15},
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {34, 60, 82},
            damage_min = {22, 40, 56},
            bounces = {2, 2, 2},
            s_bounces = {3, 3, 3},
            xp_gain = {15, 30, 45},
            max_range_trigger = 200,
            min_targets = 3,
            bounce_range = 120,
        },
        shoot_around = {
            cooldown = {20, 20, 20},
            max_range = 78,
            min_targets = 3,
            damage_type = DAMAGE_TRUE,
            damage_every = fts(3),
            radius = 80,
            damage_min = {1, 2, 3},
            damage_max = {3, 4, 5},
            s_damage_min = {22, 44, 66},
            s_damage_max = {66, 88, 110},
            duration = {3, 3, 3},
            xp_gain = {20, 40, 60},
        },
        beasts = {
            cooldown = {18, 18, 18},
            damage_min = {2, 3, 4},
            damage_max = {3, 6, 8},
            duration = {8, 8, 8},
            gold_to_steal = {2, 3, 4},
            chance_to_steal = 25,
            max_distance_from_owner = 200,
            attack_cooldown = 0.5,
            attack_range = 150,
            max_range = 100,
            damage_type = DAMAGE_PHYSICAL,
            xp_gain = {18, 36, 54},
        },
        ultimate = {
            cooldown = {50, 50, 50, 50},
            duration = 12,
            slow_radius = 80,
            slow_factor = 0.75,
            slow_duration = 0.5,
            distance_to_revive = 150,
            entity = {
                basic_ranged = {
                    cooldown = 1,
                    min_range = 0,
                    max_range = 150,
                    damage_min = {5, 10, 14, 20},
                    damage_max = {8, 14, 22, 30},
                    damage_type = DAMAGE_TRUE,
                },
            },
        }
    },
    -- KOSMYR
    hero_dragon_gem = {
        stats = {
            hp = 10,
            armor = 0,
            damage = 9,
            cooldown = 4,
        },
        dead_lifetime = 30,
        speed = 100,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {300, 330, 360, 390, 420, 450, 480, 510, 545, 580},
        regen_health = {24, 26, 29, 31, 34, 36, 38, 41, 43, 46},
        
        basic_ranged_shot = {
            min_range = 50,
            max_range = 220,
            damage_type = DAMAGE_PHYSICAL,
            cooldown = 2,
            xp_gain_factor = 1.82,
            damage_range = 75,
            damage_max = {18, 23, 26, 30, 34, 37, 41, 44, 49, 55},
            damage_min = {13, 15, 18, 20, 22, 25, 27, 30, 32, 35},
        },
        stun = {
            min_targets = 2,
            range = 180,
            duration = {2, 3, 4},
            cooldown = {16, 16, 16},
            xp_gain = {16, 32, 48},
            stun_radius = 80,
        },
        floor_impact = {
            cooldown = {25, 25, 25},
            xp_gain = {25, 50, 75},
            min_targets = 3,
            damage_type = DAMAGE_PHYSICAL,
            damage_min = {24, 48, 72},
            damage_max = {36, 72, 108},
            damage_range = 50,
            shards = 3,
            min_nodes_trigger = 0,
            max_nodes_trigger = 30,
            nodes_between_shards = 8,
        },
        crystal_instakill = {
            hp_max = {200, 400, 800},
            cooldown = {35, 35, 35},
            xp_gain = {35, 70, 105},
            damage_aoe_min = {32, 76, 96},
            damage_aoe_max = {32, 76, 96},
            s_damage = {32, 76, 96},
            max_range = 200,
            damage_range = 100,
            explode_time = fts(27),
        },
        crystal_totem = {
            min_targets = 2,
            max_range_trigger = 160,
            cooldown = {20, 20, 20},
            xp_gain = {20, 40, 60},
            aura_radius = 80,
            duration = {6, 8, 10},
            slow_factor = 0.75,
            s_slow_factor = 0.25,
            slow_duration = 1,
            damage_min = {8, 16, 25},
            damage_max = {8, 16, 25},
            s_damage = {8, 16, 25},
            trigger_every = fts(30),
        },
        passive_charge = {
            distance_threshold = 300,
            damage_factor = 3,
            shots_amount = 1,
        },
        ultimate = {
            damage_max = {72, 108, 132, 156},
            damage_min = {48, 72, 88, 104},
            damage_type = DAMAGE_TRUE,
            damage_range = 30,
            cooldown = {45, 45, 45, 45},
            range = 500,
            max_shards = {3, 4, 6, 8},
            distance_between_shards = 10,
            random_ni_spread = 30,
        }
    },
    --BRODEN
    hero_bird = {
        stats = {
            hp = 6,
            armor = 0,
            damage = 8,
            cooldown = 6,
        },
        dead_lifetime = 30,
        speed = 120,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {280, 300, 320, 340, 360, 380, 400, 420, 440, 460},
        regen_health = {22, 24, 26, 27, 29, 30, 32, 34, 35, 37},
        
        basic_attack = {
            cooldown = 1.5,
            xp_gain_factor = 1.7,
            min_range = 20,
            max_range = 200,
            damage_type = DAMAGE_EXPLOSION,
            damage_max = {18, 20, 23, 25, 27, 29, 32, 34, 37, 38},
            damage_min = {12, 14, 15, 17, 18, 20, 21, 23, 24, 26},
            damage_radius = 75,
        },
        cluster_bomb = {
            cooldown = {20, 20, 20},
            xp_gain = {20, 40, 60},
            min_targets = 2,
            min_range = 100,
            max_range = 220,
            explosion_damage_type = DAMAGE_EXPLOSION,
            explosion_damage_min = {8, 20, 32},
            explosion_damage_max = {8, 20, 32},
            explosion_damage_radius = 75,
            first_explosion_height = 150,
            fire_duration = {3, 5, 8}, -- in seconds
            fire_radius = 50, 
            burning = {
                damage = {1, 1, 1}, -- per tick
                damage_type = DAMAGE_TRUE,
                duration = 3,
                cycle_time = 0.25,  -- tick
                s_total_damage = 12, 
            },
        },
        shout_stun = {
            cooldown = {20, 18, 16},
            xp_gain = {20, 36, 48},
            min_targets = 2,
            radius = 100,
            stun_duration = {1, 1.5, 2},
            slow_duration = {3, 4, 6}, -- AFTER the stun has ended
            slow_factor = 0.5,
        },
        gattling = {
            cooldown = {12, 12, 12},
            xp_gain = {12, 24, 36},
            min_range = 60,
            max_range = 190,
            duration = {2, 2, 2},
            damage_min = {3, 5, 8},
            damage_max = {5, 8, 11},
            damage_type = DAMAGE_PHYSICAL,
            shoot_every = fts(4), -- synced with anim, do not change
            s_damage_min = {42, 70, 112},
            s_damage_max = {70, 112, 154},
        },
        eat_instakill = {
            cooldown = {25, 22, 20},
            xp_gain = {25, 44, 60},
            hp_max = {200, 400, 600},
            min_range = 0,
            max_range = 50,
        },
        ultimate = {
            cooldown = {35, 35, 35, 35},
            bird = {
                target_range = 180, -- distance to target the enemy
                chase_range = 250, -- max distance to continue chasing the enemy
                duration = {8, 10, 12, 15},
                melee_attack = {
                    cooldown = 0.1,
                    damage_max = {16, 25, 36, 50},
                    damage_min = {16, 25, 36, 50},
                    damage_type = DAMAGE_TRUE,
                    range = 15
                }
            }
        }
    },
    -- KRATOA
    hero_lava = {
        stats = {
            hp = 4,
            armor = 0,
            damage = 5,
            cooldown = 7,
        },
        dead_lifetime = 15,
        speed = 55,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {225, 235, 245, 255, 265, 275, 285, 295, 305, 315},
        melee_damage_max = {15, 17, 18, 20, 21, 23, 24, 26, 28, 32},
        melee_damage_min = {10, 11, 12, 13, 14, 15, 16, 17, 18, 20},
        regen_health = {18, 19, 20, 20, 21, 22, 23, 24, 25, 25},
        basic_melee = {
            cooldown = 1.25,
            xp_gain_factor = 1.8,
        },
        temper_tantrum = {
            cooldown = {15, 15, 15},
            duration = {2, 2, 2},
            s_damage_min = {56, 105, 162},
            s_damage_max = {84, 156, 240},
            damage_min = {19, 35, 54},
            damage_max = {28, 52, 80},
            damage_type = DAMAGE_PHYSICAL,
            stun_duration = 2,
            xp_gain = {15, 30, 45},
        },
        hotheaded = {
            cooldown = {5, 5, 5},
            s_damage_factors = {0.2,0.3,0.4},
            damage_factors = {1.2,1.3,1.4},
            durations = {6,6,6},
            xp_gain = {320, 450, 580},
            range = 180,
        },
        double_trouble = {
            cooldown = {20, 20, 20},
            min_range = 100,
            max_range = 150,
            s_damage = {30, 50, 75},
            damage_min = {30, 50, 75},
            damage_max = {30, 50, 75},
            damage_radius = 70,
            damage_type = DAMAGE_EXPLOSION,
            xp_gain = {20, 40, 60},
            soldier ={
                duration = 10,
                hp_max ={75, 100, 125},
                damage_min = {10, 13, 16},
                damage_max = {14, 19, 24},
                cooldown = 1,
                max_speed = 36,
                armor = 0,
            }
        },
        death_aura = {
            damage_radius = 70,
            cycle_time = 0.25,
            damage_min = 4,
            damage_max = 6,
            damage_type = DAMAGE_TRUE,
        },
        wild_eruption = {
            cooldown = {30, 30, 30},
            duration = {4, 4, 4},
            damage_min = {10, 12, 15},
            damage_max = {10, 12, 15},
            s_damage = {40, 48, 60},
            damage_type = DAMAGE_TRUE,
            radius = 180/2,
            loop_duration = 1.5,
            xp_gain = {30, 60, 90},
            --xp_gain_factor = 4.0,
            max_range_trigger = 60,
            max_range_effect = 100,
            damage_every = 0.25,
            min_targets = 3,
        },
        ultimate = {
            cooldown = {60, 60, 60, 60},
            max_spread = 30,
            fireball_count = {3,4,5,6},
            bullet = {
                s_damage = {40, 80, 130, 200},   
                damage_min = {40, 80, 130, 200},
                damage_max = {40, 80, 130, 200},
                damage_type = DAMAGE_TRUE,
                damage_radius = 120/2,
                scorch = {
                    duration = 4,
                    damage_min = {1, 1, 1, 1},
                    damage_max = {1, 1, 1, 1},
                    cycle_time = 0.25,
                    damage_radius = 120/2,
                    damage_type = DAMAGE_TRUE,
                }
            },
        },
        ultimate_combo = {
            min_radius = 100,
            max_radius = 400,
            max_targets = 12,
        }
    },
    -- STREGI
    hero_witch = {
        stats = {
            hp = 3,
            armor = 0,
            damage = 6,
            cooldown = 7,
        },
        dead_lifetime = 30,
        speed = 180,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {130, 145, 160, 175, 190, 205, 220, 235, 250, 265},
        melee_damage_max =  {10, 11, 13, 14, 15, 17, 18, 19, 21, 22},
        melee_damage_min =  {7, 8, 8, 9, 10, 11, 12, 13, 14, 15},
        ranged_damage_max = {10, 11, 13, 14, 15, 17, 18, 19, 21, 22},
        ranged_damage_min = {7, 8, 8, 9, 10, 11, 12, 13, 14, 15},
        regen_health = {12, 13, 14, 14, 15, 16, 17, 18, 19, 20},
        basic_melee = {
            cooldown = 1.0,
            xp_gain_factor = 2.2,
        },
        ranged_attack = {
            cooldown = 1.5,
            max_range = 200,
            min_range = 50,
            damage_type = DAMAGE_MAGICAL,
            xp_gain_factor = 1.7,
        },
        skill_soldiers = {
            cooldown = {18, 18, 18},
            max_range = 180,
            min_targets = 2,
            xp_gain = {18, 36, 54},
            soldiers_amount = {2, 3, 4}, -- max 4
            soldier = {
                hp_max = {40, 60, 80},
                armor = 0,
                max_speed = 60,
                duration = 8,
                melee_attack = {
                    cooldown = 1,
                    damage_min = {2, 4, 6},
                    damage_max = {3, 6, 9},
                    range = 100,
                }
            }
        },
        skill_polymorph = {
            cooldown = {20, 20, 20},
            hp_max = {400, 800, 1200},
            duration = {4, 6, 8},
            range = 200,
            xp_gain = {20, 40, 60},
            max_nodes_to_goal = 50,
            pumpkin = {
                armor = 0,
                magic_armor = 0,
                speed = 20,
                hp = {0.75, 0.6, 0.4},
            },
        },
        skill_path_aoe = {
            cooldown = {16, 16, 16},
            duration = {5, 6, 8},
            min_range = 75,
            max_range = 180,
            min_targets = 3,
            node_prediction = 30,
            slow_factor = 0.5,
            xp_gain = {16, 32, 48},
            damage_min = {50, 80, 120},
            damage_max = {50, 80, 120},
            s_damage = {50, 80, 120},
            damage_type = DAMAGE_MAGICAL,
        },
        disengage = {
            cooldown = {18, 18, 18},
            min_distance_from_end = 300,
            distance = 160,
            hp_to_trigger = 0.4, -- percentage
            xp_gain = {20, 40, 60},
            decoy = {
                hp_max = {50, 75, 100},
                armor = 0,
                max_speed = 60,
                duration = 8,
                melee_attack = {
                    cooldown = 1,
                    damage_min = {8, 12, 16},
                    damage_max = {12, 18, 24},
                    range = 80,
                },
                explotion = {
                    radius = 45,
                    stun_duration = {2, 2.5, 3}
                }
            }
        },
        ultimate = {
            cooldown = {35, 35, 35, 35},
            radius = 75,
            duration = {2, 3, 4, 5},
            max_targets = {4, 6, 8, 10},
            nodes_teleport = 50, -- in nodes
            nodes_limit = 20,
        }
    },
    -- BONEHART
    hero_dragon_bone = {
        stats = {
            hp = 9,
            armor = 0,
            damage = 10,
            cooldown = 6,
        },
        dead_lifetime = 30,
        speed = 130,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {315, 340, 365, 390, 415, 440, 465, 490, 515, 540},
        regen_health = {25, 27, 29, 31, 33, 35, 37, 39, 41, 43},
        basic_attack = {
            cooldown = 2,
            min_range = 50,
            max_range = 220,
            radius = 120/2,
            damage_type = DAMAGE_TRUE,
            damage_max = {17, 20, 24, 27, 30, 34, 37, 40, 44, 46},
            damage_min = {11, 13, 16, 18, 20, 22, 25, 27, 29, 32},
            xp_gain_factor = 1.4,
        },
        plague = {
            damage_max = 1,
            damage_min = 1,
            duration = 4,
            every = 0.25,
            explotion = {
                damage_type = DAMAGE_EXPLOSION,
                damage_max = 20,
                damage_min = 10,
                damage_radius = 120/2,
            }
        },
        cloud = {
            cooldown = {15, 15, 15},
            min_range = 150, -- careful! the animation doesn't look good with range lower than 125~150 
            max_range = 225,
            radius = 200 / 2,
            min_targets = 3,
            duration = {4, 6, 10},
            slow_factor = 0.5,
            xp_gain = {15, 30, 45},
        },
        nova = {
            cooldown = {24, 24, 24},
            min_range = 0,
            max_range = 75,
            min_targets = 3,
            damage_type = DAMAGE_EXPLOSION,
            damage_max = {40, 118, 156},
            damage_min = {20, 62, 84},
            damage_radius = 75,
            xp_gain = {24, 48, 72},
        },
        rain = {
            cooldown = {20, 20, 20},
            min_range = 50,
            max_range = 200,
            min_targets = 3,
            damage_max = {18, 36, 54},
            damage_min = {12, 24, 36},
            damage_type = DAMAGE_TRUE,
            stun_time = 0.25,
            bones_count = {3, 5, 8},
            xp_gain = {20, 40, 50},
        },
        burst = {
            cooldown = {32, 32, 32},
            min_range = 0,
            max_range = 300,
            min_targets = 5,
            damage_max = {36, 86, 130},
            damage_min = {24, 58, 86},
            damage_type = DAMAGE_TRUE,
            proj_count = {6, 8, 10},
            xp_gain = {30, 60, 90},
        },
        ultimate = {
            cooldown = {45, 45, 45, 45},
            dog = {
                cooldown = 1,
                duration = {10, 15, 20, 25},
                armor = 0,
                hp = {100, 120, 150, 180},
                speed = 95,
                melee_attack = {
                    cooldown = 1,
                    damage_type = DAMAGE_PHYSICAL,
                    damage_max = {14, 17, 22, 31},
                    damage_min = {10, 11, 14, 21},
                },
            },
        }
    },
    hero_dragon_arb = {
        stats = {
            hp = 7,
            armor = 4,
            damage = 8,
            cooldown = 10,
        },
        dead_lifetime = 30,
        speed = 130,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        magic_armor = {0.03, 0.06, 0.09, 0.12, 0.15, 0.18, 0.21, 0.24, 0.27, 0.3},
        hp_max = {195, 220, 245, 270, 295, 320, 345, 370, 395, 420},
        regen_health = {24, 27, 30, 33, 36, 39, 42, 45, 48, 51},
        passive_plant_zones = {
            zone_duration = 25,
            radius = 15,
            slow_factor = 0.5,
            expansion_cooldown = 0.3,
        },
        basic_breath_attack = {
            min_range = 10,
            max_range = 300,
            damage_type = DAMAGE_MAGICAL,
            cooldown = 1.75,
            xp_gain_factor = 1.82,
            damage_max = {15, 19, 23, 27, 32, 36, 40, 44, 49, 53},
            damage_min = {10, 13, 15, 18, 21, 24, 27, 29, 32, 35},
        },
        arborean_spawn = {
            min_targets = 2,
            max_targets = 3,
            cooldown = {35, 35, 35},
            xp_gain = {35, 70, 105},
            spawn_max_range_to_enemy = 200,
            min_range = 0,
            max_range = 10000,
            arborean = {
                armor = 0,
                magic_armor = 0,
                hp = {80, 110, 140},
                speed = 30,
                duration = {10, 12, 14},
                basic_attack = {
                    cooldown = 2,
                    damage_type = DAMAGE_PHYSICAL,
                    damage_max = {4, 6, 8},
                    damage_min = {2, 3, 5},
                },
            },
            paragon = {
                armor = 0,
                magic_armor = 0,
                hp = {120, 160, 200},
                speed = 40,
                duration = {10, 12, 14},
                basic_attack = {
                    cooldown = 1,
                    damage_type = DAMAGE_PHYSICAL,
                    damage_max = {12, 16, 20},
                    damage_min = {8, 12, 16},
                },
            },
        },
        tower_runes = {
            cooldown = {35, 35, 35},
            xp_gain = {35, 70, 105},
            min_range = 0,
            max_range = 300,
            max_targets = {3, 3, 3},
            duration = {6, 7, 8},
            damage_factor = {1.3, 1.45, 1.6},
            s_damage_factor = {0.3, 0.45, 0.6}
        },
        thorn_bleed = {
            damage_type = DAMAGE_MAGICAL,
            xp_gain = {35, 70, 105},
            damage_speed_ratio = {0.375, 0.564, 0.825},
            damage_every = 0.75,
            cooldown = {12, 10, 8},
            instakill_chance = {0.3, 0.3, 0.3},
            duration = {5, 5, 5},
        },
        tower_plants = {
            cooldown = {20, 20, 20},
            xp_gain = {22, 44, 66},
            min_range = 0,
            max_range = 300,
            max_targets = {1, 2, 3},
            duration = {8, 10, 12}, -- duration of plants
            linirea = {
                cooldown_min = 0.5,
                cooldown_max = 0.5,
                range = 240,
                heal_duration = 1.5,
                heal_max = {15, 15, 15},
                heal_min = {15, 15, 15},
                heal_every = 0.2
            },
            dark_army = {
                cooldown_min = 2,
                cooldown_max = 2,
                range = 120,
                slow_factor = {0.5, 0.4, 0.3}, 
                damage_type = DAMAGE_MAGICAL,
                damage_every = 0.25,
                damage_min = {5, 7, 9},
                damage_max = {5, 7, 9}
            },
        },
        ultimate = {
            duration = {8, 10, 13, 15},
            cooldown = {45, 45, 45, 45},
            extra_armor = {0.3, 0.4, 0.5, 0.6},
            extra_magic_armor = {0.3, 0.4, 0.5, 0.6},
            speed_factor = {1.3, 1.4, 1.5, 1.6},
            inflicted_damage_factor = {1.3, 1.4, 1.5, 1.6},

            s_bonuses = {0.3, 0.4, 0.5, 0.6}
        }
    },

    hero_spider = {
        stats = {
            hp = 3,
            armor = 0,
            damage = 6,
            cooldown = 8,
        },
        dead_lifetime = 25,
        speed = 90,
        teleport_min_distance = 220,
        tp_delay = 0.4,
        tp_duration = 1,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {180, 190, 200, 210, 220, 230, 240, 250, 260, 270},
        regen_health = {13, 14, 14, 15, 16, 17, 18, 18, 19, 20},
        shared_cooldown = 3,
        basic_melee = {
            cooldown = 1,
            xp_gain_factor = 2.36,
            damage_max = {11,13,15,17,19,21,23,25,27,30},
            damage_min = {9,10,12,14,16,17,19,21,22,24},
            damage_type = DAMAGE_PHYSICAL,
            dot = {
                poison_radius = 80,
                poison_damage_min = {2,2,2,2,3,3,3,4,4,4},
                poison_damage_max = {2,2,2,2,3,3,3,4,4,4},
                poison_damage_every = 0.25,
                poison_mod_duration = 1.5,
                damage_type = DAMAGE_PHYSICAL,
            },
        },
        basic_ranged = {
            min_range = 68,
            max_range = 160,
            cooldown = 1.5,
            xp_gain_factor = 1.5,
            damage_max = {20, 22, 25, 28, 31, 34, 37, 40, 44, 48},
            damage_min = {11, 12, 14, 15, 17, 18, 20, 22, 24, 26},
            damage_type = DAMAGE_MAGICAL,
        },
        instakill_melee = {
            life_threshold = {500, 750, 1000}, -- max life to trigger
            use_current_health_instead_of_max = true,
            cooldown = {7, 6, 5},
            xp_gain = {20, 40, 60},
        },
        area_attack = {
            cooldown = {18, 16, 14},
            damage_type = DAMAGE_PHYSICAL,
            damage_radius = 170/2,
            damage_max = {0, 0, 0},
            damage_min = {0, 0, 0},
            s_damage = {0, 0, 0},
            min_targets = 3,
            stun_time = {90, 120, 150}, -- in frames
            s_stun_time = {3, 4, 5},
            xp_gain = {18, 34, 48},
        },
        tunneling = {
            damage_type = DAMAGE_PHYSICAL,
            damage_radius = 170/2,
            damage_max = {36, 54, 84},
            damage_min = {24, 36, 56},
            s_damage = {36, 54, 84},
            min_targets = 1,
            xp_gain = {7, 7, 7},
        },
        supreme_hunter = {
            cooldown = {25, 25, 25},
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {84, 168, 252},
            damage_min = {56, 112, 168},
            s_damage = {50, 80, 115},
            min_targets = 1,
            xp_gain = {25, 50, 75},
        },
        ultimate = {
            cooldown = {35, 35, 35, 35},
            spawn_amount = {2, 3, 4, 5}, -- min: 1 max: 5
            spider = {
                cooldown = 1,
                duration = {5, 7, 8, 10},
                armor = 0,
                hp = {150, 150, 150, 150},
                speed = 95,
                stun_duration = 3,
                stun_chance = 0.15,
                melee_attack = {
                    cooldown = 1,
                    damage_type = DAMAGE_PHYSICAL,
                    damage_max = {12, 12, 12, 12},
                    damage_min = {8, 8, 8, 8},
                },
            },
        }
    },
    hero_wukong = {
        stats = {
            hp = 5,
            armor = 0,
            damage = 8,
            cooldown = 5,
        },
        dead_lifetime = 20,
        speed = 80,
        teleport_min_distance = 250,
        tp_delay = 0.4,
        tp_duration = 1,
        regen_cooldown = 1,
        armor = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
        hp_max = {200, 220, 240, 260, 280, 300, 320, 340, 360, 380},
        regen_health = {13, 14, 14, 15, 16, 17, 18, 18, 19, 20},
        shared_cooldown = 3,
        distance_to_flywalk = 150,
        flywalk_speed_mult = 2.2,
        melee_attacks = {
            cooldown = 1.0, -- all attacks share cooldown and are selected randomly
            can_repeat_attack = false,
            spin = {
                damage_type = DAMAGE_TRUE,
                damage_max = {8, 10, 12, 13, 15, 17, 19, 21, 23, 25},
                damage_min = {6, 7, 8, 9, 10, 12, 14, 16, 18, 20},
                xp_gain_factor = 1.55,
            },
            jump = {
                damage_type = DAMAGE_TRUE,
                damage_max = {8, 10, 12, 13, 15, 17, 19, 21, 23, 25},
                damage_min = {6, 7, 8, 9, 10, 12, 14, 16, 18, 20},
                xp_gain_factor = 1.55,
            },
            simple = {
                damage_type = DAMAGE_TRUE,
                damage_max = {8, 10, 12, 13, 15, 17, 19, 21, 23, 25},
                damage_min = {6, 7, 8, 9, 10, 12, 14, 16, 18, 20},
                xp_gain_factor = 1.55,
            },
            fast_hits = {
                damage_type = DAMAGE_TRUE,
                damage_max = {8, 10, 12, 13, 15, 17, 19, 21, 23, 25},
                damage_min = {6, 7, 8, 9, 10, 12, 14, 16, 18, 20},
                xp_gain_factor = 1.55,
            },
        },
        pole_ranged ={
            cooldown = {18, 18, 18},
            damage_type = DAMAGE_PHYSICAL,
            damage_max = {15, 25, 30}, -- per pole
            damage_min = {10, 14, 18}, -- per pole
            min_range = 0,
            max_range = 200,
            damage_radius = 50,
            min_targets = 3,
            pole_amounts = {3, 5, 7},
            xp_gain = {20, 40, 60},
            stun_duration = 3,
        },
        hair_clones = {
            cooldown = {25, 23, 21},
            max_range = 160,
            min_targets = 2,
            xp_gain = {20, 40, 60},
            soldier = {
                hp_max = {80, 100, 120},
                armor = 0,
                max_speed = 60,
                duration = {9, 9, 9},
                melee_attack = {
                    cooldown = 1,
                    damage_min = {4, 8, 12},
                    damage_max = {5, 10, 14},
                    range = 72,
                }
            }
        },
        zhu_apprentice = {
            hp_max = {60, 90, 140},
            armor = 0,
            max_speed = 100,
            dead_lifetime = 9,
            melee_attack = {
                cooldown = 1,
                damage_min = {2, 4, 8},
                damage_max = {3, 6, 12},
                damage_type = DAMAGE_PHYSICAL,
                range =150, -- distance to look for enemies
            },
            smash_attack = {
                cooldown = 5,
                damage_min = {30, 55, 75},
                damage_max = {35, 70, 90},
                damage_radius = 72,
                damage_type = DAMAGE_PHYSICAL,
                chance = {0.3, 0.4, 0.5}
            }
        },
        giant_staff = {
            cooldown = {53, 50, 46},
            xp_gain = {20, 40, 60},
            -- Skill instakills it's target but could also deal damage around
            area_damage = {
                damage_type = DAMAGE_PHYSICAL,
                damage_max = {50, 75, 100},
                damage_min = {45, 60, 85},
                damage_radius = 70,
                max_targets = 10,
            },
        },
        ultimate = {
            cooldown = {53, 53, 53, 53},
            damage_total = {200, 300, 400, 500},
            damage_type = DAMAGE_TRUE,
            slow_duration = {3, 3.5, 4, 4.5}, -- WARNING - DUARCION AJUSTADA CON LA ANIMACION
            slow_factor = {0.5, 0.5, 0.5, 0.5},
        }
    },
}

local relics = {
    banner_of_command = {
        cooldown = {20, 20, 20, 20},
        soldier = {
            cooldown = 1,
            damage_min = {5, 6, 8, 10},
            damage_max = {7, 8, 12, 14},
            hp = {30, 40, 50, 70},
            armor = {0, 0, 0, 0},
            regen_health = {3, 4, 5, 7},
            duration = 15
        }
    },
    locket_of_the_unforgiven = {
        max_skeletons = {2, 3, 4, 5},
        range = 200/2,
        skeleton = {
            cooldown = 1,
            damage_min = {2, 3, 4, 5},
            damage_max = {4, 5, 6, 7},
            hp = {50, 60, 75, 85},
            duration = 15
        }
    },
    guardian_orb = {
        damage_min = {4, 5, 6, 8},
        damage_max = {8, 10, 12, 18},
        damage_type = DAMAGE_MAGICAL,
        cooldown = 1.5,
        shoot_range = 50/2,
        search_range = 300/2,
        hero_max_distance = 200, --stops following targets farther than
        hero_idle_distance = 50, -- if no target then tries to stay closer than
        search_cooldown = 0.25
    },
    mirror_of_inversion = {
        --nada?
    },
    hammer_of_the_blessed = {
        cooldown = 12,
        heal_percent = {0.15, 0.20, 0.25, 0.3},
        range = 100/2
    }
}

local enemies = {
    werebeasts = {
        hog_invader = {
            gold = 5,
            hp = 48,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 4,
                damage_min = 2
            }
        },
        tusked_brawler = {
            gold = 10,
            hp = 96,
            armor = 0.2,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 6,
                damage_min = 4
            }
        },
        turtle_shaman = {
            gold = 25,
            hp = 300,
            armor = 0,
            magic_armor = 0.8,
            speed = 20,
            basic_attack = {
                cooldown = 1.5,
                damage_max = 4,
                damage_min = 3
            },
            ranged_attack = {
                cooldown = 1.5,
                max_range = 100, -- +60 variance
                min_range = 60,
                damage_max = 10,
                damage_min = 6
            },
            natures_vigor = {
                cooldown = 4,
                hp_trigger_factor = 0.6,
                range = 200,
                duration = 1.5,
                heal_max = 10,
                heal_min = 10,
                heal_every = 0.25
            }
        },
        bear_vanguard = {
            gold = 30,
            hp = 600,
            armor = 0.6,
            magic_armor = 0,
            speed = 20,
            basic_attack = {
                cooldown = 2.0,
                damage_max = 20,
                damage_min = 12,
                damage_radius = 70/2,
                damage_type = DAMAGE_PHYSICAL
            },
            wrath_of_the_fallen = {
                radius = 80,
                duration = 8,
                inflicted_damage_factor = 2.0
            },
        },
        bear_woodcutter = {
            gold = 50,
            hp = 900,
            armor = 0.4,
            magic_armor = 0,
            speed = 20,
            basic_attack = {
                cooldown = 2.0,
                damage_max = 20,
                damage_min = 12,
                damage_radius = 70/2,
                damage_type = DAMAGE_PHYSICAL
            },
            wrath_of_the_fallen = {
                radius = 80,
                duration = 5,
                inflicted_damage_factor = 2.0
            },
        },
        cutthroat_rat = {
            gold = 6,
            hp = 60,
            armor = 0,
            magic_armor = 0,
            speed = 64,
            basic_attack = {
                cooldown = 1,
                damage_max = 12,
                damage_min = 8
            },
            gut_stab = {
                cooldown = 15,
                damage_max = 16,
                damage_min = 10,
                damage_type = DAMAGE_PHYSICAL,
                duration = 2.5,
                bleed_duration = 3,
                bleed_damage_min = 1,
                bleed_damage_max = 1,
                bleed_every = 0.25, --seconds
                min_distance_from_end = 450
            },
        },
        dreadeye_viper = {
            gold = 12,
            hp = 120,
            armor = 0,
            magic_armor = 0.3,
            speed = 36,
            basic_attack = {
                cooldown = 1.5,
                damage_max = 2,
                damage_min = 1,
                poison = {
                    damage_max = 1,
                    damage_min = 1,
                    duration = 1,
                    every = 0.25
                }
            },
            ranged_attack = {
                cooldown = 2.0,
                max_range = 150, -- +60 variance
                min_range = 60,
                damage_max = 14,
                damage_min = 8,
                poison = {
                    damage_max = 1,
                    damage_min = 1,
                    duration = 2,
                    every = 0.25
                }
            },
        },
        -- VULTURE
        surveyor_harpy = {
            gold = 5,
            hp = 50,
            armor = 0,
            magic_armor = 0,
            speed = 50,
        },
        skunk_bombardier = {
            gold = 15,
            hp = 160,
            armor = 0,
            magic_armor = 0.60,
            speed = 36,
            melee_attack = {
                cooldown = 1,
                damage_max = 4,
                damage_min = 3
            },
            ranged_attack = {
                cooldown = 2.5,
                max_range = 150,
                min_range = 40,
                damage_max = 15,
                damage_min = 10,
                radius = 45,
                mod_duration = 2,
                received_damage_factor = 2.0,
            },
        },
        hyena5 = {
            gold = 15,
            hp = 200,
            armor = 0.2,
            magic_armor = 0,
            speed = 50,
            melee_attack = {
                cooldown = 1.5,
                damage_max = 22,
                damage_min = 14
            },
            feast = {
                heal = 20,
                heal_every = 0.25,
                duration = 1.5,
                hp_min_trigger = 120,
                cooldown = 10,
            }
        },
        rhino = {
            gold = 80,
            hp = 1500,
            armor = 0,
            magic_armor = 0,
            speed = 20,
            lives_cost = 2,
            basic_attack = {
                cooldown = 2,
                damage_max = 64,
                damage_min = 48
            },
            instakill = {
                cooldown = 5,
                damage_min = 300,
                damage_max = 300,
                damage_type = DAMAGE_INSTAKILL,
            },
            charge = {
                cooldown = 20,
                damage_enemy_min = 56,
                damage_enemy_max = 56,
                damage_soldier_min = 56,
                damage_soldier_max = 56,
                damage_type = DAMAGE_PHYSICAL,
                duration = 1.8,
                min_distance_from_end = 50,
                speed = 120,
                trigger_range = 120,
                min_range = 90,
                range = 50, -- check while charing
            }
        },
        --GOREGRIND
        boss = { 
            hp = 8000,
            armor = 0.25,
            speed = 16,
            melee_attack = {
                cooldown = 1,
                damage_max = 160,
                damage_min = 120,
                damage_radius = 60,
            },
            fall = {
                damage_min = 800,
                damage_max = 500,
                radius = 100,
            }
        }
    },

    cult_of_the_overseer = {
        acolyte = {
            gold = 15,
            hp = 200,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 10,
                damage_min = 6
            },
            tentacle = {
                hp = 80,
                armor = 0,
                magic_armor = 0,
                duration = 6,
                hit = {
                    cooldown = 1,
                    damage_max = 10,
                    damage_min = 6,
                    -- not in use
                    radius = 90/2,
                    first_cooldown_min = 1,
                    first_cooldown_max = 2,
                }
            }
        },
        lesser_sister = {
            gold = 50,
            hp = 600,
            armor = 0,
            magic_armor = 0.60,
            speed = 28,
            crooked_souls = {
                max_targets = 1,
                max_total = 10,
                cooldown = 8,
                max_range = 300,
                nodes_limit = 60,
                nodes_random_max = 10,
                nodes_random_min = 5,
            },
            melee_attack = {
                cooldown = 1,
                damage_max = 8,
                damage_min = 4
            },
            ranged_attack = {
                cooldown = 1.5,
                max_range = 120,
                damage_max = 18,
                damage_min = 12,
                damage_type = DAMAGE_MAGICAL,
            },
            nightmare = {
                hp = 120,
                speed = 50,
                armor = 0,
                magic_armor = 0,
                lives_cost = 1,
                basic_attack = {
                    cooldown = 1,
                    damage_min = 6,
                    damage_max = 10,
                }
            }
        },
        small_stalker = {
            gold = 10,
            hp = 180,
            armor = 0,
            magic_armor = 0,
            speed = 50,
            dodge = {
                cooldown = 50,
                nodes_before_exit = 80,
                nodes_advance = 25,
                wait_between_teleport = fts(3)
            },
        },
        unblinded_priest = {
            gold = 25,
            hp = 400,
            armor = 0,
            magic_armor = 0.90,
            speed = 28,
            health_trigger_factor = 0.25,
            transformation_time = 4,
            basic_attack = {
                cooldown = 1,
                damage_max = 11,
                damage_min = 7
            },
            ranged_attack = {
                cooldown = 2,
                max_range = 120, -- +60 variance
                min_range = 10,
                damage_max = 28,
                damage_min = 18,
                damage_type = DAMAGE_MAGICAL,
            },
            abomination = {
                gold = 30,
                hp = 900,
                armor = 0.5,
                lives_cost = 2,
                magic_armor = 0,
                speed = 20,
                melee_attack = {
                    cooldown = 2,
                    damage_max = 38,
                    damage_min = 26
                },
                eat = {
                    hp_required = 0.3,
                    cooldown = 10,
                },
                glare = {
                    regen_hp = 15,
                }
            },
        },
        abomination_stage_8 = {
            gold = 0,
            hp = 1000,
            armor = 0,
            magic_armor = 0,
            speed = 0,
            regen_cooldown = 0.8,
            regen_health = 60,
            melee_attack = {
                cooldown = 2,
                damage_max = 60,
                damage_min = 40
            },
        },
        spiderling = {
            gold = 10,
            hp = 200,
            armor = 0,
            magic_armor = 0.3,
            speed = 64,
            basic_attack = {
                cooldown = 1,
                damage_max = 12,
                damage_min = 8
            },
        },
        unblinded_shackler = {
            gold = 50,
            hp = 750,
            armor = 0,
            magic_armor = 0.60,
            speed = 28,
            melee_attack = {
                cooldown = 1.5,
                damage_max = 26,
                damage_min = 18
            },
            shackles = {
                max_range = 150,
                min_targets = 1,
                max_targets = 2,
                health_trigger_factor = 0.2,
            },
        },
        armored_nightmare = {
            gold = 20,
            hp = 350,
            armor = 0.8,
            magic_armor = 0,
            speed = 28,
            basic_attack = {
                cooldown = 1,
                damage_max = 18,
                damage_min = 12,
                damage_type = DAMAGE_PHYSICAL,
            },
        },
        corrupted_stalker = {
            gold = 100,
            hp = 1500,
            armor = 0,
            magic_armor = 0,
            speed = 20,
            lives_cost = 2,
        },
        crystal_golem = {
            gold = 200,
            hp = 2400,
            armor = 0.8,
            magic_armor = 0,
            speed = 20,
            lives_cost = 5,
            basic_attack = {
                cooldown = 2,
                damage_max = 48,
                damage_min = 30,
                damage_radius = 70/2,
                damage_type = DAMAGE_PHYSICAL
            },
        },
        boss_corrupted_denas = {
            hp = 10000,
            armor = 0.5,
            magic_armor = 0.5,
            speed = 16,
            melee_attack = {
                cooldown = 2,
                damage_max = 320,
                damage_min = 180,
                damage_type = DAMAGE_PHYSICAL,
                damage_radius = 120/2,
            },
            spawn_entities = {
                cooldown = 10,
                max_range = 120,
            },
            life_threshold_stun = {
                life_percentage = {50, 25},
                stun_duration = 5,
            },
        },
        glareling = {
            gold = 0,
            hp = 60,
            armor = 0,
            magic_armor = 0,
            speed = 64,
            basic_attack = {
                cooldown = 1,
                damage_max = 10,
                damage_min = 6
            },
            glare = {
                regen_hp = 15,
                speed_factor = 1.75,
            }
        },
    },

    void_beyond = {
        glare = {
            regen_every = 0.25,
            extra_duration = 0.25,
            range = 200
        },
        blinker = {
            gold = 25,
            hp = 350,
            armor = 0,
            magic_armor = 0,
            speed = 64,
            ranged_attack = {
                cooldown = 10,
                duration = 4,
                radius = 90/2,
                stun_duration = 1, -- extra time after the attack ends
                stun_every = 0.25, --seconds
                min_range = 40,
                max_range = 60,
            },
            glare = {
                regen_hp = 15,
                dot_duration = 0.5,
                dot_damage_min = 2,
                dot_damage_max = 2,
                dot_every = 0.25, --seconds
            }
        },
        mindless_husk = {
            gold = 20,
            hp = 300,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 15,
                damage_min = 10,
                damage_type = DAMAGE_PHYSICAL
            },
            -- egg = {
            --     gold = 0,
            --     hp = 80,
            --     armor = 0,
            --     magic_armor = 0,
            --     time_to_spawn = 4, -- in seconds
            --     glare = {
            --         regen_hp = 10,
            --     }
            -- },
            spawn = {
                min_nodes_ahead = 15,
                max_nodes_ahead = 20,
                max_nodes_to_exit = 70
            },
            glare = {
                regen_hp = 15,
            }
        },
		vile_spawner = {
            gold = 75,
            hp = 1000,
            armor = 0,
            magic_armor = 0.3,
            speed = 28,
            basic_attack = {
                cooldown = 1.5,
                damage_max = 21,
                damage_min = 14
            },
            lesser_spawn = {
                cooldown = 8,
                min_range = 40,
                max_range = 80,
                distance_between_entities = 40,
                entities_amount = 3,
                min_distance_from_end = 400,
                max_total = 9
            },
            glare = {
                regen_hp = 10,
                lesser_spawn_cooldown = 6,
            },
        },
        lesser_eye = {
            gold = 0,
            hp = 80,
            armor = 0,
            magic_armor = 0,
            speed = 75,
            glare = {
                regen_hp = 15,
            }
        },
        noxious_horror = {
            gold = 45,
            hp = 600,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 14,
                damage_min = 9,
                damage_type = DAMAGE_PHYSICAL
            },
            ranged_attack = {
                cooldown = 4,
                damage_max = 28,
                damage_min = 18,
                max_range = 300, -- +60 variance
                min_range = 100,
                damage_type = DAMAGE_TRUE,
                radius = 100/2
            },
            poison = {
                damage_max = 6,
                damage_min = 4,
                duration = 4,
                every = 0.25
            },
            glare = {
                regen_hp = 15,
                magic_armor = 0.9,
                aura = {
                    radius = 60/2,
                }
            }
        },
        hardened_horror = {
            gold = 80,
            hp = 1200,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                cooldown = 2,
                damage_max = 42,
                damage_min = 28,
                damage_radius = 80/2,
                damage_type = DAMAGE_PHYSICAL
            },
            glare = {
                regen_hp = 15,
                armor = 0.9,
                roll_speed = 70
            }
        },
        evolving_scourge = {
            lives_cost = 2,
            gold = {30, 50, 90},
            armor = 0.30,
            magic_armor = 0,
            hp = {450, 800, 1500},
            speed = {50, 36, 20},
            basic_attack = {
                cooldown = {1, 2, 2},
                damage_max = {18, 36, 72},
                damage_min = {12, 24, 48},
                damage_type = DAMAGE_PHYSICAL
            },
            eat = {
                cooldown = 20,
                hp_required = 0.30,
            },
            glare = {
                regen_hp = 15,
            }
        },
        amalgam = {
            gold = 200,
            armor = 0.4,
            magic_armor = 0,
            hp = 3500,
            speed = 20,
            lives_cost = 5,
            basic_attack = {
                cooldown = 2,
                damage_max = 120,
                damage_min = 80,
                damage_radius = 100/2,
                damage_type = DAMAGE_PHYSICAL
            },
            explosion = {
                damage_max = 200,
                damage_min = 100,
                damage_radius = 120/2,
                damage_type = DAMAGE_PHYSICAL
            },
            glare = {
                regen_hp = 15,
            }
        },
        --MydriasBF
        boss_cult_leader = {
            hp = 15000,
            glare = {
                regen_hp = 50,
            },
            open_armor = 0,
            open_magic_armor = 0,
            close_armor = 0.9,
            close_magic_armor = 0.9,
            speed = 16,
            denas_ray_resistance = 0.5,
            melee_attack = {
                cooldown = 1,
                damage_max = 240,
                damage_min = 160,
                damage_type = DAMAGE_PHYSICAL,
                damage_radius = 20/2,
            },
            block_attack = {
                damage_min = 0,
                damage_max = 0,
                damage_type = DAMAGE_PHYSICAL,
                radius = 100,
            },
            area_attack = {
                min_count = 2,
                damage_min = 320,
                damage_max = 320,
                damage_type = DAMAGE_MAGICAL,
                damage_radius = 80,
                cooldown = 4,
            },
            life_threshold_teleport = {
                life_percentage = {66.67, 33.33},
                away_duration = 8,
            },
        },
    },
    undying_hatred = { -- update halloween
        corrupted_elf = {
            gold = 30,
            hp = 350,
            armor = 0.25,
            magic_armor = 0,
            speed = 36,
            melee_attack = {
                cooldown = 0.8,
                damage_min = 10,
                damage_max = 14,
            },
            ranged_attack = {
                cooldown = 10,
                min_range = 80,
                max_range = 250, -- +60 variance
                damage_min = 10,
                damage_max = 14,
            },
            spawn_nodes_limit = 60,
        },
        specter = {
            hp = 150,
            speed = 36,
            armor = 0,
            magic_armor = 0,
            lives_cost = 1,
            basic_attack = {
                cooldown = 1,
                damage_min = 8,
                damage_max = 12,
            },
            speed_chase = 120,
        },
        dust_cryptid = {
            gold = 20,
            hp = 200,
            armor = 0,
            magic_armor = 0.25,
            speed = 50,
            lives_cost = 1,
            dust_radius = 60,
            dust_duration = 3.5,
            nodes_to_prevent_dust = 25,
        },
        bane_wolf = {
            gold = 10,
            hp = 250,
            armor = 0,
            magic_armor = 0,
            speed = 50,
            lives_cost = 1,
            max_speed_mult = 2.2, -- max speed factor
            basic_attack = {
                cooldown = 0.8,
                damage_max = 16,
                damage_min = 10,
                damage_type = DAMAGE_PHYSICAL
            },
        },
        deathwood = {
            gold = 200,
            hp = 3200,
            armor = 0.6,
            magic_armor = 0,
            speed = 20,
            lives_cost = 2,
            basic_attack = {
                cooldown = 1.5,
                damage_max = 94,
                damage_min = 62,
                damage_radius = 50/2,
                damage_type = DAMAGE_PHYSICAL
            },
            ranged_attack = {
                cooldown = 6,
                damage_max = 180,
                damage_min = 120,
                max_range = 180, -- +60 variance
                min_range = 80,
                damage_type = DAMAGE_EXPLOSION,
                damage_radius = 100/2
            },
        },
        animated_armor = {
            gold = 100,
            hp = 1500,
            armor = 0.8,
            magic_armor = 0,
            speed = 28,
            lives_cost = 2,
            death_duration = 10,
            respawn_health_factor = 1,
            basic_attack = {
                cooldown = 2,
                damage_max = 48,
                damage_min = 32,
                damage_radius = 50/2,
                damage_type = DAMAGE_PHYSICAL,
            },
        },
        revenant_soulcaller = {
            gold = 50,
            hp = 900,
            armor = 0,
            magic_armor = 0.80,
            speed = 28,
            melee_attack = {
                cooldown = 1.5,
                damage_max = 24,
                damage_min = 10
            },
            ranged_attack = {
                cooldown = 2,
                max_range = 120,
                damage_max = 38,
                damage_min = 16,
                damage_type = DAMAGE_MAGICAL,
            },
            summon = {
                max_total = 10,
                cooldown = 10,
                max_range = 300,
                nodes_limit = 60,
                nodes_random_max = 10,
                nodes_random_min = 5,
            },
            tower_stun = {
                cooldown = 15,
                max_range = 150,
                duration = 5,
                min_targets = 1,
                max_targets = 1,
            }
        },
        revenant_harvester = {
            gold = 30,
            hp = 600,
            armor = 0,
            magic_armor = 0.25,
            speed = 36,
            melee_attack = {
                cooldown = 1,
                damage_max = 52,
                damage_min = 22
            },
            clone = {
                max_total = 5,
                cooldown = 12,
                max_range = 150,
                nodes_limit = 60,
            },
        },
        boss_navira = {
            hp = 16000,
            armor = 0,
            magic_armor = 0,
            speed = 16,
            melee_attack = {
                cooldown = 1.5,
                damage_max = 280,
                damage_min = 192,
                damage_type = DAMAGE_PHYSICAL,
                damage_radius = 120/2,
            },
            tornado = {
                hp_trigger = {0.9, 0.6, 0.15},
                fire_balls = {3, 3, 3}, -- how many fire balls it spawns after the tornado
                duration = 6,
                speed_mult = 1.25,
                damage = 15,
                damage_type = DAMAGE_MAGICAL,
                cycle_time = 0.25, -- how often damage is applied 
                radius = 80/2,
            },
            fire_balls = {
                count = 3,
                cooldown = 25,
                wait_between_balls = 5, 
                wait_before_shoot = 1,
                wait_between_shots = 0.2,
                stun_duration = 5
            },
            corruption = {
                hp = 320,
                cooldown = 10,
            }
        },
    },
    crocs = { -- update crocs
        crocs_basic_egg = {
            gold = 3,
            hp = 70,
            armor = 0.3,
            magic_armor = 0,
            speed = 50,
            water_fixed_speed = {[21]=80, [22]=50, [9000]=50},
            evolve = {
                cooldown_min = 9,
                cooldown_max = 11,
            }
        },
        crocs_basic = {
            gold = 6,
            hp = 350,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            water_fixed_speed = {[21]=80, [22]=50, [9000]=50},
            basic_attack = {
                cooldown = 1,
                damage_max = 16,
                damage_min = 10
            }
        },
        quickfeet_gator = {
            gold = 20,
            hp = 250,
            armor = 0,
            magic_armor = 0,
            speed = 60,
            water_fixed_speed = {[21]=80, [22]=50, [9000]=50},
            basic_attack = {
                cooldown = 1,
                damage_max = 17,
                damage_min = 11
            },
            ranged_attack = {
                cooldown = 3,
                min_range = 70,
                max_range = 250,
                damage_max = 17,
                damage_min = 11
            },
            chicken_leg = {
                target_nodes_from_start = 22,
                self_nodes_from_start = 22,
                min_range = 70,
                max_range = 250,
            },
        },
        killertile = {
            gold = 50,
            hp = 1400,
            armor = 0,
            lives_cost = 1,
            magic_armor = 0,
            speed = 23,
            water_fixed_speed = {[21]=80, [22]=50, [9000]=50},
            basic_attack = {
                cooldown = 1.5,
                damage_max = 99,
                damage_min = 66
            }
        },
        crocs_flier = {
            gold = 15,
            hp = 225,
            armor = 0,
            magic_armor = 0,
            speed = 60,
        },
        crocs_ranged = {
            gold = 17,
            hp = 300,
            armor = 0,
            magic_armor = 0,
            speed = 60,
            water_fixed_speed = {[21]=80, [22]=50, [9000]=50},
            basic_attack = {
                cooldown = 1.5,
                damage_max = 17,
                damage_min = 11,
                
            },
            ranged_attack = {
                cooldown = 2,
                max_range = 200, -- +60 variance
                min_range = 60,
                damage_max = 43,
                damage_min = 29,
               
            },
        },
        crocs_shaman = {
            gold = 90,
            hp = 700,
            armor = 0,
            magic_armor = 0.60,
            speed = 28,
            water_fixed_speed = {[21]=80, [22]=50, [9000]=50},
            melee_attack = {
                cooldown = 2,
                damage_max = 20,
                damage_min = 13
            },
            ranged_attack = {
                cooldown = 1.5,
                max_range = 120,
                damage_max = 45,
                damage_min = 30,
                damage_type = DAMAGE_MAGICAL,
            },
            healing = {
                cooldown = 10,
                duration = 2,
                range = 150,
                heal_every = 0.5,
                heal_max = 45,
                heal_min = 15,
                min_targets = 1,
                max_targets = 5
            },
            debuff_towers = {
                cooldown = 8,
                stun_duration = 6,
                max_range = 200,
                nodes_limit = 40,
            }
        },
        crocs_tank = {
            gold = 70,
            hp = 2200,
            armor = 0.35,
            magic_armor = 0,
            speed = 20,
            lives_cost = 2,
            basic_attack = {
                cooldown = 2,
                damage_max = 64,
                damage_min = 48
            },
            charge = {
                cooldown = 13,
                damage_soldier_min = 62,
                damage_soldier_max = 62,
                damage_type = DAMAGE_PHYSICAL,
                duration = 1.5,
                min_distance_from_end = 45,
                blocker_charge_delay = 3,
                speed = 120,
                range = 50, -- check while charging
            }
        },
		crocs_egg_spawner = {
            gold = 100,
            hp = 850,
            armor = 0.6,
            magic_armor = 0,
            lives_cost = 1,
            speed = 20,
            water_fixed_speed = {[21]=80, [22]=50, [9000]=50},
            basic_attack = {
                cooldown = 1.5,
                damage_max = 63,
                damage_min = 42
            },
            eggs_spawn = {
                cooldown = 10,
                min_range = 90,
                max_range = 120,
                distance_between_entities = 40,
                entities_amount = 3,
                min_distance_from_end = 430,
                max_total = 10
            },
        },
        crocs_hydra = {
            gold = 250,
            hp = {2750, 3800},
            armor = 0,
            magic_armor = 0.80,
            lives_cost = 5,
            speed = 20,
            water_fixed_speed = {[21]=30, [22]=30, [9000]=30},
            basic_attack = {
                cooldown = 1.5,
                damage_max = 105,
                damage_min = 75,
                damage_type = DAMAGE_PHYSICAL
            },
            dot = {
                cooldown = 8,
                max_range = 200,
                duration = 6,
                radius = 60,
                nodes_limit = 60,
                damage_min = 8,
                damage_max = 12,
                damage_every = 0.3,
                damage_type = DAMAGE_TRUE
            }
        },
		boss_crocs = {
            hp = {16000, 16000, 16000, 16000, 16000},
            armor = {0.0, 0.0, 0.0, 0.0, 0.0},
            magic_armor = {0.0, 0.0, 0.0, 0.0, 0.0},
            speed = {24, 21, 17, 14, 11},
            eat_tower_evolution = {50, 50, 50, 50},
            life_percentage_evolution = {0.7, 0.65, 0.58, 0.4},
            pre_fight_towers_destroy = {
                waves           = {02, 03, 05, 08, 10, 11, 13, 14, 15},
                first_cooldown  = {05, 05, 07, 01, 15, 10, 10, 05, 01},
                cooldown        = {00, 00, 00, 40, 45, 35, 40, 26, 20},
                max_casts       = {01, 01, 01, 02, 01, 02, 02, 03, 04},
                prevent_timed_destroy_price = 0,
                destroy_tower_time = 2,
                can_prevent_destroy = false,
                needs_arborean_mages_to_clean = false,
                taunt_keys_amount = 8,
                low_priority_holders = {'4', '7', '8', '12'}
            },
            primordial_hunger = {
                {
                    pre_evolution_step_cap = 0.7, -- how much power 
                    hp_evolution_method = 2,
                    -- hp evolution methods
                    -- 0: Restore all health
                    -- 1: Keep life percentage with new max health
                    -- 2: Restore fixed amount
                    hp_restore_fixed_amount = 1700,
                },
                {
                    pre_evolution_step_cap = 0.7, -- how much power 
                    hp_evolution_method = 2,
                    -- hp evolution methods
                    -- 0: Restore all health
                    -- 1: Keep life percentage with new max health
                    -- 2: Restore fixed amount
                    hp_restore_fixed_amount = 2200,
                },
                {
                    pre_evolution_step_cap = 0.7, -- how much power 
                    hp_evolution_method = 2,
                    -- hp evolution methods
                    -- 0: Restore all health
                    -- 1: Keep life percentage with new max health
                    -- 2: Restore fixed amount
                    hp_restore_fixed_amount = 1700,
                },
                {
                    pre_evolution_step_cap = 0.8, -- how much power 
                    hp_evolution_method = 2,
                    -- hp evolution methods
                    -- 0: Restore all health
                    -- 1: Keep life percentage with new max health
                    -- 2: Restore fixed amount
                    hp_restore_fixed_amount = 3500,
                },
                {
                    pre_evolution_step_cap = 0.9, -- how much power 
                    hp_evolution_method = 2,
                    -- hp evolution methods
                    -- 0: Restore all health
                    -- 1: Keep life percentage with new max health
                    -- 2: Restore fixed amount
                    hp_restore_fixed_amount = 3500,
                }
            },
            basic_attack = {
                cooldown = 1.5,
                -- has third level for the pre-step
                damage_max = {250, 290, 480, 600, 720},
                damage_min = {150, 240, 360, 430, 500},
                damage_radius = 60,
                instakill_threshold = {0.95, 0.95, 0.95, 0.95, 0.95},
            },
            tower_destruction = {
                cooldown = {12, 12, 16, 16, 18},
                max_range = {200, 200, 200, 200, 200},
                low_priority_holders = {}
            },
            eggs_spawn = {
                cooldown = {25, 25, 25, 25, 25},
                min_range = {80, 90, 100, 110, 120},
                max_range = {330, 340, 350, 360, 370},
                distance_between_entities = 12,
                loop_times = {2, 3, 4, 4, 5},
                entities_amount = {22, 22, 20, 20, 16},
                min_distance_from_end = {
                    -- path, distance
                    [20] = 600,
                    [19] = 430,
                    [12] = 430,
                    [9] = 600
                },
                max_total = 1e99
            },
            poison_rain = {
                cooldown = {20, 30},
                min_range = {0, 0},
                max_range = {3000, 3000},
                shots_amount = {5, 10},
                poison_radius = 80,
                poison_damage_min = {3, 5},
                poison_damage_max = {5, 9},
                poison_damage_every = 0.25,
                poison_decal_duration = {6, 6},
                poison_mod_duration = {0.4, 0.4},
                damage_type = DAMAGE_PHYSICAL,
            },
            stomper = {
                damage_soldiers_min = 3,
                damage_soldiers_max = 6,
                damage_every = 0.25,
                damage_type = DAMAGE_PHYSICAL,
                range = 80,
            },
        },
    },
    hammer_and_anvil = { -- dlc1 / dlc_1  / dlc 1 / dwarfs
        darksteel_hammerer = {
            gold = 22,
            hp = {280, 320, 420, 450},
            armor = 0,
            magic_armor = 0,
            speed = 28,
            melee_attack = {
                cooldown = 1,
                damage_min = 12,
                damage_max = 18,
            },
        },
        darksteel_shielder = {
            gold = 30,
            hp = {380, 440, 550, 600},
            armor = 0.80,
            magic_armor = 0,
            speed = 28,
            melee_attack = {
                cooldown = 1.5,
                damage_min = 18,
                damage_max = 26,
            },
        },
        surveillance_sentry = {
            gold = 5,
            hp = 180,
            armor = 0.20,
            magic_armor = 0,
            speed = 50,
            lives_cost = 1,
        },
        rolling_sentry = {
            gold = 25,
            hp = {450, 520, 640, 720},
            armor = 0.20,
            magic_armor = 0,
            speed = 64,
            melee_attack = {
                cooldown = 1,
                damage_min = 10,
                damage_max = 18,
                damage_type = DAMAGE_TRUE,
            },
            ranged_attack = {
                cooldown = 1,
                min_range = 80,
                max_range = 150, -- +60 variance
                damage_min = 10,
                damage_max = 18,
                damage_type = DAMAGE_TRUE,
            },
        },
        scrap_drone = {
            gold = 0,
            hp = 50,
            armor = 0,
            magic_armor = 0,
            speed = 75,
            lives_cost = 1,
        },
        mad_tinkerer = {
            gold = 70,
            hp = 1000,
            armor = 0,
            magic_armor = {0.6, 0.6, 0.6, 0.8},
            speed = 50,
            melee_attack = {
                cooldown = 1,
                damage_max = 24,
                damage_min = 16
            },
            clone = {
                max_total = 5,
                cooldown = 1,
                max_range = 150,
                min_range = 80,
                nodes_limit = 30,
            },
        },
        brute_welder = {
            gold = 120,
            hp = {1200, 1440, 1800, 1950},
            armor = 0,
            magic_armor = 0,
            speed = 24,
            lives_cost = 2,
            basic_attack = {
                cooldown = 2.0,
                flame = {
                    duration = 0.25,
                    radius = 80/2,
                    cycle_time = 0.25,  -- how often applies burn
                },
                burn = {
                    duration = 2,
                    cycle_time = 0.25,  -- how often damages the target
                    damage_max = 5, -- per tick
                    damage_min = 3,
                    damage_type = DAMAGE_TRUE
                }
            },
            death_missile = {
                range = 200,
                block_duration = {6,8,8,10},
            }
        },
        scrap_speedster = {
            gold = 6,
            hp = 200,
            armor = 0,
            magic_armor = 0,
            speed = {80, 80, 80, 80},
            basic_attack = {
                cooldown = 1.5,
                damage_min = 7,
                damage_max = 11,
            },
        },
        scrap = {
            duration = 6,
        },
        common_clone = {
            gold = 0,
            armor = 0,
            magic_armor = 0,
            hp = 120,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 10,
                damage_min = 5,
                damage_type = DAMAGE_PHYSICAL
            },
        },
        darksteel_fist = {
            gold = 30,
            hp = 700,
            armor = {0.4, 0.4, 0.4, 0.5},
            magic_armor = 0,
            speed = 28,
            basic_attack = {
                cooldown = 0,
                damage_min = 6,
                damage_max = 9,
                damage_type = DAMAGE_PHYSICAL,
            },
            stun_attack = {
                cooldown = {4, 4, 4, 3},
                damage_min = 48,
                damage_max = 72,
                damage_radius = 100/2,
                damage_type = DAMAGE_PHYSICAL,
                stun_duration = 1.5,
            },
        },
        darksteel_guardian = {
            gold = 250,
            armor = 0.8,
            magic_armor = 0,
            hp = {2000, 2400, 3000, 3300},
            speed = 16,
            lives_cost = 5,
            rage_hp_trigger = 0.3,
            basic_attack = {
                cooldown = 2,
                damage_max = 114,
                damage_min = 76,
                damage_radius = 100/2,
                damage_type = DAMAGE_PHYSICAL
            },
            rage_attack = {
                cooldown = 1.5,
                damage_max = 162,
                damage_min = 108,
                damage_type = DAMAGE_PHYSICAL
            },
            death_explotion = {
                damage_min = 300,
                damage_max = 200,
                damage_radius = 100/2,
                damage_type = DAMAGE_EXPLOSION
            },
        },
        darksteel_anvil = {
            gold = 80,
            hp = 1400,
            armor = 0,
            magic_armor = {0.4, 0.4, 0.4, 0.5},
            speed = 42,
            basic_attack = {
                cooldown = 1,
                damage_min = 26,
                damage_max = 40,
                damage_type = DAMAGE_PHYSICAL,
            },
            basic_ranged = {
                cooldown = 3,
                damage_min = 48,
                damage_max = 72,
                max_range = 200,
                min_range = 50,
                damage_type = DAMAGE_PHYSICAL,
            },
            aura = {
                cooldown = {10, 10, 10, 8},
                trigger_range = 80,
                aura_radius = 125,
                cycle_time = 0.25,
                min_targets = 2,
                duration = 3,
                nodes_limit_start = 30,
                nodes_limit_end = 30,
                 -- can the unit apply the armor mod to itself?
                 -- doesn't count for min_targets
                target_self = true,
                mod = {
                    duration = 4, -- can't be less than cycle time
                    speed_factor = 1.5,
                    extra_armor = 0.3,
                }
            },
        },
        darksteel_hulk = {
            gold = 250,
            hp = {2350, 2700, 3500, 3800},
            armor = 0,
            magic_armor = 0.8,
            speed = 20,
            lives_cost = 2,
            basic_attack = {
                cooldown = 2,
                damage_max = 156,
                damage_min = 104
            },
            charge = {
                cooldown = 20,
                charge_while_blocked = true,
                health_threshold = 0.08,
                damage_enemy_min = 64, -- damage to other enemies (darksteel)
                damage_enemy_max = 96,
                damage_soldier_min = 64, -- damage to allied units
                damage_soldier_max = 96,
                damage_type = DAMAGE_PHYSICAL,
                min_distance_from_end = 12,
                speed_mult = 3.2,
                range = 50, -- damage range while charging
            }
        },
        machinist = {
            gold = 0,
            armor = 0,
            magic_armor = 0,
            hp = 1500,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 36,
                damage_min = 24,
                damage_type = DAMAGE_PHYSICAL
            },
            regen_cooldown = 0.8,
            regen_health = 60,
            timeout = 30, --time that the Machinist is active
            operation_cd = 3, --time between lever pulls
            operations_needed = 3, --lever pulls needed to activate spawn
        },
        boss_machinist = {
            hp = 10000,
            armor = 0,
            magic_armor = 0,
            speed = 16,
            stop_cooldown = 5,
            attacks_count = 1,
            ranged_attack = {
                cooldown = 0,
                damage_max = 145,
                damage_min = 95,
                damage_type = DAMAGE_EXPLOSION,
                damage_radius = 120/2,
                min_range = 120,
                max_range = 300,
            },
            fire_floor = {
                radius = 50,
                cycle_time = 0.2,
                damage_max = 6, -- per tick
                damage_min = 4,
                damage_type = DAMAGE_TRUE,
                burn = {
                    duration = 4,
                    cycle_time = 0.25,  -- how often damages the target
                    damage_max = 3, -- per tick
                    damage_min = 1,
                    damage_type = DAMAGE_TRUE
                }
            }
        },
        deformed_grymbeard_clone = {
            gold = 20,
            armor = 0,
            magic_armor = 0, -- without the shield
            hp = 800,
            speed = 24,
            lives_cost = 2,
            shield_magic_armor = 0.8,
            shield_hp_threshold = 0.5,
            speed_factor = 2.5, -- without the shield
        },
        boss_deformed_grymbeard = {
            clones_to_die = 15,
        },
        boss_grymbeard = { -- grymbeard prime - stage 27
            hp = 16000,
            armor = 0,
            magic_armor = 0,
            speed = 12,
            melee_attack = {
                cooldown = 2,
                damage_max = 300,
                damage_min = 200,
                damage_type = DAMAGE_PHYSICAL,
                damage_radius = 90/2,
            },
            ranged_attack = {
                cooldown = 5,
                damage_max = 570,
                damage_min = 380,
                damage_type = DAMAGE_EXPLOSION,
                damage_radius = 90/2,
                min_range = 80,
                max_range = 300,
            },
        },
    },
    arachnids = {
        spider_priest = {
            gold = 30,
            hp = 700,
            armor = 0,
            magic_armor = 0.90,
            speed = 32,
            health_trigger_factor = 0.3,
            transformation_time = 5.5,
            transformation_nodes_limit = 30,
            basic_attack = {
                cooldown = 1,
                damage_max = 11,
                damage_min = 7
            },
            ranged_attack = {
                cooldown = 1.2,
                max_range = 130, -- +60 variance
                min_range = 10,
                damage_max = 52,
                damage_min = 36,
                damage_type = DAMAGE_MAGICAL,
            },
        },
        glarenwarden = {
            gold = 70,
            hp = 1500,
            armor = 0.9,
            magic_armor = 0,
            speed = {24, 24, 24, 24},
            basic_attack = {
                cooldown = 2,
                damage_max = 84,
                damage_min = 56,
                damage_type = DAMAGE_PHYSICAL,
                lifesteal = {
                    damage_factor = 0.5,
                    fixed_heal = 0,
                },
            },
        },
        ballooning_spider = {
            gold = 20,
            hp = 160,
            armor = 0.45,
            magic_armor = 0,
            speed = 64,
            speed_air = 36,
            detection_range = 100
        },
        spider_sister = {
            gold = 45,
            hp = 700,
            armor = 0,
            magic_armor = 0.30,
            speed = 28,
            spiderlings_summon = {
                max_total = 12,
                cooldown = {2.5, 2.5, 2.5, 3.5},
                cooldown_init = 5,
                max_range = 300,
                nodes_limit = 60,
                nodes_random_max = 10,
                nodes_random_min = 5,
                cooldown_increment = 1,
                cooldown_max = 12
            },
            melee_attack = {
                cooldown = 1,
                damage_max = 8,
                damage_min = 4
            },
            ranged_attack = {
                cooldown = 1.5,
                max_range = 150,
                damage_max = 21,
                damage_min = 13,
                damage_type = DAMAGE_MAGICAL,
            },
        },
        glarebrood_crystal = {
            gold = 1,
            hp = 150,
            armor = 0,
            magic_armor = 0.90,
            transformation_time = 4,
            spiderling_spawn = {
                gold = 1
            },
        },
        cultbrood = {
            gold = 50,
            hp = 600,
            armor = 0,
            magic_armor = 0,
            speed = {58,58,58,58},
            spawn_time = 2,
            basic_attack = {
                cooldown = 1,
                damage_max = 24,
                damage_min = 16,
                damage_type = DAMAGE_PHYSICAL,
            },
            poison_attack = {
                cooldown_init = 0,
                cooldown = 9,
                damage_max = 48,
                damage_min = 32,
                damage_type = DAMAGE_PHYSICAL,
                poison = {
                    duration = 6,
                    damage_every = 0.2,
                    damage = 3,
                    damage_type = DAMAGE_PHYSICAL,
                },
                transformation_nodes_limit = 40
            },
        },
        drainbrood = {
            gold = 55,
            hp = 700,
            armor = 0,
            magic_armor = 0,
            speed = 48,
            basic_attack = {
                cooldown = 1,
                damage_max = 35,
                damage_min = 21,
                damage_type = DAMAGE_PHYSICAL,
            },
            webspit = {
                cooldown = 11,
                duration = 4,
                damage_min = 30,
                damage_max = 50,
                damage_type = DAMAGE_PHYSICAL,
                lifesteal = {
                    damage_factor = {3,3,3,4},
                    fixed_heal = 0,
                },
            },
        },
        spidead = {
            gold = 25,
            hp = 600,
            armor = 0,
            magic_armor = 0.333,
            speed = 36,
            basic_attack = {
                cooldown = 1,
                damage_max = 70,
                damage_min = 42,
                damage_type = DAMAGE_PHYSICAL,
            },
            spiderweb = {
                cycle_time = 0.3,
                min_distance = 50,
                duration = 14,
            },
            nodes_to_prevent_web = 25,
        },
        boss_spider_queen = {
            spawn_path = 1,
            spawn_node = 45,
            gold = 0,
            hp = 20000,
            armor = 0,
            magic_armor = {0.3, 0.3, 0.3, 0.6},
            speed = 13,
            reach_nodes = {76}, --{78,65,101}, --De donde salta
            jump_paths = {8}, --{8,1,8},
            jump_nodes = {65}, --{39,95, 92}, --A donde salta
            wave_spawns = { -- glarenwarden spawns for waves 1 to 15
                [3] = {
                    {delay = 16, spawns={ {pi=2, ni=95, spi=1} }},
                },
                [4] = {
                    {delay = 3, spawns={ {pi=1, ni=78, spi=1} }},
                    {delay = 7, spawns={ {pi=5, ni=55, spi=1} }},
                    {delay = 43, spawns={ {pi=1, ni=78, spi=1} }},
                },
                [7] = {
                    {delay = 50, spawns={ {pi=5, ni=55, spi=1} }},
                },
                [10] = {
                    {delay = 2, spawns={ {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 28, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1} }},
                },
                [12] = {
                    {delay = 2, spawns={ {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 42, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1}, {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                },
                [15] = {
                    {delay = 2, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1}, {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 15, spawns={ {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 45, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1}, {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1}, {pi=7, ni=70, spi=1}, {pi=8, ni=90, spi=1} }},
                },
            },
            wave_spawns_impossible = { -- glarenwarden spawns for waves 1 to 15
                [3] = {
                    {delay = 16, spawns={ {pi=2, ni=95, spi=1} }},
                },
                [4] = {
                    {delay = 3, spawns={ {pi=1, ni=78, spi=1} }},
                    {delay = 7, spawns={ {pi=5, ni=55, spi=1} }},
                    {delay = 43, spawns={ {pi=1, ni=78, spi=1} }},
                    {delay = 47, spawns={ {pi=5, ni=55, spi=1} }},
                },
                [6] = {
                    {delay = 50, spawns={ {pi=2, ni=95, spi=1} }},
                },
                [7] = {
                    {delay = 50, spawns={ {pi=5, ni=55, spi=1} }},
                },
                [10] = {
                    {delay = 2, spawns={ {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 28, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1} }},
                },
                [12] = {
                    {delay = 2, spawns={ {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 42, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1}, {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                },
                [15] = {
                    {delay = 2, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1}, {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 15, spawns={ {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1} }},
                    {delay = 45, spawns={ {pi=1, ni=78, spi=1}, {pi=5, ni=55, spi=1}, {pi=7, ni=35, spi=1}, {pi=8, ni=35, spi=1}, {pi=7, ni=70, spi=1}, {pi=8, ni=90, spi=1} }},
                },
            },
            basic_attack = {
                cooldown = 1,
                damage_max = 372,
                damage_min = 258,
                damage_type = DAMAGE_PHYSICAL,
                damage_radius = 110,
            },
            ranged_attack = {
                cooldown = 3,
                max_range = 200,
                min_range = 10,
                damage_min = 86,
                damage_max = 64,
                damage_type = DAMAGE_PHYSICAL,
                poison = {
                    duration = 5,
                    damage_every = 0.2,
                    damage_max = 5,
                    damage_min = 5,
                },
            },
            stun_towers = {
                cooldown = 22,
                nodes_limit = 30,
                duration = {5, 5, 5, 4},
                duration_long = {15, 15, 15, 20},
                required_clics_phone_tablet = 5,
                required_clics_desktop = 3,
                required_clics_console = 2,
                min_targets = 3,
                max_targets = 4,
                max_range = 200,
            },
            call_wardens = {
                cooldown = 40,
                nodes_limit = 20,
                nodes_limit_reverse = 60,
                first_cooldown = 15,
                amount = 2,
                nodes_offset = 8, -- center of spawn, 0 is queen pos
                nodes_spread_start = 3, -- starting distance from center
                nodes_spread = 6, -- distance between each warden
                use_custom_formation = false,
                custom_formation = {
                    { -- formation 1
                        {spi=2, n=5},
                        {spi=3, n=5},
                    },
                    { -- formation 2
                        {spi=2, n=5},
                        {spi=3, n=5},
                    },
                    { -- formation 3
                        {spi=2, n=5},
                        {spi=3, n=5},
                    },
                }
            },
            spiderweb = {
                cycle_time = 0.3,
                min_distance = 50,
                duration = 18,
            },
            drain_life = {
                min_targets = 1,
                max_targets = 1e99,
                cooldown = 21,
                cooldown_init = 30,
                nodes_limit = 50,
                max_range = 106,
                loop_duration = 5,
                lifesteal_loop = { -- continous lifesteal done during loop animation as dps
                    damage_factor = {1.0, 1.0, 1.0, 1.0},
                    fixed_heal = 0, -- Per target
                    damage_min = {3, 3, 3, 5},
                    damage_max = {5, 5, 5, 7},
                    damage_type = DAMAGE_MAGICAL,
                    damage_every = 0.3,
                },
                lifesteal_end = { -- big lifesteal done at the end
                    damage_factor = 1.0,
                    fixed_heal = {500, 500, 500, 750}, -- Per target
                    damage_max = {750, 750, 750, 900},
                    damage_min = {750, 750, 750, 900},
                    damage_type = DAMAGE_MAGICAL,
                },
            },
            webspit = {
                nodes_limit = 30,
                cooldown = 45,
                first_cooldown = 10,
                duration = 2,
            },
        },
    },
    wukong = { -- update wukong
        fire_phoenix = {
            gold = 25,
            hp = 180,
            armor = 0,
            magic_armor = 0,
            speed = 54,
            explode_nodes_limit = 30,
            flaming_ground = {
                duration = 11
            }
        },
        fire_fox = {
            gold = 15,
            hp = 290,
            armor = 0,
            magic_armor = 0.3,
            speed = 60,
            transform_duration = 10,
            transform_hp_threshold = 0.0001,
            basic_attack = {
                damage_min = 38,
                damage_max = 56,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 2
            },
            flaming_ground = {
                duration = 11,
                explotion =  {
                    damage_min = 38,
                    damage_max = 56,
                    damage_type = DAMAGE_MAGICAL,
                },
            }
        },
        nine_tailed_fox = {
            gold = 60,
            hp = 1950,
            armor = 0,
            magic_armor = 0.6,
            speed = 28,
            basic_attack = {
                damage_min = 52,
                damage_max = 84,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 2
            },
            double_attack = {
                damage_min = 64,
                damage_max = 96,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 4,
            },
            stun_attack = {
                damage_min = 60,
                damage_max = 80,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 20,
                has_stun = false,
                stun_duration = 1
            },
            teleport = {
                cooldown = 5,
                lava_paths_first_cooldown = 5,
                first_cooldown = 5,
                nodes_max = 30,
                nodes_min = 25,
                nodes_limit = 75,
                stun_duration = 3,
                tp_speed = 350,
                damage_radius = 30,
                damage_min = 3,
                damage_max = 6,
                damage_type = DAMAGE_PHYSICAL
            }
        },
        blaze_raider = {
            gold = 30,
            hp = 800,
            armor = 0.3,
            magic_armor = 0,
            speed = 32,
            heavy_attack = {
                damage_min = 65,
                damage_max = 110,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 6
            },
            punzada_attack = {
                damage_min = 28,
                damage_max = 42,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 2
            },
            double_attack = {
                damage_min = 28,
                damage_max = 42,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 4
            },
        },
        flame_guard = {
            gold = 6,
            hp = 270,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                damage_min = 19,
                damage_max = 28,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 3
            },
            special_attack = {
                damage_min = 39,
                damage_max = 55,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 4.5
            },
        },
        wuxian = {
            gold = 60,
            hp = 1700,
            armor = 0.7,
            magic_armor = 0,
            speed = 28,
            basic_attack = {
                damage_min = 58,
                damage_max = 74,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 2
            },
            kamehame_attack = {
                damage_min = 180,
                damage_max = 280,
                damage_type = DAMAGE_MAGICAL,
                cooldown = 6
            },
            ranged_attack = {
                damage_min = 90,
                damage_max = 110,
                damage_radius = 70,
                max_range = 350,
                min_range = 200,
                damage_type = DAMAGE_MAGICAL,
                cooldown = 10,
                flaming_ground = {
                    duration = 11,
                }
            },
        },
        burning_treant = {
            gold = 30,
            hp = 1350,
            armor = 0,
            magic_armor = 0,
            speed = 32,
            lives_cost = 1,
            basic_attack = {
                cooldown = 2,
                damage_max = 50,
                damage_min = 40,
                damage_type = DAMAGE_PHYSICAL
            },
            area_attack = {
                cooldown = 6,
                damage_max = 60,
                damage_min = 45,
                damage_type = DAMAGE_PHYSICAL,
                radius = 55,
                flaming_ground = {
                    duration = 11
                }
            },
        },
        ash_spirit = {
            gold = 65,
            hp = 3000,
            armor = 0.75,
            magic_armor = 0,
            speed = 20,
            lives_cost = 2,
            basic_attack = {
                cooldown = 2,
                damage_max = 100,
                damage_min = 60,
                damage_radius = 80/2,
                damage_type = DAMAGE_PHYSICAL
            },
        },
        storm_spirit = {
            gold = 20,
            hp = 220,
            armor = 0,
            magic_armor = 0.3,
            speed = 42,
            lives_cost = 1,
            jump_ahead = {
                nodes_limit = 70,
                max_nodes = 30,
                min_nodes = 25,
                speed_mult = 9,
                hp_threshold = 0.85
            }
        },
        water_spirit = {
            gold = 5,
            hp = 230,
            armor = 0,
            magic_armor = 0.30,
            speed = 46,
            water_spawn_speed = 100,
            lives_cost = 1,
            melee_attack = {
                damage_min = 12,
                damage_max = 18,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 1
            },
        },
        gale_warrior = {
            gold = 30,
            hp = 750,
            armor = 0.35,
            magic_armor = 0,
            speed = 42,
            lives_cost = 1,
            basic_attack = {
                damage_min = 45,
                damage_max = 70,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 1.5
            },
            puncturing_thrust = {
                every_x_attacks = 3,
                damage_min = 50,
                damage_max = 80,
                damage_type = DAMAGE_PHYSICAL,
                dot = {
                    damage_min = 4,
                    damage_max = 4,
                    damage_every = 0.25,
                    duration = 2,
                    damage_type = DAMAGE_PHYSICAL,
                },
            },
        },
        qiongqi = {
            gold = 65,
            hp = 1700,
            armor = 0,
            magic_armor = 0.6,
            speed = 24,
            lives_cost = 1,
            ranged_attack = {
                damage_min = 225,
                damage_max = 335,
                max_range = 200,
                min_range = 50,
                -- damage_radius = 70, -- descomentar para daño en area
                damage_type = DAMAGE_MAGICAL,
                cooldown = 3.5,
                hold_advance = false
            },
        },
        storm_elemental = {
            gold = 100,
            hp = 3500,
            armor = 0.7,
            magic_armor = 0,
            speed = 20,
            lives_cost = 2,
            basic_attack = {
                cooldown = 2,
                damage_min = 66,
                damage_max = 100,
                damage_radius = 100,
                damage_type = DAMAGE_MAGICAL
            },
            ranged_attack = {
                cooldown = 2,
                min_range = 130,
                max_range = 165,
                damage_min = 66,
                damage_max = 100,
                damage_type = DAMAGE_MAGICAL,
                hold_advance = true
            },
            tower_block = {
                range = 200,
                duration = 15,
            },
        },
        water_sorceress = {
            gold = 35,
            hp = 650,
            armor = 0,
            magic_armor = 0.5,
            speed = 32,
            lives_cost = 1,
            ranged_attack = {
                range = 180,
                damage_min = 35,
                damage_max = 50,
                damage_type = DAMAGE_MAGICAL,
                cooldown = 1.5
            },
            melee_attack = {
                damage_min = 19,
                damage_max = 28,
                damage_type = DAMAGE_MAGICAL,
                cooldown = 1.5
            },
            melee_attack_escupida = {
                damage_min = 20,
                damage_max = 29,
                damage_type = DAMAGE_MAGICAL,
                cooldown = 3
            },
            heal_wave = {
                heal_max = 75,
                heal_min = 40,
                cooldown = 25,
                first_cooldown = 5,
                nodes_range = 30,
                safe_nodes = 50,
                damage_min = 10,
                damage_max = 20,
                damage_type = DAMAGE_MAGICAL
            },
        },
        fan_guard = {
            gold = 45,
            hp = 1500,
            walking_armor = 0.5,
            blocking_armor = 0,
            walking_magic_armor = 0.5,
            blocking_magic_armor = 0,
            speed = 28,
            lives_cost = 1,
            basic_attack = {
                cooldown = 2,
                damage_max = 55,
                damage_min = 35,
                damage_type = DAMAGE_PHYSICAL,
                hit_damage_factor = {0.1, 0.9}
            },
            heavy_attack = {
                cooldown = 5,
                damage_max = 110,
                damage_min = 78,
                damage_type = DAMAGE_PHYSICAL,
                hit_damage_factor = {0.1, 0.9}
            },
        },
        hellfire_warlock = {
            gold = 50,
            hp = 600,
            armor = 0,
            magic_armor = 90,
            speed = 20,
            lives_cost = 1,
            melee_vertical = {
                cooldown = 1.5,
                damage_max = 48,
                damage_min = 30,
                damage_type = DAMAGE_PHYSICAL
            },
            melee_horizontal = {
                cooldown = 1.5,
                damage_max = 48,
                damage_min = 30,
                damage_type = DAMAGE_PHYSICAL
            },
            ranged = {
                cooldown = 2,
                damage_max = 48,
                damage_min = 30,
                radius = 30,
                range = 150,
                damage_type = DAMAGE_MAGICAL,
                flaming_ground = {
                    duration = 8,
                }
            },
            summon_fox = {
                loop_duration = 5,
                first_cooldown = 13,
                cooldown = 40,
                cancelled_cooldown = 10, -- when skill gets cancelled
                nodes_limit = 60
            },
        },
        citizen = {
            gold = 5,
            hp = 175,
            armor = 0,
            magic_armor = 0,
            speed = 36,
            basic_attack = {
                damage_min = 7,
                damage_max = 11,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 1
            },
            special_attack = { -- needs proper balance for final creeps 
                damage_min = 15,
                damage_max = 25,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 8
            },
        },
        terracota = {
            gold = 0,
            hp = 220,
            armor = 0,
            magic_armor = 0,
            speed = 20,
            basic_attack = {
                damage_min = 14,
                damage_max = 23,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 3
            },
        },
        big_terracota = {
            gold = 0,
            hp = 900,
            armor = 0,
            magic_armor = 0,
            speed = 20,
            basic_attack = {
                damage_min = 32,
                damage_max = 45,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 3
            },
        },
        palace_guard = {
            gold = 6,
            hp = 185,
            armor = 0.25,
            magic_armor = 0,
            speed = 42,
            basic_attack = {
                damage_min = 19,
                damage_max = 28,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 3
            },
            special_attack = {
                damage_min = 19,
                damage_max = 28,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 3
            },
        },
        golden_eyed = {
            gold = 100,
            hp =4500,
            armor = 0,
            magic_armor = 0,
            speed = 25,
            basic_attack = {
                damage_min = 70,
                damage_max = 100,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 3,
                damage_radius = 50,
            },
            aura = {
                cooldown = {9, 9, 9, 7},
                trigger_range = 80,
                aura_radius = 150,
                cycle_time = 0.25,
                min_targets = 2,
                duration = 3,
                nodes_limit_start = 30,
                nodes_limit_end = 30,
                 -- can the unit apply the armor mod to itself?
                 -- doesn't count for min_targets
                target_self = false,
                mod = {
                    duration = 3, -- can't be less than cycle time
                    speed_factor = 1.8,
                }
            },
        },
        doom_bringer = {
            gold = 45,
            hp = 1600,
            armor = 0.5,
            magic_armor = 0,
            speed = 28,
            basic_attack = {
                damage_min = 140,
                damage_max = 210,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 2.5
            },
            tower_curse = {
                first_cooldown = 5,
                cooldown = 10,
                duration =8;
                nodes_limit = 20,
                range = 200,
            },
        },
        demon_minotaur = {
            gold = 70,
            hp = 3000,
            armor = 0.8,
            magic_armor = 0,
            speed = 20,
            lives_cost = 2,
            basic_attack = {
                cooldown = 3,
                damage_max = 90,
                damage_min = 60
            },
            charge = {
                damage_min = 150,
                damage_max = 190,
                damage_type = DAMAGE_PHYSICAL,
                max_duration = 1e99,
                speed = 120,
                range_jump = 50,
                range_damage = 100,
            }
        },
        boss_dragon = {
            death_taps_per_mouth_phase = 5,
            death_duration = 12,
            campaign = {
                node_fissure_fixed = {26, 07, 10, 14, 07, 10, 14},
                path_fissure_fixed = {01, 02, 02, 02, 03, 03, 03},
                pre_fight_block_power = {
                    waves           = { },
                    first_cooldown  = { },
                    cooldown        = { },
                    max_casts       = { },
                    duration        = { },
                },
                pre_fight_fissure = {
                    waves           = {03, 06, 07, 08, 10, 13, 15},
                    first_cooldown  = {13, 07, 14, 01, 24, 01, 14},
                    cooldown        = {00, 00, 00, 00, 26, 00, 35},
                    max_casts       = {01, 01, 01, 01, 01, 01, 02},
                    duration        = {18, 14, 33, 58, 45, 72, 30,
                                            ['boss_jump'] = 1e99},
                    path = {1, 1, 2, 3},
                    node = {50, 41, 40, 36},
                },
                pre_fight_block_towers = {
                    waves           = {05, 09, 11, 12, 14, 15},
                    first_cooldown  = {20, 13, 12, 20, 07, 35},
                    cooldown        = {00, 33, 00, 19, 40, 00},
                    max_casts       = {01, 02, 01, 02, 02, 01},
                    quantity        = {01, 02, 02, 02, 02, 03,
                                            ['boss_jump'] = 3,
                                            ['TEEN_REDBOY_1'] = 3},
                    side            = {{{07, 08, 09}}, {{07, 08, 09}}, {{04, 03, 06}}, {{07, 08, 09}}, {{07, 08, 09}}, {{04, 03, 06}},
                                            ['boss_jump'] = {{07, 08, 09}, {07, 08, 09}},
                                            ['TEEN_REDBOY_1'] = {{07, 08, 09}, {07, 08, 09}}},
                    repair_cost = 100,
                    duration = 30,
                },
                pre_fight_meteorite = {
                    fire_duration   = 15,
                    waves           = {04, 07, 10, 14},
                    first_cooldown  = {00, 00, 00, 00},
                    cooldown        = {00, 00, 00, 00},
                    max_casts       = {01, 01, 01, 01},
                    side            = {'right', 'left', 'left', 'right',
                                            ['bossfight_start'] = 'left',
                                            ['boss_jump'] = 'right'},
                },
            },
            heroic = {
                no_boss = true,
                node_fissure_fixed = {26, 07, 10, 14, 07, 10, 14},
                path_fissure_fixed = {01, 02, 02, 02, 03, 03, 03},
            },
            iron = {
                node_fissure_fixed = {26, 07, 10, 14, 07, 10, 14},
                path_fissure_fixed = {01, 02, 02, 02, 03, 03, 03},
                pre_fight_block_power = {
                    waves           = {},
                    first_cooldown  = {},
                    cooldown        = {},
                    max_casts       = {},
                    duration        = {},
                },
                pre_fight_fissure = {
                    waves           = {01},
                    first_cooldown  = {00},
                    cooldown        = {42},
                    max_casts       = {09},
                    duration        = {33},
                    path = {1, 1, 2, 3},
                    node = {50, 41, 40, 36},
                },
                pre_fight_block_towers = {
                    waves           = {01},
                    first_cooldown  = {35},
                    cooldown        = {45},
                    max_casts       = {08},
                    quantity        = {01},
                    side            = {{{47, 48, 49}, {43, 44, 45, 46}, {47, 48, 49}, {43, 44, 45, 46}, {47, 48, 49}, {47, 48, 49}, {43, 44, 45, 46}, {43, 44, 45, 46}}},
                    repair_cost = 100,
                    duration = 30,
                },
                pre_fight_meteorite = {
                    waves           = {},
                    first_cooldown  = {},
                    cooldown        = {},
                    max_casts       = {},
                    side            = {},
                },
            },
        },
        boss_redboy_teen = {
            hp = 19000,
            armor = 0,
            magic_armor = 0,
            speed = 11,
            spawn_pos = {path = 2, node_pos = v(137, 413)},
            basic_attack = {
                damage_min = 300,
                damage_max = 400,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 1.5
            },
            groundfire = { -- summon ash elemental
                first_cooldown = 5000,
                cooldown = 14,
                nodes_limit = 50,
            },
            heartfire = { -- ui block power
                first_cooldown = 5000,
                cooldown = 20,
                duration = 5,
                nodes_limit = 40,
            },
            skyfire = { -- meteorites
                activate_on_positions = {
                -- path = {{pos, side}, {pos, side}, {pos, side}, {pos, side}}
                    -- [2] = {{pos=v(137, 413), side='left'}},
                    -- [3] = {{pos=v(906, 341), side='right'}}
                },
                bossfight_start_meteorite_side = 'left',
            },
            stun_towers = { -- stun towers
                first_cooldown = 5,
                cooldown = 20,
                nodes_limit = 40,
                side = 'TEEN_REDBOY_1'
            },
            change_path = {
                node_start_pos = v(155, 225),
                target = {path = 3, node_pos = v(906, 341)},
                meteorite_side = 'right',
            },
            area_attack = {
                damage_min = 400,
                damage_max = 500,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 5,
                damage_radius = 50,
            },
            fireabsorb = {
                damage_min = 600,
                damage_max = 700,
                damage_type = DAMAGE_MAGICAL,
                cooldown = 14,
                first_cooldown = 10,
                damage_radius = 75,
                absorb_radius = 100,
                minimum_fires = 1,
                nodes_limit = 20,
            }
        },
        boss_princess = {
            waves = {
                shield = {
                    duration = 40,
                    health = 1500,
                    armor = 0,
                    magic_resistance = 0,
                },
                illusory_summon = {
                    
                    -- [WAVE] = {config}
                    [7] = {first_cd = {3}, cd = 13, wave = {'mud_spawner_w7_1', 'mud_spawner_w7_2', 'mud_spawner_w7_3'}},
                    [10] = {first_cd = {23}, cd = 26, wave = {'mud_spawner_w10_1', 'mud_spawner_w10_2'}},
                    [12] = {first_cd = {20}, cd = 16, wave = {'mud_spawner_w12_1'}},
                    [15] = {first_cd = {1}, cd = 25, wave = {'mud_spawner_w15_1', 'mud_spawner_w15_2'}},
                },
                stun_hero = {
                    ['WARNING_DURATION'] = 4,
                    ['DURATION'] = 13,
                    
                    -- [WAVE] = {config}
                    [5] = {first_cd = {5}, cd = 15},
                    [9] = {first_cd = {8}, cd = 12.5},
                    -- [12] = {first_cd = {33}, cd = 10},
                    [14] = {first_cd = {13}, cd = 8},
                    [15] = {first_cd = {10}, cd = 7.5},
                },
                tower_curse = {
                    -- ['DURATION'] = 14,
                    quantity_formations_spawns = 1,
                    spawn_every = 5,
                    spawn_formations = {
                        {
                            {enemy = 'enemy_big_terracota', subpath = 1},
                            {enemy = 'enemy_terracota', subpath = 3, delay = 2},
                            {enemy = 'enemy_terracota', subpath = 2},

                            {enemy = 'enemy_terracota', subpath = 3, delay = 2},
                            {enemy = 'enemy_terracota', subpath = 2},
                            
                        },
                    },
                    holders_not_to_block = {'3', '4', '5'},

                    -- [WAVE] = {config}
                    [12] = {first_cd = {10}, cd = 18, towers = {1, 2, 6, 7, 8}},
                    [14] = {first_cd = {8}, cd = 18, towers = {1, 2, 6, 7, 8}},
                    [15] = {first_cd = {6}, cd = 11, towers = {1, 2, 6, 7, 8}},
                }
            },
            bossfight = {
                hp = 7500,
                armor = 0,
                magic_armor = 0,
                speed = 18,
                spawn_pos = {path = 13, node_pos = v(605, 355)},
                basic_attack = {
                    damage_min = 100,
                    damage_max = 200,
                    damage_type = DAMAGE_PHYSICAL,
                    cooldown = 1e99
                },
                area_attack = {
                    damage_min = 200,
                    damage_max = 330,
                    damage_type = DAMAGE_PHYSICAL,
                    cooldown = 2.5,
                    radius = 100
                },
                ranged_area_attack = {
                    damage_min = 133,
                    damage_max = 200,
                    damage_type = DAMAGE_PHYSICAL,
                    radius = 70,
                    cooldown = 6,
                    max_range = 250,
                    min_range = 100,
                },
                tower_curse = {
                    first_cooldown = 58,
                    cooldown = 14.5,
                    quantity_formations_spawns = 1,
                    nodes_limit = 20,
                    range = 350,
                    spawn_every = 5,
                    spawn_formations = {
                        {
                            {enemy = 'enemy_big_terracota', subpath = 1},
                            {enemy = 'enemy_terracota', subpath = 3, delay = 2},
                            {enemy = 'enemy_terracota', subpath = 2},
                            
                            {enemy = 'enemy_big_terracota', subpath = 1, delay = 4},
                            
                        },
                    },
                    holders_not_to_block = {'3', '4', '5', '9', '10', '11', '12'},
                },
                stun_hero = {
                    first_cooldown = 11,
                    cooldown = 21,
                    nodes_limit = 20,
                    range = 1e99,
                    warning_duration = 4,
                    duration = 10,
                },
                illusory_summon = {
                    first_cooldown = 1e99,
                    cooldown = 1e99,
                    shield_duration = 10,
                    shield_hp = 2000,
                    shield_armor = 0,
                    shield_magic_resistance = 0,
                    nodes_limit = 20,
                    manual_wave_name = 'ILLUSORY_SUMMON_1'
                },
                change_paths = {
                    cooldown = 70,
                    config = {path = 11, node_pos = v(490, 389)},
                },
                illusory_self = {
                    first_cooldown = 2,
                    cooldown = 24,
                    nodes_limit = 20,
                    clon_config = {
                        hp = 9000,
                        armor = 0,
                        magic_armor = 0,
                        speed = 15,
                        spawn_pos = {{path = 9, node_pos = v(529, 267)}, {path = 10, node_pos = v(480, 389)}, {path = 12, node_pos = v(610, 450)}},
                        basic_attack = {
                            damage_min = 55,
                            damage_max = 88,
                            damage_type = DAMAGE_PHYSICAL,
                            cooldown = 2,
                        },
                        ranged_area_attack = {
                            damage_min = 105,
                            damage_max = 170,
                            damage_type = DAMAGE_PHYSICAL,
                            radius = 70,
                            cooldown = 6,
                            max_range = 250,
                            min_range = 100,
                        },
                        tower_curse = {
                            first_cooldown = 1e99,
                            cooldown = 20,
                            duration = 20,
                            nodes_limit = 20,
                            range = 400,
                        },
                        change_paths = {
                            {from_path=2, from_pos=v(200,200), to_path=4, to_pos=v(200,200)},
                        },
                    }
                },
            },
        },
        boss_bull_king = {
            hp = 15000,
            armor = 0.8,
            magic_armor = 0.8,
            speed = 16,
            spawn_pos = {path = 12, node_pos = v(80, 275)},
            basic_attack = {
                damage_min = 450,
                damage_max = 600,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 2.5
            },
            area_attack = {
                first_cooldown = 10,
                cooldown = 15,
                damage_type = DAMAGE_PHYSICAL,
                damage_max = 60,
                damage_min = 50,
                damage_radius = 450,
                nodes_limit = 20,
                min_targets = 1,
                stun_duration = 6,
                stun_tower_duration = 6,
                max_towers_block = 6,
                max_range_towers_block = 450,
                min_range_towers_block = 0,
            },
        },
    },
}

local towers = {
    arcane_wizard = {
        price = {110, 150, 220, 280},
        stats = {
            damage = 8,
            range = 5,
            cooldown = 5,
        },
        shared_min_cooldown = 2,
        basic_attack = {
            damage_min = {12, 25, 48, 70},
            damage_max = {18, 47, 80, 132},
            cooldown = 2,
            range = {160, 168, 176, 186},
            damage_every = fts(2),
        },
        disintegrate = {
            price = {300, 150, 150},
            range = 186,
            cooldown = {30, 28, 26},
            boss_damage = {800, 1200, 1500},
        },
        empowerment = {
            price = {200, 200, 200},
            cooldown = {1, 1, 1},
            min_range = 80,
            max_range = 220,
            damage_factor = {1.15, 1.25, 1.4},
            s_damage_factor = {0.15, 0.25, 0.40},
        }
    },
    elven_stargazers = {
        price = {120, 180, 240, 310},
        stats = {
            damage = 9,
            range = 5,
            cooldown = 3,
        },
        shared_min_cooldown = 2,
        basic_attack = {
            damage_min = {4, 11, 22, 34},
            damage_max = {6, 18, 32, 52},
            cooldown = 2,
            range = {160, 170, 185, 200},
            damage_every = fts(1),
            ray_timing = 0.2,
        },
        teleport = {
            price = {250, 100, 100},
            teleport_nodes_back = {20, 25, 30}, 
            cooldown = {20, 18, 16},
            max_targets = {3,4,6}
        },
        stars_death = {
            price = {150, 150, 150},
            stars = {3,4,5},
            chance = {1, 1, 1},
            min_range = 0,
            max_range = 200,
            damage_min = {18, 28, 36},
            damage_max = {30, 42, 54},
            damage_type = DAMAGE_MAGICAL,
            stun = 0.8
        }
    },
    tricannon = {
        price = {140, 200, 280, 360},
        stats = {
            damage = 7,
            range = 4,
            cooldown = 2,
        },
        shared_min_cooldown = 3,
        basic_attack = {
            bomb_amount = {3, 3, 3, 3},
            damage_min = {3, 8, 16, 28},
            damage_max = {5, 12, 24, 42},
            damage_radius = 90/2,
            cooldown = 3,
            range = {360/2, 360/2, 360/2, 360/2},
            time_between_bombs = fts(1)
        },
        bombardment = {
            price = {250, 200, 200},
            range = 360/2,
            cooldown = {15,15,15},
            damage_radius = 100/2,
            damage_min = {24, 32, 40},
            damage_max = {48, 64, 80},
            spread = {20, 20, 20},
            node_skip = {11, 6, 4}
        },
        overheat = {
            price = {150, 150, 150},
            cooldown = {24,24,24},
            duration = {3, 6, 9}, --skill (1, 2, 3 attacks)
            decal = {
                radius = 80/2,
                duration = 5, --decal
                effect = {
                    duration = 3, --burn mod
                    damage = {3, 5, 7},
                    damage_every = 0.25,
                    s_damage = {12, 20, 28},
                }
            },
        }
    },
    paladin_covenant = {
        price = {70, 120, 180, 250},
        stats = {
            damage = 2,
            hp = 9,
            armor = 5,
        },
        rally_range = 290/2,
        max_soldiers = 3,
        soldier = {
            armor = {0, 0.1, 0.25, 0.40},
            dead_lifetime = 12,
            hp = {40, 80, 120, 180},
            regen_hp = {6, 12, 18, 28},
            speed = 75,
            basic_attack = {
                damage_min = {1, 3, 6, 10},
                damage_max = {3, 5, 9, 14},
                cooldown = 1,
                range = 70
            }
        },
        lead = {
            price = {250},
            soldier_veteran = {
                armor = 0.6,
                hp = 200,
                regen_hp = 30,
                basic_attack = {
                    damage_max = 22,
                    damage_min = 14,
                    },
                aura_damage_buff_factor = 1.2,
                s_aura_damage_buff_factor = 0.2,
                aura_range = 140/2,
                aura_cooldown = {20},
                aura_duration = 8
            },
        },
        healing_prayer = {
            price = {140, 140, 140},
            health_trigger_factor = {0.25, 0.25, 0.25},
            heal = {3, 6, 9},
            heal_every = 0.25,
            s_healing = {12, 20, 32},
            duration = 4,
            cooldown = {28, 25, 22}
        },
    },
    royal_archers = {
        stats = {
            damage = 5,
            cooldown = 8,
            range = 7,
        },
        price = {70, 100, 160, 250},
        basic_attack = {
            damage_min = {3, 8, 15, 26},
            damage_max = {5, 11, 23, 38},
            cooldown = 0.8,
            range = {160, 170, 185, 200},
            damage_type = DAMAGE_PHYSICAL,
        },
        armor_piercer = {
            price = {120, 120, 120},
            range_trigger = 140, -- Range to find foremost
            range_effect = 200, -- Range to shoot arrows
            cooldown = {15, 15, 15},
            damage_min = {38, 78, 120},
            damage_max = {58, 118, 150},
            damage_type = DAMAGE_PHYSICAL,
            armor_penetration = {0.2, 0.35, 0.5},
            nearby_range = 100, -- Range from foremost enemy to select targets
        },
        rapacious_hunter = {
            price = {200, 200, 200},
            damage_min = {18, 34, 52},
            damage_max = {26, 52, 78},
            range = 220,
            max_distance_from_tower = 200,
            attack_cooldown = 2,
            damage_type = DAMAGE_PHYSICAL,
            shoot_range = 50/2,
        }
    },
    viper_goblins = {
        price = {100, 120, 145, 200},
        basic_attack = {
            damage_min_poison = {1, 8, 15, 6},
            damage_max_poison = {2, 12, 22, 12},
            damage_min = {2, 23, 26, 59},
            damage_max = {3, 24, 27, 60},
            cooldown = 1.2,
            range = 330/2,
            damage_type = DAMAGE_PHYSICAL,
            damage_every = 1,
            modifier_duration = 3,
        },
        curse_of_the_snake = {
            cooldown = {20, 15, 10},
            damage_percentage = {10, 10, 10},
            damage_min = {20, 25, 30},
            price = {110, 110, 110},
            damage_every = 1,
        },
        snake_bomb = {
            price = {150, 150, 150},
            received_damage_factor = {2, 2, 2, 2},
            radius = 170/2,
            cooldown = {15, 15, 15},
            min_targets = 2,
            slow_factor = {0.5, 0.5, 0.5},
            duration = {2, 2, 2}
        }
    },
    arborean_emissary = {
        price = {100, 130, 170, 230},
        stats = {
            damage = 3,
            range = 8,
            cooldown = 6,
        },
        shared_min_cooldown = 1.5,
        rally_range = 280 * 1.28 / 2,
        basic_attack = {
            damage_min = {3, 7, 11, 17},
            damage_max = {6, 13, 20, 32},
            damage_type = DAMAGE_MAGICAL,
            cooldown = 1.2,
            range = {160, 180, 200, 220},
            received_damage_factor = {1.2, 1.3, 1.4, 1.5},
            modifier_duration = {5, 5, 5, 5},
        },
        gift_of_nature = {
            price = {120, 120, 120},
            cooldown = {20, 20, 20},
            max_range = 230,
            radius = 150/2,
            duration = {6, 6, 6},
            heal_min = {4, 8, 12},
            heal_max = {4, 8, 12},
            s_heal = {16, 32, 48},
            heal_every = 0.25,
            inflicted_damage_factor = {1, 1, 1},
        },
        wave_of_roots = {
            price = {160, 160, 160},
            cooldown = {15, 14, 12},
            trigger_range = 200,
            effect_range = 220,
            min_targets = 2,
            max_targets = {3, 5, 8},
            mod_duration = {3, 3, 3},
            damage_min = {40, 40, 40},
            damage_max = {40, 40, 40},
            s_damage = {40, 40, 40},
            damage_type = DAMAGE_TRUE,
        }
    },
    elder_portal = {
        price = {105, 145, 220, 280},
        shared_min_cooldown = 3,
        basic_attack = {
            damage_min = {10, 18, 35, 55},
            damage_max = {14, 40, 75, 125},
            damage_type = DAMAGE_MAGICAL,
            damage_radius = 30,
            cooldown = 3,
            duration = {10, 10, 10, 10},
            range = {320/2, 320/2, 320/2, 320/2},
            spawn_offset = fts(0),
            distance_between = 30,
        },
        orbital_cannon = {
            price = {100, 150, 150},
            cooldown = {15, 12, 10},
            damage_min = {40, 50, 60},
            damage_max = {45, 55, 65},
            damage_radius = 80,
            damage_type = DAMAGE_PHYSICAL,
        },
        teleport = {
            price = {100, 150, 150},
            cooldown = {15, 12, 10},
            max_targets = {1, 2, 3},
            min_targets = 2,
            max_times_applied = 3,
            nodes_offset = {-20, -20, -20},
        }
    },
    demon_pit = {
        price = {80, 140, 220, 280},
        stats = {
            damage = 4,
            hp = 3,
            armor = 1,
        },
        basic_attack = {
            range = {320/2, 320/2, 320/2, 320/2},
            cooldown = {4, 4, 4, 4},
            duration = 10,
            armor = 0,
            hp_max = {12, 16, 20, 25},
            max_speed = 90,
            regen_health = 1,
            melee_attack = {
                cooldown = {1, 1, 1, 1},
                damage_max = {4, 8, 12, 18},
                damage_min = {2, 5, 8, 12},
                range = 60,
            },
        },
        big_guy = {
            price = {200, 100, 100},
            cooldown = {40, 40, 40},
            max_range = 320/2,
            duration = 30,
            armor = 0,
            hp_max = {100, 150, 200},
            max_speed = 20,
            regen_health = 1,
            explosion_damage = {100, 150, 250},
            explosion_range = {60, 60, 60},
            explosion_damage_type = DAMAGE_EXPLOSION,
            melee_attack = {
                cooldown = {1},
                damage_max = {29, 42, 55},
                damage_min = {19, 28, 37},
                range = 80/2,
            },
        },
        master_exploders = {
            price = {150, 150, 150},
            explosion_damage_factor = {1.2, 1.4, 1.6},
            s_damage_increase = {0.2, 0.4, 0.6},
            burning_duration = {2.75, 3.75, 4.75},
            s_burning_duration = {3, 4, 5},
            burning_damage_min = {2, 4, 6},
            burning_damage_max = {4, 6, 10},
            s_total_burning_damage_min = {8, 16, 24},
            s_total_burning_damage_max = {16, 24, 40},
            damage_every = 0.25,
            damage_type = DAMAGE_TRUE,
        },
        demon_explosion = {
            damage_min = {2, 5, 8, 12},
            damage_max = {4, 8, 12, 18},
            range = {40,40,40,40},
            damage_type = DAMAGE_EXPLOSION,
            stun_duration = {0.25, 0.4, 0.6, 0.8},
        }
    },
    rocket_gunners = {
        price = {100, 140, 190, 270},
        stats = {
            damage = 7,
            range = 10,
            cooldown = 5,
        },
        rally_range = {130, 145, 160, 175},
        max_soldiers = 2,
        sting_missiles = {
            cooldown = {24, 20, 16},
        },
        soldier = {
            armor = {0.1, 0.15, 0.20, 0.25},
            hp = {30, 50, 70, 100},
            dead_lifetime = 10, -- time to respawn
            speed_flight = 250,
            speed_ground = 75,
            regen_hp = {5, 8, 11, 15},
            melee_attack = {
                damage_max = {8, 19, 36, 60},
                damage_min = {5, 13, 24, 40},
                cooldown = 2,
                range = 72,
            },
            ranged_attack = {
                cooldown = 2,
                max_range = {150, 150, 150, 150},
                min_range = {10, 10, 10, 10},
                damage_max = {7, 18, 34, 58},
                damage_min = {5, 12, 22, 38},
            },
            phosphoric = {
                price = {200, 200, 200},
                armor_reduction = {0.01, 0.02, 0.03},
                damage_radius = 80/2,
                damage_area_max = {16, 22, 27},
                damage_area_min = {13, 17, 21},
                damage_factor = {1, 1, 1},
            },
            sting_missiles = {
                price = {250, 100, 100},
                max_range = {200, 200, 200},
                min_range = {20, 20, 20},
                damage_type = DAMAGE_INSTAKILL,
                hp_max_target = {300, 600, 900},
            },
        },
    },
    necromancer = {
        price = {100, 140, 200, 260},
        stats = {
            damage = 4,
            range = 7,
            cooldown = 6,
        },
        shared_min_cooldown = 2,
        basic_attack = {
            damage_min = {4, 12, 20, 36},
            damage_max = {8, 20, 36, 68},
            cooldown = 1.5,
            range = {160, 170, 185, 200},
            damage_type = DAMAGE_MAGICAL,
            always_new_bullet = false,
        },
        skill_debuff = {
            mod_duration = {1, 1, 1},
            damage_factor = {1.5, 2, 2.5}, 
            s_damage_factor = {0.5, 1, 1.5},
            aura_duration = {10, 10, 10},
            price = {120, 120, 120},
            range = 400/2,
            radius = 250/2,
            cooldown = {16, 14, 12},
            min_targets = 2,
        },
        skill_rider = {
            price = {200, 200, 200},
            damage_min = {60, 110, 150},
            damage_max = {60, 110, 150},
            s_damage = {60, 110, 150},
            cooldown = {30, 26, 22},
            damage_type = DAMAGE_TRUE,
            duration = 5,
            speed = 150,
            min_targets = 2,
            range = 180,
            radius = 150/2,
        },
        curse = {
            duration = 3,
            max_skeletons = {2, 3, 4, 5},
            max_golems = 1,
            max_units_total = 30,
        },
        skeleton = {
            armor = {0, 0, 0, 0},
            hp_max = {50, 50, 50, 50},
            max_speed = 36,
            dead_lifetime = 10,
            melee_attack = {
                cooldown = {1, 1, 1, 1},
                damage_max = {5, 5, 5, 5},
                damage_min = {3, 3, 3, 3},
                range = 72,
            }
        },
        skeleton_golem = {
            armor = {0, 0, 0, 0},
            hp_max = {150, 150, 150, 150},
            max_speed = 28,
            dead_lifetime = 10,
            regen_cooldown = 1,
            regen_hp = 10,
            melee_attack = {
                cooldown = {1, 1, 1, 1},
                damage_max = {12, 12, 12, 12},
                damage_min = {8, 8, 8, 8},
                range = 72,
            }
		},
        spawn_delay_min = 4,
        spawn_delay_max = 4,
    },
    ballista = {
        stats = {
            damage = 8,
            cooldown = 3,
            range = 6,
        },
        price = {90, 130, 180, 260},
        turn_speed = 15,  -- degree angles per frame
        basic_attack = {
            damage_min = {3, 7, 14, 25},
            damage_max = {5, 11, 22, 38},
            cooldown = 2.5,
            range = {160, 175, 190, 210},
            damage_type = DAMAGE_PHYSICAL,
            burst_count = 5,
        },
        skill_final_shot = {
            price = {250, 100, 100},
            cooldown = {4, 4, 4},
            damage_factor = {1.5, 2, 2.5},
            s_damage_factor = {0.5, 1, 1.5},
            stun_time = 60, -- in frames
            s_stun = 2,
        },
        skill_bomb = {
            price = {200, 200, 200},
            cooldown = {24, 20, 16},
            max_range = 250,
            min_range = 80,
            damage_radius = 110/2,
            damage_min = {82, 118, 142},
            damage_max = {124, 176, 214},
            duration = {6, 6, 6},
            damage_type = DAMAGE_PHYSICAL,
            min_targets = 2,
            node_prediction = 40,
        },
    },
    flamespitter = {
        stats = {
            damage = 5,
            cooldown = 3,
            range = 5,
        },
        price = {130, 190, 270, 370},
        turn_speed = 8,  -- degree angles per frame
        burning = {
            damage = {1, 2, 3, 4}, -- per tick
            duration = 3,
            cycle_time = 0.25,  -- how often it is applied
        },
        basic_attack = {
            damage_min = {2, 5, 12, 16},
            damage_max = {3, 10, 16, 30},
            duration = 0.5, -- how long the flame is on per attack
            cycle_time = 0.3,  -- how often it is applied
            cooldown = 3,
            range = {180, 180, 180, 180},
            damage_type = DAMAGE_TRUE,
        },
        skill_bomb = {
            price = {150, 150, 150},
            cooldown = {20, 20, 20},
            max_range = 300,
            min_range = 100,
            damage_radius = 100/2,
            damage_max = {80, 160, 280},
            damage_min = {80, 160, 280},
            s_damage = {80, 160, 280},
            damage_type = DAMAGE_PHYSICAL,
            min_targets = 3,
            node_prediction = 40,
            burning = {
                damage = 4, -- per tick
                s_damage = 16,
                duration = 5,
                cycle_time = 0.25,  -- how often it is applied
            },
        },
        skill_columns = {
            price = {200, 200, 200},
            cooldown = {25, 25, 25},
            max_range = 150,
            min_range = 0,
            columns = 6,
            radius_in = 50/2,
            radius_out = 100/2,
            damage_in_max = {70, 180, 300},
            damage_in_min = {70, 180, 300},
            s_damage_in = {70, 180, 300},
            damage_in_type = DAMAGE_DISINTEGRATE,
            damage_out_max = {42, 108, 180}, --0.6 max dmg
            damage_out_min = {42, 108, 180}, --0.6 max dmg
            s_damage_out = {42, 108, 180},
            damage_out_type = DAMAGE_PHYSICAL,
            min_targets = 2,
            stun_time = 30, -- in frames
            s_stun = 1,
        },
    },
    -- Brewmasters
    barrel = {
        stats = {
            damage = 5,
            cooldown = 2,
            range = 3,
        },
        price = {120, 180, 260, 360},
        rally_range = 290/2,
        basic_attack = {
            damage_min = {7, 18, 35, 60},
            damage_max = {11, 28, 53, 90},
            damage_radius = 120/2,
            cooldown = 2.5,
            range = {170, 170, 170, 170},
            debuff = {
                damage_reduction = {0.5, 0.5, 0.5, 0.5},
                duration = {3, 3, 3, 3},
            },
        },
        skill_warrior = {
            price = {200, 100, 100},
            cooldown = {12, 12, 12},
            min_targets = 1,
            range = 240,
            entity = {
                speed = 75,
                cooldown = 1,
                damage_min = {26, 37, 48},
                damage_max = {38, 55, 72},
                damage_type = DAMAGE_PHYSICAL,
                hp_max = {100, 140, 180},
                armor = {0.0, 0.0, 0.0},
                range = 72,
                duration = 10
            },
        },
        skill_barrel = {
            price = {200, 200, 200},
            cooldown = {24, 24, 24},
            range = {180, 180, 180},
            radius = 160/2,
            min_targets = 3,
            duration = 4,
            explosion = {
                damage_min = {80, 176, 288},
                damage_max = {120, 264, 432},
                damage_type = DAMAGE_PHYSICAL,
                damage_radius = 120/2
            },
            poison = {
                damage_max = 1,
                damage_min = 1,
                s_damage = 4,
                duration = 5,
                every = 0.25
            },
            slow = {
                factor = 0.5,
                duration = 1,
            }
        },
    },
    sand = {
        stats = {
            damage = 6,
            cooldown = 8,
            range = 5,
        },
        price = {80, 120, 170, 260},
        basic_attack = {
            damage_min = {3, 6, 10, 18},
            damage_max = {5, 10, 16, 26},
            cooldown = 0.8,
            range = {145, 155, 170, 180},
            damage_type = DAMAGE_PHYSICAL,
            max_bounces = {1, 2, 3, 4},
            bounce_range = 140,
            bounce_speed_mult = 1.25,
            bounce_damage_mult = 0.6
        },
        skill_gold = {
            price = {250, 250, 250},
            range_trigger = 150, -- Range to find foremost
            range_effect = 190, 
            cooldown = {8, 8, 8},
            damage_min = {56, 118, 184},
            damage_max = {56, 118, 184},
            s_damage = {56, 118, 184},
            damage_type = DAMAGE_PHYSICAL,
            gold_chance = 1,
            gold_extra = {4, 8, 12} ,
            max_bounces = 4,
        },
        skill_big_blade = {
            price = {200, 200, 200},
            damage_min = {6, 10, 13},
            damage_max = {10, 16, 19},
            s_damage_min = {24, 40, 52},
            s_damage_max = {40, 64, 76},
            cooldown = {16, 16, 16},
            range = 200,
            min_targets = 2,
            radius = 80 / 2,
            duration = {3, 4, 5},
            damage_every = 0.25,
            slow_factor = 0.75,
            slow_duration = 0.5,
            damage_type = DAMAGE_PHYSICAL,
        }
    },
    ghost = {
        price = {90, 150, 220, 300},
        stats = {
            damage = 3,
            hp = 6,
            armor = 6,
        },
        rally_range = 310/2,
        max_soldiers = 2,
        soldier = {
            armor = {0.2, 0.3, 0.45, 0.6},
            dead_lifetime = 8,
            hp = {30, 50, 75, 100},
            regen_hp = {5, 8, 12, 18},
            speed = 75,
            basic_attack = {
                damage_min = {4, 6, 10, 16},
                damage_max = {6, 10, 16, 24},
                damage_type = DAMAGE_TRUE,
                cooldown = 1,
                range = 70
            }
        },
        extra_damage = {
            price = {200, 100, 100},
            damage_factor = {1.5, 1.75, 2},
            s_damage = {0.5, 0.75, 1},
            cooldown_start = 5,
            duration = 8, --auto refresh
        },
        soul_attack = {
            price = {150,150,150},
            range = 120,
            damage_type = DAMAGE_TRUE,
            damage_min = {60, 120, 180},
            damage_max = {60, 120, 180},
            s_damage = {60, 120, 180},
            slow_factor = 0.6,
            slow_duration = 5,
            damage_factor = 0.5,
            damage_factor_duration = 5,

        },
    },
    ray = {
        stats = {
            damage = 9,
            cooldown = 2,
            range = 4,
        },
        price = {120, 170, 230, 330},
        shared_min_cooldown = 2,
        basic_attack = {
            -- from end of the attack to start of the next attack
            cooldown = 1.5,
            range = {150, 160, 170, 180},
            extra_range_to_stay = 20, -- extra range to keep the chain connected
            -- TOTAL damage over the whole duration
            damage_min = {32, 80, 146, 248}, 
            damage_max = {32, 80, 146, 248},
            damage_type = DAMAGE_MAGICAL,
            -- total attack duration (ray duration, whole animation may be longer)
            duration = 4, 
            -- percentage of the total damage that the tower deals over each second (must add up to 1 and have the same # of elements as duration)
            damage_per_second = {0.10, 0.20, 0.35, 0.35}, 
            damage_every = 0.25,
            slow = {
                factor = 0.8,
            },
        },
        skill_chain = {
            price = {150, 150, 150},
            damage_mult = {0.25, 0.50, 0.75},
            damage_type = DAMAGE_MAGICAL,
            max_enemies = 4, -- in total
            s_max_enemies = 3,
            chain_delay = 0.1,
            chain_range = 100, -- the max range to connect the chain
        },
        skill_sheep = {
            price = {200},
            cooldown = {20},
            range = 200,
            -- duration = 10,
            sheep = {
                hp_mult = 0.5,
                armor = 0,
                magic_armor = 0,
                speed = 28,
                clicks_to_destroy = 5
            }
        }
    },
    -- TWILIGHT LONGBOWS
    dark_elf = {
        stats = {
            damage = 8,
            cooldown = 2,
            range = 9,
        },
        rally_range = 290/2,
        soldier = {
            armor = {0.25, 0.25, 0.25},
            dead_lifetime = 10,
            hp = {60, 90, 120},
            regen_hp = {6, 9, 12},
            speed = 75,
            basic_attack = {
                damage_min = {12, 16, 22},
                damage_max = {16, 24, 34},
                cooldown = 1,
                range = 70,
                damage_type = DAMAGE_PHYSICAL,
            },
            dodge_chance = {0.6, 0.6, 0.6},
        },
        price = {90, 130, 180, 240},
        basic_attack = {
            damage_min = {14, 40, 76, 134},
            damage_max = {22, 54, 92, 146},
            cooldown = 2,
            range = {200, 230, 260, 300},
            damage_type = DAMAGE_PHYSICAL,
        },
        skill_soldiers = {
            price = {150, 150, 150},
            cooldown = {1, 1, 1},
        },
        skill_buff = {
            price = {200},
            extra_damage_min = {1},
            extra_damage_max = {2},
            s_extra_damage_total = {1},
        },
    },
    hermit_toad = {
        stats = {
            damage = 5,
            cooldown = 5,
            range = 8,
        },
        price = {120, 160, 240, 280},
        engineer_basic_attack = {
            damage_min = {7, 15, 29, 45},
            damage_max = {9, 21, 39, 59},
            damage_radius = 60,
            cooldown = 2.5,
            range = {180, 200, 220, 240},
            slow_factor = {0.8, 0.7, 0.6, 0.5},
            slow_decal_duration = {1.75, 1.75, 1.75, 1.75},
            slow_mod_duration = {0.2, 0.2, 0.2, 0.2},
            damage_type = DAMAGE_PHYSICAL,
        },
        mage_basic_attack = {
            damage_min = {7, 17, 29, 47},
            damage_max = {10, 22, 38, 63},
            cooldown = 1.2,
            range = {160, 175, 190, 200},
            damage_type = DAMAGE_MAGICAL,
        },
        power_jump = {
            price = {120, 120, 120},
            cooldown = {35, 30, 25},
            min_targets = 2,
            stun_duration = {2, 3, 4},
            range = 220,
            radius = 100,
            damage_min = {80, 140, 180},
            damage_max = {80, 140, 180},
            damage_type = DAMAGE_PHYSICAL
        },
        power_instakill = {
            price = {300},
            range = 240,
            cooldown = {18},
        },
    },
    --CANNONEER SQUAD
    dwarf = {
        price = {60, 130, 180, 240},
        stats = {
            damage = 5,
            hp = 7,
            armor = 4,
        },
        rally_range = 360/2,
        max_soldiers = 2,
        soldier = {
            armor = {0, 0.1, 0.2, 0.3},
            dead_lifetime = 8,
            hp = {35, 70, 100, 140},
            regen_hp = {6, 12, 18, 28},
            speed = 75,
            melee_attack = {
                damage_max = {4, 10, 18, 26},
                damage_min = {3, 6, 12, 17},
                cooldown = 1,
                range = 72,
            },
            ranged_attack = {
                cooldown = 1.5,
                max_range = {180, 180, 180, 180},
                min_range = {70, 70, 70, 70},
                damage_max = {6, 14, 26, 38},
                damage_min = {4, 10, 18, 26},
            },
        },
        formation = {
            price = {120, 180, 240},
        },

        incendiary_ammo = {
            price = {150, 150, 150},
            cooldown = 12,
            damages_min = {20, 38, 52},
            damages_max = {28, 58, 80},
            damage_radius = 60,
            damage_type = DAMAGE_EXPLOSION,
            burn = {
                duration = 2,
                damage_every = 0.25,  -- how often damages the target
                s_damage = {24, 64, 112},
                damage = {3, 8, 14}, -- per tick
                damage_type = DAMAGE_TRUE,
                aura = { -- area were we apply burn 
                    radius = 50,
                    duration = 0.1,
                    cycle_time = 0.1
                }
            }
        },
    },
    sparking_geode = {
        price = {110, 130, 210, 290},
        stats = {
            damage = 4,
            range = 8,
            cooldown = 7,
        },
        shared_min_cooldown = 2,
        basic_attack = {
            damage_min = {3, 5, 7, 9},
            damage_max = {4, 6, 9, 12},
            cooldown = 2, -- doesnt matter really, look at ray_timing
            range = {130, 140, 150, 160},
            ray_timing_min = {1.05, 0.95, 0.75, 0.55},
            ray_timing_max = {1.15, 1.05, 0.85, 0.65},
            damage_type = DAMAGE_TRUE,
            targeting_style = 1, -- 1 = RANDOM, 2 = ORDERED BY POSITION
            bounces_min = {1, 2, 3, 4},
            bounces_max = {1, 2, 3, 4},
            bounce_range = 140,
            bounce_damage_factor = {1.05, 1.1, 1.15, 1.25},
        },
        crystalize = {
            price = {150, 150, 150},
            cooldown = {31, 28, 25},
            max_targets = {2, 3, 4},
            duration = {5, 5, 5},
            received_damage_factor = {1.3, 1.3, 1.3},
            s_received_damage_factor = {0.3, 0.3, 0.3},
        },
        spike_burst = {
            price = {200, 200, 200},
            cooldown = {40, 35, 30},
            duration = {6, 8, 10},
            radius = 200,
            damage_min = {3, 4, 5},
            damage_max = {3, 4, 5},
            damage_type = DAMAGE_TRUE,
            speed_factor = {0.7, 0.6, 0.5},
            damage_every = 0.5
        }
    },
    pandas = {
        stats = {
            damage = 6,
            hp = 8,
            armor = 0,
        },
        price = {110, 150, 210, 270},
        ranged_attack = {
            damage_min = {4, 7, 10, 15},
            damage_max = {6, 10, 15, 20},
            cooldown = 0.5,
            range = {180, 180, 180, 180},
            damage_type = DAMAGE_TRUE,
        },
        rally_range = 360/2,
        soldier = {
            cooldown = 5,
            retreat_duration = 8,
            armor = {0, 0, 0, 0},
            hp = {60, 90, 120, 170},
            regen_hp = {6, 8, 10, 15},
            speed = 75,
            melee_attack = {
                damage_max = {10, 15, 22, 30},
                damage_min = {7, 12, 19, 25},
                cooldown = 1,
                range = 72,
            },
            thunder = {
                cooldown = {15, 10},
                range = {180, 180},
                damage_min = {12, 22},
                damage_max = {24, 34},
                damage_type = DAMAGE_TRUE,
                damage_area = 100,
                stun_duration = 1.5,
                min_targets = 2,
            },
            hat = {
                cooldown = {8, 8},
                range = {180, 200},
                damage_levels = {
                    [1] = {min=20, max=25},
                    [2] = {min=40, max=50},
                },
                damage_type = DAMAGE_TRUE,
                max_bounces = {2, 4},
                bounce_range = 200,
                bounce_speed_mult = 1.25,
                bounce_damage_mult = 0.8
            },
            teleport = {
                cooldown = {20, 15},
                range = {200, 200},
                damage_min = {3, 6},
                damage_max = {6, 9},
                damage_type = DAMAGE_TRUE,
                nodes_offset_max = {-20, -25},
                max_targets = 5,
                nodes_offset_min = {-24, -24}
            }
        },
        thunder = {
            price = {150, 200},
        },
        hat = {
            price = {150, 200},
        },
        teleport = {
            price = {150, 200},
        }
    },
}

local specials = {
    trees = {
        arborean_sages = {
            range = 350/2,
            cooldown_min = 3,
            cooldown_max = 3,
            damage_min = 10,
            damage_max = 20,
            damage_type = DAMAGE_MAGICAL
        },
        fruity_tree = {
            max_range = 150,
            cooldown_min = 4,
            cooldown_max = 6,
            duration = 5,
            consume_range = 25,
            heal = 100,
            max_fruits = 3
        },
        guardian_tree = {
            max_range = 450,
            min_range = 15,
            cooldown_min = 16,
            cooldown_max = 16,
            effect_duration = 4,
            immune_for_seconds = 3,
            wave_config = {true, true, true, true, true, true, true, true},
            disabled = false,
            sep_nodes_min = 4,
            sep_nodes_max = 5,
            roots_count = 14,
            show_delay_min = 0.04,
            show_delay_max = 0.04,
        },
        heart_of_the_arborean = {
            cooldown_max = 90,
            cooldown_min = 90,
            max_range = 1400,
            damage_radius = 80,
            damage_max = 40,
            damage_min = 30,
            damage_type = DAMAGE_TRUE,
            max_targets = 10,
            min_targets = 10,
            wait_between_shots = fts(2),
            min_dist_between_tgts = 130,
        },
        blocked_holders = {
            price = 60
        },
    },
    terrain_2 = {
        blocked_holders = {
            price = 100
        },
    },
    terrain_3 = {
        blocked_holders = {
            price = 150
        },
    },
    terrain_4 = {
        blocked_holders = {
            price = 150
        },
    },
    terrain_6 = {
        blocked_holders = {
            price = 150
        },
    },
    terrain_7 = {
        spider_floor_webs = {
            slow_factor = 0.3, -- allied units
            sprint_factor = 1.7, -- spider units
        },
    },
    terrain_8 = {
        flaming_ground = {
            dps = {
                damage_min = 2,
                damage_max = 2,
                damage_every = 0.25,
                damage_type = DAMAGE_PHYSICAL,
                duration = 0.25, -- no less than 0.25, low duration will only damage while over the fire,
            },
            sprint = {
                sprint_factor = 1.7,
                duration = 1,
            },
            healing = {
                heal_duration = 1,
                heal_every = 0.25,
                heal_max = 30,
                heal_min = 10,
            }
        },
        elemental_holders = { -- \(^.^)/
            wooden_holder = {
                price = 150,
                first_cooldown = 2, -- pls not less than 2, talk to martin
                cooldown = 50,
                slow_factor = 0.5,
                default_max_range = 200, -- solo para detectar enemigos
                duration = 8,
                damage_every = 0.25,
                damage_type = DAMAGE_TRUE,
                damage_min = 3,
                damage_max = 5,
                range_factor = 1.25,
                rally_range_factor = 1.25,
                skill_detection_range_factor = 0.8
            },
            fire_holder = {
                price = 150,
                first_cooldown = 2,
                cooldown = 52,
                damage_factor = 1.25,
                default_max_range = 200, -- solo para detectar enemigos
            },
            water_holder = {
                price = 150,
                default_max_range = 200, -- solo para detectar enemigos
                healing = {
                    duration = 1, 
                    heal_min = 5,
                    heal_max = 6,
                    heal_every = 1, --Heal tick
                    min_health_factor = 0.8,
                },
                teleport = {
                    cooldown = 20, --TP cd
                    first_cooldown = 2,
                    duration = 9,
                    tp_max_targets = 5,
                    tp_distance_nodes_min = 20,
                    tp_distance_nodes_max = 55,
                    delay_between_tps = 2,
                    tp_radius = 50,

                    -- visuals
                    wander_interval = 1.5, -- time between appearings when not actively tping enemies
                    chase_speed = 40, -- speed when chasing enemies to tp
                }
            },
            earth_holder = {
                price = 150,
                first_cooldown = 2,
                cooldown = 30,
                extra_health_multiplier = 1.25,
                spawn_amount = 1,
                max_spawns = 3,
                soldier = {
                    hp_max = 68,
                    armor = 0.3,
                    max_speed = 24,
                    melee_attack = {
                        damage_max = 30,
                        damage_min = 18,
                        cooldown = 3,
                        range = 50,
                    },
                },
                holder_spawn_pos = {
                    ["22"] = {
                        {
                            x = 282,
                            y = 361
                        }
                    },
                    ["23"] = {
                        {
                            x = 85,
                            y = 361
                        }
                    },
                    ["25"] = {
                        {
                            x = 109,
                            y = 433
                        }
                    },
                    ["26"] = {
                        {
                            x = 367,
                            y = 515
                        }
                    },
                    ["29"] = {
                        {
                            x = 770,
                            y = 515
                        }
                    },
                }
            },
            metal_holder = {
                price = 150,
                first_cooldown = 0,
                cooldown = 15,
                upgrade_price_multiplier = 0.75,
                default_max_range = 200, -- solo para detectar enemigos
                steal_gold = {
                    cooldown = 15, --TP cd
                    first_cooldown = 2,
                    duration = 9,
                    gold_steal_group_max_size = 3,
                    delay_between_steals = 2,
                    steal_radius = 50,

                    gold_steal_amount = 3,
                    gold_steal_amount_boss = 50,

                    -- visuals
                    wander_interval = 1.5, -- time between appearings when not actively stealing from enemies
                    chase_speed = 40, -- speed when chasing enemies to steal from
                }
            }
        }
    },
    stage07_temple = {
        activation_wave = 10,
    },
    stage08_elf_rescue = {
        spawn_cooldown = 90,
        elf = {
            range = 202,
            cooldown_min = 1.2,
            cooldown_max = 1.6,
            damage_min = 36,
            damage_max = 54,
            damage_type = DAMAGE_PHYSICAL,
            stun_duration = 24, -- in frames
        },
    },
    stage09_spawn_nightmares = {
        wave_config = {
            {
                -- wave1
                {},
                -- wave2
                {},
                -- wave3
                {
                    {time_start = 10, duration = 28}
                },
                -- wave4
                {
                    {time_start = 10, duration = 28},
                },
                -- wave5
                {},
                -- wave6
                {},
                -- wave7
                {
                    {time_start = 10, duration = 30},
                },
                -- wave8
                {},
                -- wave9
                {
                    {time_start = 10, duration = 30},
                },
                -- wave10
                {},
                -- wave11
                {
                    {time_start = 10, duration = 52},
                },
                -- wave12
                {
                    {time_start = 10, duration = 40},
                },
                -- wave13
                {},
                -- wave14
                {
                    {time_start = 12, duration = 40},
                },
                -- wave15
                {
                    {time_start = 10, duration = 70},
                },
            },
            -- heroic
            {
                -- wave1
                {},
                -- wave2
                {},
                -- wave3
                {
                },
                -- wave4
                {
                    {time_start = 20, duration = 70},
                },
                -- wave5
                {
                },
                -- wave6
                {
                    {time_start = 21, duration = 107},
                },
            },
            -- iron
            {
                -- wave1
                {
                    {time_start = 74, duration = 110},
                    {time_start = 310, duration = 330},
                },
            }
        },
        path_portal_off_delay = 10, -- in seconds
    },
    stage10_obelisk = {
        change_mode_every = 4,
        start_delay = {20, 0, 30}, -- Por modos (C, H, I). Solo se usa en la primera wave
        mode_first_delay = 1, -- Tiempo entre cambio y trigger.
        min_enemies = 2,
        per_wave_config_campaign = {
            { -- wave 1
                delay = 30,
                mode = 'heal',
                duration = 12,
            },
            { -- wave 2
                delay = 12,
                mode = 'teleport',
                duration = 12,
            },
            { -- wave 3
                delay = 30,
                mode = 'heal',
                duration = 12,
            },
            { -- wave 4
                delay = 8,
                mode = 'teleport',
                duration = 12,
            },
            { -- wave 5 (sacrifice)
                delay = 1,
                mode = 'sacrifice',
            },
            { -- wave 6
                delay = 20,
                mode = 'heal',
                duration = 12,
            },
            { -- wave 7
                delay = 12,
                mode = 'teleport',
                duration = 12,
            },
            { -- wave 8
               delay = 20,
                mode = 'heal',
                duration = 12,
            },
            { -- wave 9
                delay = 12,
                mode = 'teleport',
                duration = 12,
            },
            { -- wave 10 (sacrifice)
                delay = 1,
                mode = 'sacrifice',
            },
            { -- wave 11
                delay = 25,
                mode = 'heal',
                duration = 12,
            },
            { -- wave 12
                delay = 12,
                mode = 'teleport',
                duration = 12,
            },
            { -- wave 13
                delay = 20,
                mode = 'heal',
                duration = 12,
            },
            { -- wave 14
                delay = 12,
                mode = 'teleport',
                duration = 12,
            },
            { -- wave 15 (sacrifice)
                delay = 10,
                mode = 'sacrifice',
            },
        },
        per_wave_config_heroic = {
            { -- wave 1
                delay = 30,
                mode = 'heal',
                duration = 10,
            },
            { -- wave 2
                delay = 48,
                mode = 'heal',
                duration = 10,
            },
            { -- wave 3
                delay = 20,
                mode = 'heal',
                duration = 10,
            },
            { -- wave 4
                delay = 45,
                mode = 'heal',
                duration = 10,
            },
            { -- wave 5
                delay = 30,
                mode = 'heal',
                duration = 10,
            },
            { -- wave 6
                delay = 120,
                mode = 'heal',
                duration = 10,
            },
        },
        iron_config = {
            golem_activate_delay = {50, 190, 270, 350, 370},
        },
        stun = {
            mode_duration = 90,
            stun_duration = 3,
            cooldown = 26,
        },
        heal = {
            mode_duration = 55, -- Esto no se usa
            heal_duration = 10,
            heal_every = 0.25,
            heal_max = 3,
            heal_min = 1,
            cooldown = 50,
        },
        teleport = {
            mode_duration = 75, -- Esto no se usa
            nodes_advance = 25,
            max_targets = 4,
            cooldown = 5,
            aura_radius = 200/2,
            nodes_limit = 30,
            nodes_from_selectable = 30,
            nodes_to_goal_selectable = 80,
        },
        sacrifice = {
            waves = {5, 10, 15},
            inactive_time = 20,
        }
    },
    stage10_ymca = {
        soldier = {
            armor = 0,
            hp = 100,
            max_speed = 30 * 3,
            melee_attack = {
                cooldown = 1,
                damage_max = 12,
                damage_min = 6,
            }
        }
    },
    stage11_cult_leader = {
        deck_total_cards = 2,
        deck_chain_ability = 1,
        ability_cooldown = 90,
        ability_cooldown_bossfight = 30,
        ability_first_delay = 30,
        stun_time = 15,
        illusion = {
            spawn_charge_time = 5,
            hp_max = 150,
            magic_armor = 0,
            armor = 0,
            max_speed = 20,
            nodes_limit = 20,
            melee_attack = {
                cooldown = 1,
                damage_max = 5,
                damage_min = 5,
            },
            ranged_attack = {
                cooldown = 1.5,
                max_range = 200/2,
                min_range = 10,
                damage_min = 16,
                damage_max = 24,
                damage_type = DAMAGE_MAGICAL,
            },
            chain = {
                cooldown = 1,
                max_range = 320/2,
                duration = 12,
            },
            shield = {
                radius = 160/2,
                duration = 12,
            }
        },
        config_per_wave = {
            -- wave 1
            {illusions = 1},
            -- wave 2
            {illusions = 1},
            -- wave 3
            {illusions = 1},
            -- wave 4
            {illusions = 1},
            -- wave 5
            {illusions = 1},
            -- wave 6
            {illusions = 1},
            -- wave 7
            {illusions = 1},
            -- wave 8
            {illusions = 1},
            -- wave 9
            {illusions = 1},
            -- wave 10
            {illusions = 2},
            -- wave 11
            {illusions = 2},
            -- wave 12
            {illusions = 2},
            -- wave 13
            {illusions = 2},
            -- wave 14
            {illusions = 2},
            -- wave 15
            {illusions = 2},
            -- wave Boss Fight
            {illusions = 3},
            -- bossfight
            {illusions = 3},
        },
    },
    stage11_portal = {
        waves_campaign = {3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15},
        waves_heroic = {2, 3, 4, 5, 6},
        waves_iron = {1}
    },
    stage11_veznan = {
        cooldown = 12,
        skill_soldiers = {
            soldier = {
                armor = 0,
                hp_max = 200,
                max_speed = 30,
                regen_health = 8,
                nodes_from_start = 20,
                melee_attack = {
                    damage_min = 24,
                    damage_max = 40,
                    range = 100/2,
                },
            },
        },
        skill_cage = {
            duration = 5,
        },
    },
    stage14_amalgam = {
        sacrifices_to_show_1 = 1,
        sacrifices_to_show_2 = 2,
        sacrifices_to_spawn = 5,
    },
    stage15_denas = {
        speed = 60,
        cooldown = 30,
        attack_cooldown = 2,
        damage_min = 30,
        damage_max = 49,
        damage_type = DAMAGE_TRUE,
        hp_max = 600,
        regen_health = 15,
        armor = 0.5,
        magic_armor = 0.5,
        range = 72,
        duration = 20,
        --special attack
        damage_special_min = 400,
        damage_special_max = 500,
        attack_cooldown_special = 8,
        spawn_stun_duration = 1,
        spawn_stun_radius = 100/2,
    },
    stage15_cult_leader_tower = {
        aura_radius = 40,
        aura_time_before_stun = 5,
        aura_duration = 4 + 4 - 0.5, -- tentacle duration + aura_time_before_stun - tentacle animation
        config_per_wave = {
            -- wave 1
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=1},
            -- wave 2
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=1},
            -- wave 3
            {tentacle_cd = 40, tentacle_duration=6, targets_amount=1},
            -- wave 4
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=1},
            -- wave 5
            {tentacle_cd = 25, tentacle_duration=6, targets_amount=1},
            -- wave 6
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=1},
            -- wave 7
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=1},
            -- wave 8
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=1},
            -- wave 9
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=1},
            -- wave 10
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
            -- wave 11
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
            -- wave 12
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
            -- wave 13
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
            -- wave 14
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
            -- wave 15
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
            -- wave Boss Fight
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
            -- bossfight
            {tentacle_cd = 30, tentacle_duration=6, targets_amount=2},
        },
    },
    stage16_overseer = {
        hp = 30000,
        phase_per_hp_threshold =      {100, 090, 075, 070, 050, 010},
        phase_per_time =                   {030, 075, 050, 120, 150}, -- time to next phase, in seconds
        change_tower_cooldown =       {nil, 060, 060, 045, 030, nil},
        change_tower_amount =         {nil, 001, 001, 001, 001, nil},
        disable_tower_cooldown =      {nil, nil, nil, nil, nil, nil},
        disable_tower_recover_price = 100,
        destroy_tower_cooldown = 10,
        first_time_cooldown = 5,
        destroy_holder = {
            cooldown =                {nil, nil, nil, nil, nil, 15},
        },
        tentacle_spawns_per_phase = {0, 0, 3, 3, 4, 4}, -- max 4
        tentacle_left = {
            cooldown =              {nil, nil, nil, 30, 20, 10},
        },
        tentacle_right = {
            cooldown =              {nil, nil, 30, 30, 20, 12},
        },
        tentacle_bullet_explosion_damage = {
            range = 70,
            damage_type = DAMAGE_PHYSICAL,
            damage_min = 120,
            damage_max = 180,
        },
        glare1 = {
            -- Left glare (delay, duration)
            {-1, 0}, {-1, 0}, {-1, 0}, {6, 30}, {6, 20}, {60, 30},
        },
        glare2 = {
            -- Right glare (delay, duration)
            {-1, 0}, {8, 25}, {6, 30}, {-1, 0}, {-1, 0}, {6, 30},
        },
    },
    stage18_eridan = {
        ranged_attack = {
            range = 380,
            cooldown = 4,
            damage_min = 18,
            damage_max = 30,
            damage_type = DAMAGE_PHYSICAL,
        },
        instakill = {
            range = 250,
            hp_threshold = 700,
            cooldown = 18,
            damage_type = DAMAGE_INSTAKILL,
        }
    },
    stage19_mausoleum = {
        wave_config = {
            {
                -- wave1
                {},
                -- wave2
                {},
                -- wave3
                {
                },
                -- wave4
                {
                    {time_start = 2, duration = 60},
                },
                -- wave5
                {},
                -- wave6
                {
                    {time_start = 2, duration = 42},
                },
                -- wave7
                {
                },
                -- wave8
                {
                    {time_start = 2, duration = 50},
                },
                -- wave9
                {
                    {time_start = 2, duration = 55},
                },
                -- wave10
                {},
                -- wave11
                {
                    {time_start = 2, duration = 30},
                },
                -- wave12
                {
                },
                -- wave13
                {},
                -- wave14
                {
                    {time_start = 2, duration = 68},
                },
                -- wave15
                {
                    {time_start = 2, duration = 75},
                },
            },
            -- heroic
            {
                -- wave1
                {},
                -- wave2
                {
                    {time_start = 2, duration = 55},
                },
                -- wave3
                {
                    {time_start = 2, duration = 27},
                },
                -- wave4
                {
                    {time_start = 2, duration = 56},
                },
                -- wave5
                {
                },
                -- wave6
                {
                    {time_start = 2, duration = 75},
                },
            },
            -- iron
            {
                -- wave1
                {
                    {time_start = 2, duration = 325},
                },
            }
        },
        path_portal_off_delay = 10, -- in seconds
    },
    stage20_arborean_house = {
        hp_max = 280,
        armor = 0.3,
        magic_armor = 0,
    },
    stage21_falling_rocks = {
        damage = 2000,
        damage_type = DAMAGE_PHYSICAL,
        damage_radius = 60,
    },
    stage22_remolino = {
        --                           IN SECONDS
        --  [STAGE] = { {from, to}, { from, to }, { from, to } }
        [1] = { -- CAMPAIGN
            [3] = { {28, 41}},
            [4] = { {35, 48}, {67, 80}},
            [6] = { {12, 25}, {32, 45}},
            [7] = { {25, 95}},
            [9] = { {15, 24}, {75, 84}},
            [10] = { {5, 52}},
            [12] = { {12, 21}, {42, 51}},
            [13] = { {5, 48}},
            [14] = { {5, 20},  {70, 85}},
            [15] = { {8, 18}, {48, 58}},
            ['BOSS'] = { {31, 480} },
        },
        [2] = { -- HEROIC
            [2] = { {19.5, 27.5}, {43, 51.5} },
            [3] = { {0.2, 4} },
            [4] = { {27, 90} },
            [5] = { {14, 24} },
            [6] = { {0, 5}, {25, 63} },
        },
        [3] = { -- IRON
            [1] = { {115, 319}}
        }
    },
    stage22_tower_destroyed = {
        repair_cost = 220
    },
    stage23_roboboots = {
        -- can set to only open one leg
        wave_config = {
            {
                -- {legs = {2}, {{2, 10}, {2, 10}}},
                -- LEGS   {START, DURATION}, {START, DURATION}, {START, DURATION} ...
                -- if duration of the last entry is nil, it will remain open until
                -- the next duration entry (will ignore its next start timestamp)

                -- wave1
                {},
                -- wave2
                {
                    {leg=2, timings={ {0, nil} }},
                },
                -- wave3
                {
                    {leg=2, timings={ {nil, 1} }},
                },
                -- wave4
                {},
                -- wave5
                {
                    {leg=1, timings={ {10, nil} }},
                },
                -- wave6
                {
                    {leg=1, timings={ {nil, 7} }},
                    {leg=2, timings={ {17, nil} }},
                },
                -- wave7
                {
                    {leg=2, timings={ {nil, 8} }},
                },
                -- wave8
                {},
                -- wave9
                {
                    {leg=1, timings={ {1, nil} }},
                },
                -- wave10
                {
                    {leg=1, timings={ {nil, 8} }},
                    {leg=2, timings={ {1, nil} }},
                },
                -- wave11
                {
                    {leg=1, timings={ {5, 25} }},
                    {leg=2, timings={ {nil, 10} }}
                },
                -- wave12
                {
                },
                -- wave13
                {
                    {leg=2, timings={ {1, nil} }},
                },
                -- wave14
                {
                    {leg=2, timings={ {nil, 5} }},
                },
                -- wave15
                {
                    {leg=1, timings={ {10, 76} }},
                    {leg=2, timings={ {1, 72} }},
                },
            },
            -- heroic
            {
                -- wave1
                {},
                -- wave2
                {},
                -- wave3
                {
                    {leg=1, timings={ {1, nil} }},
                },
                -- wave4
                {
                    {leg=1, timings={ {nil, 6} }},
                    {leg=2, timings={ {1, nil} }},
                },
                -- wave5
                {
                    {leg=2, timings={ {nil, 6} }},
                },
                -- wave6
                {
                    {leg=1, timings={ {20, 46} }},
                    {leg=2, timings={ {13, 72} }},
                },
            },
            -- iron
            {
                -- wave1
                {
                    {leg=1, timings={ {2, 45}, {114, 163},{200,230},{295,340},{345,370}}},
                    {leg=2, timings={ {170, 205}, {280, 345} }},
                },
            }
        },
    },
    stage24_factory = {
        wave_config = {
            {
                -- wave1
                {},
                -- wave2
                {},
                -- wave3
                {},
                -- wave4
                {
                },
                -- wave5
                {},
                -- wave6
                {},
                -- wave7
                {
                    {time_start = 10, duration = 40},
                },
                -- wave8
                {
                },
                -- wave9
                {
                    {time_start = 10, duration = 40},
                },
                -- wave10
                {},
                -- wave11
                {
                    {time_start = 8, duration = 40},
                },
                -- wave12
                {},
                -- wave13
                {
                    {time_start = 12, duration = 40},
                },
                -- wave14
                {},
                -- wave15
                {
                    {time_start = 15, duration = 40},
                },
            },
            -- heroic
            {
                -- wave1
                {},
                -- wave2
                {
                },
                -- wave3
                {
                },
                -- wave4
                {
                },
                -- wave5
                {},
                -- wave6
                {
                },
            },
            -- iron
            {
                -- wave1
                {
                },
            }
        },
    },
    stage24_upgrade_station = {
        wave_config = {
            {
                -- wave1
                {},
                -- wave2
                {
                    
                },
                -- wave3
                {},
                -- wave4
                {},
                -- wave5
                {
                    {time_start = 1, duration = 60},
                },
                -- wave6
                {},
                -- wave7
                {
                },
                -- wave8
                {
                    {time_start = 10, duration = 60},
                },
                -- wave9
                {
                },
                -- wave10
                {},
                -- wave11
                {
                    
                },
                -- wave12
                {
                    {time_start = 1, duration = 50},
                },
                -- wave13
                {
                    
                },
                -- wave14
                {
                    {time_start = 1, duration = 45},
                },
                -- wave15
                {
                },
            },
            -- heroic
            {
                -- wave1
                {},
                -- wave2
                {
                    {time_start = 2, duration = 55},
                },
                -- wave3
                {
                },
                -- wave4
                {
                },
                -- wave5
                {
                    {time_start = 2, duration = 56},
                },
                -- wave6
                {
                },
            },
            -- iron
            {
                -- wave1
                {
                    {time_start = 2, duration = 560},
                },
            }
        },
    },
    stage25_torso = {
        fist = {
            radius = 280/2,
        },
        missile = {
            repair_cost = 50,
            max_duration = 30
        },
        wave_config = {
            -- campaign
            {
                -- wave 1
                {
                },
                -- wave 2
                {
                },
                -- wave 3
                {
                },
                -- wave 4
                {
                },
                -- wave 5
                {
                },
                -- wave 6
                {
                },
                -- wave 7
                {
                },
                -- wave 8
                {
                    {time_start = 8, action = 'open'},
                    {time_start = 13, action = 'fist'},
                    {time_start = 23, action = 'close'},
                },
                -- wave 9
                {
                    {time_start = 13, action = 'open'},
                    {time_start = 18, action = 'missile'},
                    {time_start = 28, action = 'close'},
                },
                -- wave 10
                {
                },
                -- wave 11
                {
                    {time_start = 2, action = 'open'},
                    {time_start = 8, action = 'fist'},
                    {time_start = 16, action = 'fist'},
                },
                -- wave 12
                {
                    {time_start = 12, action = 'missile'},
                    {time_start = 22, action = 'missile'},
                },
                -- wave 13
                {
                    {time_start = 10, action = 'fist'},
                    {time_start = 26, action = 'fist'},
                },
                -- wave 14
                {
                    {time_start = 2, action = 'missile'},
                    {time_start = 9, action = 'missile'},
                    {time_start = 17, action = 'missile'},
                },
                -- wave 15
                {
                    --{time_start = 6, action = 'open'},
                    {time_start = 11, action = 'missile'},
                    {time_start = 23, action = 'missile'},
                    {time_start = 33, action = 'missile'},
                    {time_start = 47, action = 'missile'},
                    {time_start = 57, action = 'missile'},
                    {time_start = 67, action = 'missile'},
                    {time_start = 77, action = 'missile'},
                    {time_start = 87, action = 'missile'},
                    {time_start = 97, action = 'missile'},
                },
            },
            -- heroic
            {
                -- wave 1
                {
                    {time_start = 2, action = 'open'},
                    {time_start = 17, action = 'fist'},
                    {time_start = 27, action = 'fist'},
                },
                -- wave 2
                {
                    {time_start = 12, action = 'missile'},
                    {time_start = 22, action = 'missile'},
                    {time_start = 32, action = 'missile'},
                },
                -- wave 3
                {
                    {time_start = 8, action = 'missile'},
                    {time_start = 26, action = 'missile'},
                    {time_start = 38, action = 'missile'},
                },
                -- wave 4
                {
                    {time_start = 12, action = 'fist'},
                    {time_start = 28, action = 'fist'},
                },
                -- wave 5
                {
                    {time_start = 17, action = 'fist'},
                    {time_start = 26, action = 'fist'},
                },
                -- wave 6
                {
                    {time_start = 17, action = 'fist'},
                    {time_start = 24, action = 'missile'},
                    {time_start = 34, action = 'missile'},
                    {time_start = 46, action = 'fist'},
                    {time_start = 59, action = 'missile'},
                    {time_start = 72, action = 'missile'},
                    {time_start = 83, action = 'missile'},
                    {time_start = 94, action = 'missile'},
                    {time_start = 106, action = 'missile'},
                    {time_start = 120, action = 'missile'},
                },
            },
            -- iron
            {
                -- wave 1
                {
                    {time_start = 12, action = 'open'},
                    {time_start = 24, action = 'missile'},
                    {time_start = 48, action = 'missile'},
                    {time_start = 66, action = 'fist'},
                    {time_start = 84, action = 'missile'},
                    {time_start = 104, action = 'missile'},
                    --{time_start = 120, action = 'missile'},
                    {time_start = 132, action = 'missile'},
                    {time_start = 145, action = 'missile'},
                    {time_start = 160, action = 'fist'},
                    {time_start = 180, action = 'fist'},
                    {time_start = 230, action = 'missile'},
                    --{time_start = 245, action = 'missile'},
                    {time_start = 260, action = 'missile'},
                    {time_start = 272, action = 'missile'},
                    --{time_start = 283, action = 'missile'},
                    {time_start = 300, action = 'missile'},
                    {time_start = 312, action = 'fist'},
                    {time_start = 325, action = 'missile'},
                    --{time_start = 336, action = 'missile'},
                    {time_start = 347, action = 'missile'},
                    {time_start = 360, action = 'fist'},
                    {time_start = 372, action = 'missile'},
                    {time_start = 383, action = 'missile'},
                    --{time_start = 394, action = 'missile'},
                    {time_start = 405, action = 'missile'},
                    {time_start = 416, action = 'missile'},
                    {time_start = 430, action = 'fist'},
                    {time_start = 442, action = 'missile'},
                    {time_start = 455, action = 'missile'},
                    {time_start = 472, action = 'missile'},
                    {time_start = 483, action = 'missile'},
                    --{time_start = 494, action = 'missile'},
                    {time_start = 505, action = 'missile'},
                    {time_start = 516, action = 'missile'},
                    {time_start = 527, action = 'missile'},
                },
            }
        },
    },
    stage26_spawners = {
        wave_config = {
            -- campaign
            {
                -- wave 1
                {
                },
                -- wave 2
                {
                },
                -- wave 3
                {
                    {time_start = 8, spawner = 'fist', action = 'open', count = 2},
                    {time_start = 17, spawner = 'fist', action = 'close'},
                    {time_start = 18, spawner = 'fist', action = 'open', count = 2}, 
                    {time_start = 27, spawner = 'fist', action = 'close'},
                    
                },
                -- wave 4
                {
                },
                -- wave 5
                {
                    {time_start = 1, spawner = 'clone_left', action = 'open'},
                    {time_start = 6, spawner = 'fist', action = 'open', count = 2},
                    {time_start = 11, spawner = 'clone_left', action = 'close'},
                    {time_start = 16, spawner = 'fist', action = 'close'},
                    {time_start = 18, spawner = 'clone_left', action = 'open'},
                    {time_start = 38, spawner = 'clone_left', action = 'close'},
                },
                -- wave 6
                {
                    {time_start = 1, spawner = 'hulk', action = 'activate'},
                    {time_start = 4, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 25, spawner = 'fist', action = 'close'},
                    
                },
                -- wave 7
                {
                    {time_start = 1, spawner = 'clone_right', action = 'open'},
                    {time_start = 9, spawner = 'clone_right', action = 'close'},
                    {time_start = 10, spawner = 'clone_right', action = 'open'},
                    {time_start = 19, spawner = 'clone_right', action = 'close'},
                    {time_start = 23, spawner = 'clone_right', action = 'open'},
                    {time_start = 32, spawner = 'clone_right', action = 'close'},
                },
                -- wave 8
                {
                    {time_start = 1, spawner = 'clone_left', action = 'open'},
                    {time_start = 4, spawner = 'fist', action = 'open', count = 2},
                    {time_start = 13, spawner = 'fist', action = 'close'},
                    {time_start = 11, spawner = 'clone_left', action = 'close'},
                    {time_start = 12, spawner = 'clone_left', action = 'open'},
                    {time_start = 14, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 24, spawner = 'clone_left', action = 'close'},
                    {time_start = 26, spawner = 'clone_left', action = 'open'},
                    {time_start = 30, spawner = 'fist', action = 'close'},
                    {time_start = 36, spawner = 'clone_left', action = 'close'},
                },
                -- wave 9
                {
                    {time_start = 1, spawner = 'hulk', action = 'activate'},
                },
                -- wave 10
                {
                    {time_start = 1, spawner = 'hulk', action = 'activate'},
                },
                -- wave 11
                {
                    {time_start = 2, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 9, spawner = 'clone_right', action = 'open'},
                    {time_start = 18, spawner = 'fist', action = 'close'},
                    {time_start = 19, spawner = 'clone_right', action = 'close'},
                    {time_start = 31, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 38, spawner = 'clone_right', action = 'open'},
                    {time_start = 47, spawner = 'fist', action = 'close'},
                    {time_start = 48, spawner = 'clone_right', action = 'close'},
                },
                -- wave 12
                {
                    {time_start = 1, spawner = 'fist', action = 'open', count = 2},
                    {time_start = 4, spawner = 'clone_left', action = 'open'},
                    {time_start = 10, spawner = 'fist', action = 'close'},
                    {time_start = 15, spawner = 'clone_left', action = 'close'},
                    {time_start = 16, spawner = 'fist', action = 'open', count = 2},
                    {time_start = 20, spawner = 'clone_left', action = 'open'},
                    {time_start = 26, spawner = 'fist', action = 'close'},
                    {time_start = 31, spawner = 'clone_left', action = 'close'},
                },
                -- wave 13
                {
                    {time_start = 1, spawner = 'hulk', action = 'activate'},
                    {time_start = 3, spawner = 'clone_right', action = 'open'},
                    {time_start = 14, spawner = 'clone_right', action = 'close'},
                    {time_start = 20, spawner = 'hulk', action = 'activate'},
                    {time_start = 26, spawner = 'clone_right', action = 'open'},
                    {time_start = 38, spawner = 'clone_right', action = 'close'},
                },
                -- wave 14
                {
                    {time_start = 1, spawner = 'clone_left', action = 'open'},
                    {time_start = 3, spawner = 'clone_right', action = 'open'},
                    {time_start = 5, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 13, spawner = 'clone_left', action = 'close'},
                    {time_start = 14, spawner = 'clone_right', action = 'close'},
                    {time_start = 15, spawner = 'clone_left', action = 'open'},
                    {time_start = 17, spawner = 'clone_right', action = 'open'},


                    {time_start = 20.5, spawner = 'fist', action = 'close'},
                    {time_start = 20.5, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 28, spawner = 'clone_left', action = 'close'},
                    {time_start = 30, spawner = 'clone_right', action = 'close'},
                    {time_start = 37, spawner = 'fist', action = 'close'},
                    {time_start = 38, spawner = 'clone_left', action = 'open'},
                    {time_start = 40, spawner = 'clone_right', action = 'open'},
                    {time_start = 48, spawner = 'clone_left', action = 'close'},
                    {time_start = 50, spawner = 'clone_right', action = 'close'},
                    
                },
                -- wave 15
                {
                    {time_start = 1, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 3, spawner = 'hulk', action = 'activate'},
                    {time_start = 16, spawner = 'fist', action = 'close'},
                    {time_start = 23, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 27, spawner = 'hulk', action = 'activate'},
                    {time_start = 38, spawner = 'fist', action = 'close'},
                    {time_start = 48, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 52, spawner = 'hulk', action = 'activate'},
                    {time_start = 63, spawner = 'fist', action = 'close'},
                },
            },
            -- heroic
            {
                -- wave 1
                {
                },
                -- wave 2
                {
                    {time_start = 0, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 16, spawner = 'fist', action = 'close'},
                    {time_start = 34, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 50, spawner = 'fist', action = 'close'},
                },
                -- wave 3
                {
                    {time_start = 0, spawner = 'hulk', action = 'activate'},
                    {time_start = 4, spawner = 'clone_right', action = 'open'},
                    {time_start = 15, spawner = 'clone_right', action = 'close'},
                    {time_start = 33, spawner = 'clone_right', action = 'open'},
                    {time_start = 43, spawner = 'clone_right', action = 'close'},
                    {time_start = 53, spawner = 'clone_right', action = 'open'},
                    {time_start = 63, spawner = 'clone_right', action = 'close'},
                },
                -- wave 4
                {
                    {time_start = 0, spawner = 'hulk', action = 'activate'},
                },
                -- wave 5
                {
                    {time_start = 0, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 7, spawner = 'clone_left', action = 'open'},
                    {time_start = 16, spawner = 'fist', action = 'close'},
                    {time_start = 21, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 23, spawner = 'clone_left', action = 'close'},
                    {time_start = 28, spawner = 'clone_left', action = 'open'},
                    {time_start = 37, spawner = 'fist', action = 'close'},
                    {time_start = 42, spawner = 'clone_left', action = 'close'},
                },
                -- wave 6
                {
                    {time_start = 0, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 10, spawner = 'hulk', action = 'activate'},
                    {time_start = 16, spawner = 'fist', action = 'close'},
                    {time_start = 34, spawner = 'clone_left', action = 'open'},
                    {time_start = 56, spawner = 'hulk', action = 'activate'},
                    {time_start = 62, spawner = 'clone_left', action = 'close'},
                    {time_start = 74, spawner = 'hulk', action = 'activate'},
                    {time_start = 76, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 92, spawner = 'fist', action = 'close'},
                },
            },
            -- iron
            {
                -- wave 1
                {
                    --tanda 1
                    {time_start = 1, spawner = 'clone_left', action = 'open'},
                    {time_start = 9, spawner = 'clone_left', action = 'close'},
                    {time_start = 15, spawner = 'clone_left', action = 'open'},
                    {time_start = 24, spawner = 'clone_left', action = 'close'},
                    {time_start = 54, spawner = 'clone_left', action = 'open'},
                    {time_start = 64, spawner = 'clone_left', action = 'close'},

                    --tanda 2
                    {time_start = 80, spawner = 'fist', action = 'open', count = 6},
                    {time_start = 106, spawner = 'fist', action = 'close'},
                    {time_start = 109, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 125, spawner = 'fist', action = 'close'},

                    --tanda 3
                    {time_start = 142, spawner = 'clone_right', action = 'open'},
                    {time_start = 145, spawner = 'fist', action = 'open', count = 3},
                    {time_start = 151, spawner = 'clone_right', action = 'close'},
                    {time_start = 154, spawner = 'clone_right', action = 'open'},
                    {time_start = 158, spawner = 'fist', action = 'close'},
                    {time_start = 159, spawner = 'fist', action = 'open', count = 7},
                    {time_start = 163, spawner = 'clone_right', action = 'close'},
                    {time_start = 173, spawner = 'clone_right', action = 'open'},
                    {time_start = 188, spawner = 'fist', action = 'close'},
                    {time_start = 195, spawner = 'clone_right', action = 'close'},

                    --tanda 4 no hay spawners

                    --tanda 5
                    {time_start = 260, spawner = 'hulk', action = 'activate'},
                    {time_start = 264, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 279, spawner = 'fist', action = 'close'},
                    {time_start = 294, spawner = 'clone_right', action = 'open'},
                    {time_start = 295, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 305, spawner = 'clone_right', action = 'close'},
                    {time_start = 309, spawner = 'fist', action = 'close'},
                    {time_start = 325, spawner = 'clone_right', action = 'open'},
                    {time_start = 335, spawner = 'clone_right', action = 'close'},

                    --tanda 6
                    {time_start = 342, spawner = 'clone_left', action = 'open'},
                    {time_start = 352, spawner = 'clone_left', action = 'close'},
                    {time_start = 370, spawner = 'clone_left', action = 'open'},
                    {time_start = 380, spawner = 'clone_left', action = 'close'},

                    --tanda 7
                    {time_start = 397, spawner = 'clone_right', action = 'open'},
                    {time_start = 400, spawner = 'clone_left', action = 'open'},
                    --tanda 8
                    {time_start = 419, spawner = 'hulk', action = 'activate'},
                    {time_start = 430, spawner = 'clone_right', action = 'close'},
                    {time_start = 431, spawner = 'fist', action = 'open', count = 4},
                    {time_start = 432, spawner = 'clone_left', action = 'close'},
                    {time_start = 445, spawner = 'fist', action = 'close'},
                    {time_start = 464, spawner = 'hulk', action = 'activate'},
                    {time_start = 469, spawner = 'fist', action = 'open', count = 8},
                    {time_start = 506, spawner = 'fist', action = 'close'},


                },
            }
        },
    },
    stage27_head = {
        charge_time = 3,
        attack_duration = 6,
        ray_stun_duration = 15,
        taps_to_cancel = 20,
        scrap_attack = {
            damage_max = 72,
            damage_min = 48,
            damage_type = DAMAGE_EXPLOSION,
            damage_radius = 100/2,
        },
        tower_stun_repair_cost = {50, 75, 100, 125, 150, 175, 200, 225, 250, 275},
        towers_to_stun = 13
    },
    stage29_holder_block = {
        -- 1 campaign
        -- 2 heroic
        -- 3 iron
        waves = {
            [1] = {05, 06, 07, 09, 10, 11, 12, 13, 14, 15},
            [2] = {02, 03, 04, 05, 06},
            [3] = {01}
        },
        first_cooldown = {
            [1] = {05, 40, 05, 45, 01, 22, 01, 01, 01, 01},
            [2] = {01, 35, 05, 25, 40},
            [3] = {30}
        },
        cooldown = {
            [1] = {35, 20, 00, 00, 30, 30, 40, 30, 30, 25},
            [2] = {00, 00, 42, 00, 20},
            [3] = {50}
        },
        max_casts = {
            [1] = {02, 02, 01, 01, 03, 02, 02, 03, 03, 04},
            [2] = {01, 01, 02, 01, 02},
            [3] = {50}
        },
        blocked_holders = {
            price = {
                [1] = 65,
                [2] = 65,
                [3] = 30
            }
        },
        game_start_blocked_holders = { -- por algun motivo los id de los holders son strings
            [1] = {},
            [2] = {},
            [3] = {'1', '2', '3','4', '5', '6','7', '8', '9','10', '11', '12','13', '14', '15'},
        },
        time_to_down = 3,
        time_to_up = 2,
        time_netting = 5,
        taps_to_cancel = 3
    },
    stage30_door = {
        --                           IN SECONDS
        --  [STAGE] = { {from, to}, { from, to }, { from, to } }
        [1] = { -- CAMPAIGN
            [5] = { {32, 42}, {58, 68}},
            [9] = { {21, 31}, {48, 58}},
            [11] = { {10, 25}, {43, 58}},
            [13] = { {1, 10},  {18, 28}, {41, 55}},
            [15] = { {1, 10}, {18, 28}, {52, 62}},
            --['BOSS'] = { {31, 480} },
        },
        [2] = { -- HEROIC
            [1] = { {44, 52} },
            [2] = { {57, 67} },
            [3] = { {0.2, 10}, {44, 60} },
            [4] = { {46, 60} },
            [5] = { {0.5, 19} },
            [6] = { {24, 34}, {70, 80} },
        },
        [3] = { -- IRON
            [1] = { {155, 180}, {220, 250} }
        }
    },
    stage31_water_mechanic = {
        duration = 4,
        cooldown = 50, -- works in intervals of 5 rounded up
        path = {1, 4},
        nodes = {{46, 138}, {50, 130}},
        damage_type = DAMAGE_PHYSICAL,
        damage_min = 280,
        damage_max = 320,
        unlock_wave = 4,
        warn_duration = 5,
        first_warn_minimum_targets = 3
    },
    stage32_lightning_strike = {
        warning_duration = 0.75,
        force_target_soldier_chance = 0.2,
        chain_strikes_chance = 0,
        max_chains = 2,
        areas_configs = {
            ['CAMPAIGN'] = {
                -- Center Bottom Area
                ['1'] = {
                    -- Wave
                    [05] = {{first_cd=1, max_cd=6, min_cd = 4.5, max_casts=10}}, 
                    [06] = {{first_cd=3, max_cd=5.5, min_cd = 4, max_casts=15}},
                    [08] = {{first_cd=3, max_cd=5, min_cd = 4, max_casts=4}},
                    [10] = {{first_cd=5, max_cd=5, min_cd = 4, max_casts=15}}, 
                    [11] = {{first_cd=15, max_cd=7, min_cd = 5, max_casts=10}},
                    [13] = {{first_cd=5, max_cd=3.5, min_cd = 3, max_casts=40}},
                    [15] = {{first_cd=1, max_cd=4, min_cd = 3.75, max_casts=75}},
                },
                -- Center Bottom Area FOR SPAWNS ONLY
                ['10'] = {
                    -- Wave
                    -- [05] = {{first_cd=5, max_cd=9, min_cd = 6, max_casts=5, spawn_unit='enemy_water_spirit_spawnless'}}, 
                    -- [15] = {{first_cd=85, max_cd=4, min_cd = 3, max_casts=2, spawn_unit='enemy_storm_elemental'}},
                },
                -- Left Area
                ['2'] = {
                    -- Wave
                    -- [05] = {{first_cd=0.1, max_cd=0.85, min_cd = 0.35, max_casts=5}},
                    [06] = {{first_cd=10, max_cd=3, min_cd = 2, max_casts=6, spawn_unit='enemy_water_spirit_spawnless'}, {first_cd=42, max_cd=3, min_cd = 2, max_casts=6, spawn_unit='enemy_water_spirit_spawnless'}},
                    [08] = {{first_cd=1, max_cd=4, min_cd = 2, max_casts=8}},
                    [09] = {{first_cd=2, max_cd=6, min_cd = 5, max_casts=11}},
                    [11] = {{first_cd=48.5, max_cd=3, min_cd = 2.5, max_casts=6, spawn_unit='enemy_water_spirit_spawnless'}}, 
                    [13] = {{first_cd=2, max_cd=1.5, min_cd = 1, max_casts=10}, {first_cd=70, max_cd=1.25, min_cd = 0.75, max_casts=20}},
                    [15] = {{first_cd=6.5, max_cd=1, min_cd = 1, max_casts=1, spawn_unit='enemy_storm_elemental'}, {first_cd=52, max_cd=12, min_cd = 10, max_casts=1, spawn_unit='enemy_storm_elemental'}},
                },
                -- Middle Area
                ['3'] = {
                    -- Wave
                    [09] = {{first_cd=6, max_cd=1.5, min_cd = 1, max_casts=7, spawn_unit='enemy_water_spirit_spawnless'}, {first_cd=70, max_cd=1.5, min_cd = 1, max_casts=7, spawn_unit='enemy_water_spirit_spawnless'}},
                    [11] = {{first_cd=3, max_cd=8, min_cd = 6, max_casts=6}},
                    [13] = {{first_cd=10, max_cd=1.25, min_cd = 0.75, max_casts=10}, {first_cd=25, max_cd=23, min_cd = 23, max_casts=2, spawn_unit='enemy_storm_elemental'}},
                    [15] = {{first_cd=1, max_cd=4, min_cd = 3, max_casts=1e99}},
                },
                -- Right Area
                ['4'] = {
                    -- Wave
                    -- [05] = {{first_cd=0.1, max_cd=0.85, min_cd = 0.35, max_casts=10}},
                    [09] = {{first_cd=5, max_cd=6, min_cd = 5, max_casts=11}},
                    [11] = {{first_cd=4, max_cd=3, min_cd = 2, max_casts=8, spawn_unit='enemy_water_spirit_spawnless'}, {first_cd=50, max_cd=3, min_cd = 2.5, max_casts=6, spawn_unit='enemy_water_spirit_spawnless'}}, 
                    [15] = {{first_cd=8, max_cd=12, min_cd = 10, max_casts=1, spawn_unit='enemy_storm_elemental'}, {first_cd=22, max_cd=1.25, min_cd = 1, max_casts=6, spawn_unit='enemy_water_spirit_spawnless'}, {first_cd=50, max_cd=12, min_cd = 10, max_casts=1, spawn_unit='enemy_storm_elemental'}, {first_cd=63, max_cd=0.75, min_cd = 0.5, max_casts=8, spawn_unit='enemy_water_spirit_spawnless'}},
                },
                -- Top Left Area
                ['5'] = {
                    -- Wave
                    -- [10] = {{first_cd=1, max_cd=9, min_cd = 6, max_casts=5, spawn_unit='enemy_water_spirit_spawnless'}}, 
                    [13] = {{first_cd=5, max_cd=1.5, min_cd = 1, max_casts=8, spawn_unit='enemy_water_spirit_spawnless'}, {first_cd=34, max_cd=1.5, min_cd = 1, max_casts=8, spawn_unit='enemy_water_spirit_spawnless'}},
                },
                -- Top Mid Area
                ['6'] = {
                    -- Wave
                    [10] = {{first_cd=3, max_cd=1, min_cd = 0.75, max_casts=3}, {first_cd=39, max_cd=1, min_cd = 0.75, max_casts=1, spawn_unit='enemy_storm_elemental'}},
                    [13] = {{first_cd=13, max_cd=1.25, min_cd = 0.75, max_casts=10}},
                    [15] = {{first_cd=5, max_cd=12, min_cd = 10, max_casts=1, spawn_unit='enemy_storm_elemental'}, {first_cd=54, max_cd=12, min_cd = 10, max_casts=1, spawn_unit='enemy_storm_elemental'}, {first_cd=60, max_cd=6, min_cd = 5, max_casts=11}},
                },
                -- Top Right Area
                ['7'] = {
                    -- Wave
                    [15] = {{first_cd=6, max_cd=5, min_cd = 4, max_casts=10}},
                },
                -- Spawn Units
                ['8'] = {
                    -- Wave
                    -- [05] = {{first_cd=1, max_cd=0.85, min_cd = 0.35, max_casts=7}},
                },
            },
            ['HEROIC'] = {
            },
            ['IRON'] = {
                -- Center Area
                ['1'] = {
                    [1] = {{first_cd=169, max_cd=7, min_cd = 4, max_casts=1e99}},
                },
                -- Left Area
                ['2'] = {
                    [1] = {{first_cd=171, max_cd=5, min_cd = 4, max_casts=10}, {first_cd=225, max_cd=2, min_cd = 1.5, max_casts=10, spawn_unit='enemy_water_spirit_spawnless'}},
                },
                -- Middle Area
                ['3'] = {
                    [1] = {{first_cd=174, max_cd=1, min_cd = 1, max_casts=1, spawn_unit='enemy_storm_elemental'}, {first_cd=176, max_cd=8, min_cd = 5, max_casts=1e99}},
                },
                -- Right Area
                ['4'] = {
                    [1] = {{first_cd=172.5, max_cd=7, min_cd = 4, max_casts=6}, {first_cd=212, max_cd=2.5, min_cd = 2, max_casts=7, spawn_unit='enemy_water_spirit_spawnless'}},
                },
            },
        },
        damage_config = {
            damage_type = DAMAGE_TRUE,
            radius = 100,
            damage_max = {
                [1] = 50,
                [2] = 50,
                [3] = 85,
            },
            damage_min = {
                [1] = 50,
                [2] = 50,
                [3] = 60,
            }
        },
    },
    stage33_envelops = {
        decoy_chance = 0.5,
        cooldown_min = 20,
        cooldown_max = 40,
        max_speed = 20,
        min_speed = 10,
        gold = 5,
        gold_balatro = 1000,
    },
    towers = {
        arborean_sentinels = {
            spearmen = {
                price = 50,
                regen_health = 8,
                hp_max = 92,
                armor = 0.1,
                max_speed = 75,
                melee_attack = {
                    cooldown = 1.2,
                    damage_min = 12,
                    damage_max = 18,
                    range = 120/2,
                },
                ranged_attack = {
                    cooldown = 1.5,
                    damage_min = 9,
                    damage_max = 14,
                    max_range = 330/2,
                    min_range = 121/2,
                    damage_type = DAMAGE_PHYSICAL,
                }
            },
            barkshield = {
                price = 90,
                regen_health = 30,
                hp_max = 300,
                max_speed = 60,
                armor = 0.5,
                melee_attack = {
                    cooldown = 3,
                    damage_min = 25,
                    damage_max = 50,
                    range = 120/2,
                }
            }
        },
        stage_13_sunray = {
            repair_cost = {300, 250, 200, 150},
            attacks_before_special_min = 8,
            attacks_before_special_max = 12,
            repair_cost_iron = {200, 150, 100, 50},
            attacks_before_special_min_iron = 6,
            attacks_before_special_max_iron = 10,
            basic_attack = {
                damage_min = 140,
                damage_max = 260,
                cooldown = 2,
                range = 500/2,
                damage_every = fts(2),
                duration = fts(40),
                damage_type = DAMAGE_TRUE
            },
            special_attack = {
                damage_min = 560,
                damage_max = 780,
                cooldown = 2,
                range = 700/2,
                damage_every = fts(2),
                duration = fts(60),
                speed = 20,
                radius = 40,
                damage_type = DAMAGE_DISINTEGRATE
            },
        },
        stage_17_weirdwood = {
            basic_attack = {
                damage_min = 28,
                damage_max = 50,
                damage_radius = 110/2,
                cooldown = 5,
                min_range = 80/2,
                max_range = 380/2,
            },
            corruption_phases = {1, 2, 3}, 
            corruption_limit = 3, -- corruptions needed to transform
            holder_cost = 150
        },
        stage_18_elven_barrack = {
            rally_range = 320/2,
            max_soldiers = 3,
            soldier = {
                price = {50, 75, 100},
                armor = 0.5,
                hp = 120,
                regen_hp = 10,
                speed = 75,
                dead_lifetime = 10,
                basic_attack = {
                    damage_min = 16, 
                    damage_max = 24,
                    cooldown = 1,
                    range = 75
                }
            },
            corruption_phases = {1, 2, 3}, 
            corruption_limit = 3, -- corruptions needed to transform
            spawn_cooldown = 5
        },
        stage_20_arborean_oldtree = {
            path_index = 2,
            path_index_iron = 3,
            node_index = 170,
            node_index_iron = 105,
            max_range = 100/2,
            damage_min = 350,
            damage_max = 450,
            damage_type = DAMAGE_PHYSICAL,
            price = 250,
            price_iron = 100,
            cooldown = 90
        },
        stage_20_arborean_honey = {
            max_range = 360/2,
            damage_min = 20,
            damage_max = 30,
            cooldown = 5,
            damage_radius = 100,
            damage_type = DAMAGE_PHYSICAL,
            slow_factor = 0.6,
            slow_mod_duration = 0.5,
            aura_duration = 4,
            price = 500,
            price_heroic = 300
        },
        tower_stage_20_arborean_barrack= {
            hp_max = 500,
            armor = 0.3,
            magic_armor = 0,
            soldier_hp_max = 120,
            soldier_armor = 0.1,
            soldier_damage_max = 8,
            soldier_damage_min = 4,
            spawn_cooldown_min = 0.3,
            spawn_cooldown_max = 0.6,
            cooldown_disable = 2,
            price = 50,
            life_thresholds = {0.7, 0.4, 0},
            spawns = 3
        },
        stage_20_arborean_watchtower = {
            basic_attack = {
                damage_min = 19,
                damage_max = 28,
                damage_type = DAMAGE_PHYSICAL,
                cooldown = 2.4, -- NOT LESS THAN 1.8 talk to prog if needed
                max_range = 520/2,
            },
            picked_enemies_to_destroy = {2, 4, 6},
            tunnel_check_cooldown = 3,
        },
        stage_22_arborean_mages_tower = {
            hp_max = 500,
            armor = 0.3,
            magic_armor = 0,
            basic_attack = {
                damage_min = 38,
                damage_max = 52,
                damage_type = DAMAGE_MAGICAL,
                cooldown = 2.5,
                max_range = 520/2,
            },
        },
        tower_stage_28_priests_barrack = {
            spawn_cooldown_min = 0.3,
            spawn_cooldown_max = 0.6,
            cooldown_disable = 2,
            price = 60,
            max_soldiers = 3,
            priest = {
                price = 60,
                hp_max = 110,
                armor = 0,
                regen_health = 0,
                max_speed = 45,
                transform_chances = {100, 0 }, -- abomination, tentacle
                melee = {
                    cooldown = 1,
                    damage_max = 15,
                    damage_min = 10,
                    damage_type = DAMAGE_MAGICAL,
                },
                ranged = {
                    cooldown = 2.5,
                    damage_max = 45,
                    damage_min = 30,
                    range = 180,
                    damage_type = DAMAGE_MAGICAL,
                }
            },
            abomination = {
                hp_max = 500,
                armor = 0,
                regen_health = 0,
                max_speed = 25,
                duration = 20,
                melee_attack = {
                    cooldown = 2,
                    damage_max = 60,
                    damage_min = 45
                },
                eat = {
                    hp_required = 0.3,
                    cooldown = 10,
                },
            },
            tentacle = {
                duration = 15,
                area_attack = {
                    cooldown_min = 1,
                    cooldown_max = 2,
                    damage_max = 20,
                    damage_min = 12,
                    damage_type = DAMAGE_PHYSICAL,
                    radius = 50
                }
            }
        },
    }
}

local reinforcements = {
    soldier = {
        cooldown = 15,
        duration = 12,
        hp_max = 40,
        regen_health = 8,
        armor = 0,
        max_speed = 64,
        melee_attack = {
           cooldown = 1,
           damage_min = 1,
           damage_max = 2,
           range = 72
        }
    }
}

local upgrades = {
    
    --TOWERS
    towers_war_rations = {
        hp_factor = 1.10, 
    },
    towers_wise_investment = {
        refund_factor = 0.9,
    },
    towers_scoping_mechanism = {
        rally_range_factor = 1.10,
        range_factor = 1.10,
    },
    towers_golden_time = {
        early_wave_reward_per_second_factor = 1.8,
    },
    towers_improved_formulas = {
        range_factor = 1.25,
    },
    towers_royal_training = {
        reduce_cooldown = 2,
        reinforcements_cooldown = 3,
    },
    towers_favorite_customer = {
        refund_cost_factor = 0.5,
        refund_cost_factor_one_level = 0.2,
    },
    towers_keen_accuracy = { -- battle_fervor
        -- deck_data = {
        --     total_cards = 10,
        --     trigger_cards = 1,
        -- },
        -- damage_factor = 2,
        cooldown_mult = 0.8
    },

    --HEROES
    heroes_desperate_effort = {
        -- damage_factor = 1.15,
        -- duration = 8,
        -- health_trigger = 0.2,
        armor_penetration = 0.2
    },
    heroes_lone_wolves = {
        distance_to_trigger = 150,
        duration = 3,
        xp_gain_factor = 1.5,
    },
    heroes_visual_learning = {
        distance_to_trigger = 200,
        duration = 3,
        armor_bonus = 0.1,
    },
    heroes_unlimited_vigor = {
        cooldown_factor = 0.9,
    },
    heroes_lethal_focus = {
        deck_data = {
            total_cards = 5,
            trigger_cards = 1,
        },
        damage_factor = 2,
        damage_factor_area = 1.5,
    },
    heroes_nimble_physique = {
        deck_data = {
            total_cards = 5,
            trigger_cards = 1,
        },
    },
    heroes_limit_pushing = {
        deck_data = {
            total_cards = 5,
            trigger_cards = 1,
        },
    },

    --REINFORCEMENTS
    reinforcements_master_blacksmiths = {
        damage_factor = 1.1,
        armor = 0.2,
    },
    reinforcements_intense_workout = {
        hp_factor = 1.25,
        duration_extra = 3,
    },
    reinforcements_rebel_militia = {
        soldier = {
            cooldown = 15,
            duration = 16,
            hp_max = 100,
            regen_health = 14,
            armor = 0.4,
            max_speed = 64,
            melee_attack = {
               cooldown = 1,
               damage_min = 4,
               damage_max = 8,
               range = 70
            }
        }
    },
    reinforcements_shadow_archer = {
        soldier = {
            cooldown = 15,
            duration = 14,
            hp_max = 60,
            regen_health = 10,
            armor = 0.2,
            max_speed = 64,
            melee_attack = {
               cooldown = 1,
               damage_min = 8,
               damage_max = 12,
               range = 72
            },
            ranged_attack = {
                max_range = 160,
                min_range = 50,
                cooldown = 1.5,
                damage_min = 10,
                damage_max = 14,
            }
        }
    },
    reinforcements_thorny_armor = {
        spiked_armor = 0.20,
    },
    reinforcements_night_veil = { -- umbral bow
        -- dodge_chance = 0.4,
        extra_range = 50,
        cooldown_red = 0.5,
    },
    reinforcements_special_linirea = {
        soldier = {
            cooldown = 15,
            duration = 16,
            hp_max = 140,
            regen_health = 24,
            armor = 0.4,
            spiked_armor = 0.20,
            max_speed = 64,
            melee_attack = {
               cooldown = 1,
               damage_min = 10,
               damage_max = 14,
               range = 72
            }
        }
    },
    reinforcements_special_dark_army = {
        soldier = {
            cooldown = 15,
            duration = 14,
            hp_max = 60,
            regen_health = 12,
            armor = 0.1,
            max_speed = 30 * 2,
            melee_attack = {
               cooldown = 1,
               damage_min = 8,
               damage_max = 12,
               range = 70
            }
        },
        crow = {
            max_speed = 100,
            target_range = 200, -- distance to target the enemy
            chase_range = 300, -- max distance to continue chasing the enemy
            melee_attack = {
               cooldown = 0.25,
               damage_type = DAMAGE_PHYSICAL,
               damage_min = 2,
               damage_max = 3,
               range = 10
            }
        },
    },

    --ALLIANCE
    alliance_merciless = {
        damage_factor_per_tower = 0.03,
    },
    alliance_corageous_stand = {
        hp_factor_per_tower = 0.04,
    },
    alliance_shady_company = {
        damage_extra = 0.05
    },
    alliance_friends_of_the_crown = {
        cost_red_per_hero = 5,
    },
    alliance_shared_reserves = {
        extra_gold = 100,
    },
    alliance_seal_of_punishment = {
        cooldown = 180,
        radius = 50,
        damage_min = 10, -- per tick
        damage_max = 20, -- per tick
        cycle_time = 0.25,  -- how often it is applied
        duration = 4,
    },
    alliance_flux_altering_coils = {
        cooldown = 150,
        radius = 120,
        nodes_teleport = 25,
        nodes_limit = 20,
    },
    alliance_display_of_true_might_linirea = {
        slowdown_factor = 0.5,
        slowdown_duration = 3,
    },
    alliance_display_of_true_might_dark = {
        slowdown_factor = 0.6,
        slowdown_duration = 3,
    },
    points_distribution = {
        0, -- after stage 1
        3,
        6,
        9,
        12,
        18, -- after BF1
        21,
        24,
        27,
        30,
        38, -- after BF2
        41,
        44,
        47,
        52, -- after BF3 Mydrias
        60, -- after BF3 Overseer
    },
}

local items = {
    cluster_bomb = {
        damage_max = 72,
        damage_min = 48,
        damage_radius = 90/2,
        damage_max_small = 36,
        damage_min_small = 24,
        damage_radius_small = 90/2,
        damage_type = DAMAGE_PHYSICAL,
    },
    portable_coil = {
        range = 80,
        damage_max = 72,
        damage_min = 48,
        damage_max_chain = 36,
        damage_min_chain = 24,
        damage_type = DAMAGE_ELECTRICAL, -- eq. to explosive
        max_targets = 5,
        chain_range = 120,
        max_chain_length = 3,
        stun_duration = 4,
    },
    deaths_touch = {
        radius = 110/2,
        damage_boss = 1000,
    },
    scroll_of_spaceshift = {
        radius = 150/2,
        max_targets = 10,
        nodes_teleport = 75, -- in nodes
        nodes_limit = 20,
    },
    loot_box = {
        radius = 90/2,
        damage_max = 300,
        damage_min = 300,
        damage_type = DAMAGE_PHYSICAL,
        gold_amount = 300,
    },
    medical_kit = {
        hearts = 3,
    },
    winter_age = {
        stun_duration = 15,
    },
    summon_blackburn = {
        speed = 50,
        cooldown = 25,
        spawn = {
            damage_min = 500,
            damage_max = 500,
            damage_type = DAMAGE_PHYSICAL,
            damage_radius = 150 / 2,
            stun_duration = 0.5
        },
        basic_attack = {
            cooldown = 1.5,
            damage_min = 250,
            damage_max = 300,
            damage_type = DAMAGE_PHYSICAL,
            damage_radius = 120 / 2,
        },
        special_attack = {
            cooldown = 6,
            min_range = 200,
            max_range = 320,
            damage_min = 120,
            damage_max = 180,
            damage_type = DAMAGE_TRUE,
            damage_radius = 150 / 2,
        },
        attack_cooldown_special = 0,
        hp_max = 1000,
        regen_health = 15,
        armor = 0.5,
        duration = 30,
    },
    veznan_wrath = {
        damage_max = 2000,
        damage_min = 2000,
    },
}

local balance =  {
    heroes = heroes,
    enemies = enemies,
    towers = towers,
    relics = relics,
    specials = specials,
    reinforcements = reinforcements,
    upgrades = upgrades,
    items = items,
}

if game and game.store and game.store.level_mode then
    if game.store.level_mode == GAME_MODE_IRON then
        balance.specials.trees.guardian_tree.cooldown_max = 30
        balance.specials.trees.guardian_tree.cooldown_min = 30
        balance.specials.trees.guardian_tree.wave_config = {true, }
        balance.specials.trees.guardian_tree.max_range = 450
        balance.specials.trees.guardian_tree.min_range = 15
        balance.specials.stage07_temple.activation_wave = 1

        -- balance.specials.trees.heart_of_the_arborean.cooldown_max = 30
        -- balance.specials.trees.heart_of_the_arborean.cooldown_min = 30
        -- balance.specials.trees.heart_of_the_arborean.damage_max = 40
        -- balance.specials.trees.heart_of_the_arborean.damage_min = 30

    elseif game.store.level_mode == GAME_MODE_HEROIC then
        balance.specials.trees.guardian_tree.cooldown_max = 30
        balance.specials.trees.guardian_tree.cooldown_min = 30
        balance.specials.trees.guardian_tree.aura_duration = 5
        balance.specials.trees.guardian_tree.wave_config = {true, true, true, true, true, true, }
        balance.specials.trees.guardian_tree.max_range = 450
        balance.specials.trees.guardian_tree.min_range = 15
        balance.specials.stage07_temple.activation_wave = 1
    end
end

return balance
