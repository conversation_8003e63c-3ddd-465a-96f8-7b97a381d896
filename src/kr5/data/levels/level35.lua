
--
-- level 35 / kr5
--

local log = (require 'klua.log'):new('level01')
local signal = require 'hump.signal'

local E = require 'entity_db'
local S = require 'sound_db'
local U = require 'utils'
local LU = require 'level_utils'
local V = require 'klua.vector'
local P = require 'path_db'
local storage = require 'storage'
local GR = require 'grid_db'
require 'constants'

local function fts(v) return v / FPS end

------------------------------------------------------------

local level = {}

function level:load(store)
end

function level:update(store)
    
    if store.level_mode == GAME_MODE_IRON then

        local starting_gold = store.player_gold

        local holder = table.filter(game.store.entities, function(k,e) return e.tower and e.tower.holder_id == "48" end)[1]
        holder.tower.upgrade_to = 'tower_rocket_gunners_lvl4'

        local holder = table.filter(game.store.entities, function(k,e) return e.tower and e.tower.holder_id == "43" end)[1]
        holder.tower.upgrade_to = 'tower_rocket_gunners_lvl4'

        -- local holder = table.filter(game.store.entities, function(k,e) return e.tower and e.tower.holder_id == "48" end)[1]
        -- holder.tower.upgrade_to = 'tower_pandas_lvl1'

        coroutine.yield()
        store.player_gold = starting_gold

        -- wait for victory
        while not store.waves_finished or LU.has_alive_enemies(store) do
            coroutine.yield()
        end        
    end
    
    if store.level_mode == GAME_MODE_HEROIC then
        -- wait for victory
        while not store.waves_finished or LU.has_alive_enemies(store) do
            coroutine.yield()
        end
    end

    if store.level_mode == GAME_MODE_CAMPAIGN then
        self.bossfight_ended = false

        local controller_boss_prefight
        local controller_redboy
        local controller_princess
        for _, e in pairs(store.entities) do
            if e.template_name == 'controller_stage_35_bull_king' then
                controller_boss_prefight = e
            elseif e.template_name == 'controller_stage_35_princess_powers' then
                controller_princess = e
            elseif e.template_name == 'controller_stage_35_redboy_powers' then
                controller_redboy = e
            end
        end

        if not store.restarted and not main.params.skip_cutscenes then

            -- Start level cinematic
            signal.emit('show-curtains')
            signal.emit('hide-gui')
            signal.emit('start-cinematic')

            -- Zoom bull boss
            signal.emit('pan-zoom-camera', 1, {x=512,y=500},OVtargets(nil, 1.4))
            U.y_wait(store, 2.5)

            controller_boss_prefight.do_taunt = "LV35_BOSS_INTRO_01"
            U.y_wait(store, 3.5)

            -- Zoom princess
            signal.emit('pan-zoom-camera', 1, {x=800,y=500},OVtargets(nil, 1.2))

            -- Spawn princess
            controller_princess.appear = true
            
            U.y_wait(store, 1.0)
            controller_boss_prefight.do_taunt = "LV35_BOSS_INTRO_02"
            U.y_wait(store, 2)

            -- Spawn redboy
            controller_redboy.appear = true
            U.y_wait(store, 1.5)

            -- Zoom redboy
            signal.emit('pan-zoom-camera', 1, {x=300,y=500},OVtargets(nil, 1.2))
            U.y_wait(store, 2.5)
            controller_redboy.do_taunt = "LV35_BOSS_INTRO_03"
            U.y_wait(store, 3.5)

            -- Zoom center
            signal.emit('pan-zoom-camera', 1, {x=512,y=382},OVtargets(nil, 1.0))
            U.y_wait(store, 1.5)

            -- End cinematic
            signal.emit('hide-curtains')
            signal.emit('show-gui')
            signal.emit('end-cinematic')
        end

        -- wait for victory
        while not store.waves_finished or LU.has_alive_enemies(store) do
            coroutine.yield()
        end

        controller_boss_prefight.summon_boss = true

        while not self.bossfight_ended do
            coroutine.yield()
        end

        U.y_wait(store, 2)

    end

end

------------------------------------------------------------

return level

