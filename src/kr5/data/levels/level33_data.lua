return {
    custom_spawn_pos = {
        {
            pos = {
                x = 518,
                y = 262
            }
        },
        {
            pos = {
                x = 718,
                y = 262
            }
        }
    },
    custom_start_pos = {
        pos = {
            x = 373,
            y = 202
        },
        zoom = 1.2
    },
    entities_list = {
        {
            template = "controller_stage33_envelops",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "controller_stage_33_boat",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "controller_stage_33_ciclone",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "controller_stage_33_house_doors"
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 8,
            pos = {
                x = 622,
                y = 167
            },
            strikes_spawn_radius = 85
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 4,
            pos = {
                x = 925,
                y = 264
            },
            strikes_spawn_radius = 117
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 2,
            pos = {
                x = 268,
                y = 276
            },
            strikes_spawn_radius = 117
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 1,
            pos = {
                x = 617,
                y = 279
            },
            strikes_spawn_radius = 181
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 10,
            pos = {
                x = 617,
                y = 279
            },
            strikes_spawn_radius = 181
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 3,
            pos = {
                x = 614,
                y = 467
            },
            strikes_spawn_radius = 117
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 6,
            pos = {
                x = 614,
                y = 610
            },
            strikes_spawn_radius = 117
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 5,
            pos = {
                x = 296,
                y = 628
            },
            strikes_spawn_radius = 146
        },
        {
            template = "controller_stage_33_lightning_strike",
            area_id = 7,
            pos = {
                x = 925,
                y = 628
            },
            strikes_spawn_radius = 146
        },
        {
            template = "controller_stage_33_tambor",
            pos = {
                x = 303,
                y = 390
            }
        },
        {
            template = "debug_path_renderer",
            ["path_debug.background_color"] = {
                46,
                193,
                142,
                0
            },
            ["path_debug.path_color"] = {
                168,
                199,
                169,
                0
            },
            pos = {
                x = -300,
                y = 868
            }
        },
        {
            template = "decal_background",
            pos = {
                x = 512,
                y = 384
            },
            ["render.sprites[1].name"] = "Stage33_0001",
            ["render.sprites[1].z"] = 1000
        },
        {
            template = "decal_defend_point5",
            ["editor.alpha"] = 10,
            ["editor.exit_id"] = 1,
            ["editor.flip"] = 1,
            ["editor.orientation"] = 1,
            pos = {
                x = 634,
                y = 63
            }
        },
        {
            template = "decal_defense_flag5",
            ["editor.flip"] = 0,
            pos = {
                x = 556,
                y = 58
            }
        },
        {
            template = "decal_defense_flag5",
            ["editor.flip"] = 0,
            pos = {
                x = 711,
                y = 59
            }
        },
        {
            template = "decal_generic_kill_area",
            kill_area_id = 1,
            kill_radius = 178,
            pos = {
                x = 616,
                y = 617
            }
        },
        {
            template = "decal_stage33_envelop_spawn_pos",
            pos = {
                x = 218,
                y = 417
            }
        },
        {
            template = "decal_stage33_envelop_spawn_pos",
            pos = {
                x = -47,
                y = 419
            }
        },
        {
            template = "decal_stage33_envelop_spawn_pos",
            pos = {
                x = 351,
                y = 419
            }
        },
        {
            template = "decal_stage33_envelop_spawn_pos",
            pos = {
                x = 136,
                y = 422
            }
        },
        {
            template = "decal_stage33_envelop_spawn_pos",
            pos = {
                x = 22,
                y = 426
            }
        },
        {
            template = "decal_stage33_envelop_spawn_pos",
            pos = {
                x = 267,
                y = 428
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 4,
            ["editor.r"] = -1.1742690153582001e-13,
            pos = {
                x = 1131,
                y = 261
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 3,
            ["editor.r"] = -3.1415926535899001,
            pos = {
                x = 46,
                y = 275
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 2,
            ["editor.r"] = -1.7506829319558e-14,
            pos = {
                x = 1131,
                y = 640
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 145,
            ["editor.path_id"] = 1,
            ["editor.r"] = 1.5707963267948,
            pos = {
                x = 50,
                y = 659
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 5,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 588,
                y = 693
            }
        },
        {
            template = "editor_wave_flag",
            ["editor.len"] = 130,
            ["editor.path_id"] = 6,
            ["editor.r"] = -4.7123889803847003,
            pos = {
                x = 636,
                y = 693
            }
        },
        {
            template = "mega_spawner",
            load_file = "level33_spawner"
        },
        {
            template = "stage_33_mask_1",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_1_destroyed",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_2",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_3",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_4",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_5",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_6",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_7",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_8",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_9",
            ["editor.game_mode"] = 2,
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_props",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_water_big",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_mask_water_small",
            pos = {
                x = 512,
                y = 384
            }
        },
        {
            template = "stage_33_spawner",
            pos = {
                x = -50,
                y = 270
            },
            ["spawner.name"] = "trail3",
            ["spawner.pi"] = 1
        },
        {
            template = "stage_33_spawner",
            pos = {
                x = 500,
                y = 465
            },
            ["spawner.name"] = "trail1",
            ["spawner.pi"] = 1
        },
        {
            template = "stage_33_spawner",
            pos = {
                x = 740,
                y = 465
            },
            ["spawner.name"] = "trail2",
            ["spawner.pi"] = 1
        },
        {
            template = "tower_holder_blocked_elemental_water",
            ["editor.game_mode"] = 2,
            pos = {
                x = 618,
                y = 264
            },
            ["tower.default_rally_pos"] = {
                x = 618,
                y = 357
            },
            ["tower.holder_id"] = "27",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "27",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 1,
            pos = {
                x = 435,
                y = 342
            },
            ["tower.default_rally_pos"] = {
                x = 435,
                y = 292
            },
            ["tower.holder_id"] = "8",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "8"
        },
        {
            template = "tower_holder_blocked_elemental_water",
            ["editor.game_mode"] = 1,
            pos = {
                x = 328,
                y = 530
            },
            should_flip = true,
            ["tower.default_rally_pos"] = {
                x = 328,
                y = 642
            },
            ["tower.holder_id"] = "10",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "10"
        },
        {
            template = "tower_holder_blocked_stage_33_house_1",
            ["editor.game_mode"] = 1,
            pos = {
                x = 618,
                y = 264
            },
            ["tower.default_rally_pos"] = {
                x = 618,
                y = 357
            },
            ["tower.holder_id"] = "7",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "7",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_stage_33_house_2",
            ["editor.game_mode"] = 1,
            pos = {
                x = 268,
                y = 187
            },
            ["tower.default_rally_pos"] = {
                x = 268,
                y = 271
            },
            ["tower.holder_id"] = "2",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "2",
            unlock_holder_type = "tower_holder_elemental_water"
        },
        {
            template = "tower_holder_blocked_stage_33_house_2",
            ["editor.game_mode"] = 3,
            pos = {
                x = 268,
                y = 187
            },
            ["tower.default_rally_pos"] = {
                x = 268,
                y = 271
            },
            ["tower.holder_id"] = "42",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "42",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_stage_33_house_3",
            ["editor.game_mode"] = 1,
            pos = {
                x = 921,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 921,
                y = 640
            },
            ["tower.holder_id"] = "11",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "11",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_stage_33_house_3",
            ["editor.game_mode"] = 3,
            pos = {
                x = 921,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 921,
                y = 640
            },
            ["tower.holder_id"] = "51",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "51",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_stage_33_invisible",
            ["editor.game_mode"] = 1,
            pos = {
                x = 503,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 604,
                y = 620
            },
            ["tower.holder_id"] = "12",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "12",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_stage_33_invisible",
            ["editor.game_mode"] = 3,
            pos = {
                x = 503,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 604,
                y = 620
            },
            ["tower.holder_id"] = "52",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "52",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_stage_33_invisible",
            ["editor.game_mode"] = 1,
            pos = {
                x = 720,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 604,
                y = 575
            },
            ["tower.holder_id"] = "13",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "13",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_stage_33_invisible",
            ["editor.game_mode"] = 3,
            pos = {
                x = 720,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 604,
                y = 575
            },
            ["tower.holder_id"] = "53",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "53",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 1,
            pos = {
                x = 766,
                y = 157
            },
            ["tower.default_rally_pos"] = {
                x = 694,
                y = 224
            },
            ["tower.holder_id"] = "4",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "4"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 766,
                y = 157
            },
            ["tower.default_rally_pos"] = {
                x = 689,
                y = 228
            },
            ["tower.holder_id"] = "24",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "24"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 766,
                y = 157
            },
            ["tower.default_rally_pos"] = {
                x = 689,
                y = 228
            },
            ["tower.holder_id"] = "44",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "44"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 1,
            pos = {
                x = 1030,
                y = 178
            },
            ["tower.default_rally_pos"] = {
                x = 1030,
                y = 263
            },
            ["tower.holder_id"] = "5",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "5"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 1030,
                y = 178
            },
            ["tower.default_rally_pos"] = {
                x = 1030,
                y = 263
            },
            ["tower.holder_id"] = "25",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "25"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 1030,
                y = 178
            },
            ["tower.default_rally_pos"] = {
                x = 1030,
                y = 263
            },
            ["tower.holder_id"] = "45",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "45"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 1,
            pos = {
                x = 435,
                y = 180
            },
            ["tower.default_rally_pos"] = {
                x = 425,
                y = 271
            },
            ["tower.holder_id"] = "3",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "3"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 435,
                y = 180
            },
            ["tower.default_rally_pos"] = {
                x = 425,
                y = 271
            },
            ["tower.holder_id"] = "23",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "23"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 435,
                y = 180
            },
            ["tower.default_rally_pos"] = {
                x = 425,
                y = 271
            },
            ["tower.holder_id"] = "43",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "43"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 1,
            pos = {
                x = 110,
                y = 187
            },
            should_flip = true,
            ["tower.default_rally_pos"] = {
                x = 108,
                y = 271
            },
            ["tower.holder_id"] = "1",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "1"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 110,
                y = 187
            },
            ["tower.default_rally_pos"] = {
                x = 108,
                y = 271
            },
            ["tower.holder_id"] = "21",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "21"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 110,
                y = 187
            },
            ["tower.default_rally_pos"] = {
                x = 108,
                y = 271
            },
            ["tower.holder_id"] = "41",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "41"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 268,
                y = 187
            },
            ["tower.default_rally_pos"] = {
                x = 268,
                y = 271
            },
            ["tower.holder_id"] = "22",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "22",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 618,
                y = 264
            },
            ["tower.default_rally_pos"] = {
                x = 618,
                y = 357
            },
            ["tower.holder_id"] = "47",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "47",
            unlock_holder_type = "tower_holder_sea_of_trees_13"
        },
        {
            template = "tower_holder_blocked_elemental_water",
            ["editor.game_mode"] = 1,
            pos = {
                x = 808,
                y = 326
            },
            ["tower.default_rally_pos"] = {
                x = 808,
                y = 256
            },
            ["tower.holder_id"] = "6",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "6"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 808,
                y = 326
            },
            ["tower.default_rally_pos"] = {
                x = 808,
                y = 256
            },
            ["tower.holder_id"] = "46",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "46"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 435,
                y = 342
            },
            ["tower.default_rally_pos"] = {
                x = 435,
                y = 292
            },
            ["tower.holder_id"] = "48",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "48"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 1,
            pos = {
                x = 164,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 164,
                y = 642
            },
            ["tower.holder_id"] = "9",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "9"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 164,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 164,
                y = 642
            },
            ["tower.holder_id"] = "29",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "29"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 164,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 164,
                y = 642
            },
            ["tower.holder_id"] = "49",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "49"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 328,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 328,
                y = 641
            },
            ["tower.holder_id"] = "30",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "30"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 3,
            pos = {
                x = 328,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 328,
                y = 641
            },
            ["tower.holder_id"] = "50",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "50"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 503,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 604,
                y = 620
            },
            ["tower.holder_id"] = "32",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "32"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 720,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 604,
                y = 575
            },
            ["tower.holder_id"] = "33",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "33"
        },
        {
            template = "tower_holder_sea_of_trees_13",
            ["editor.game_mode"] = 2,
            pos = {
                x = 921,
                y = 530
            },
            ["tower.default_rally_pos"] = {
                x = 921,
                y = 640
            },
            ["tower.holder_id"] = "31",
            ["tower.terrain_style"] = 13,
            ["ui.nav_mesh_id"] = "31",
            unlock_holder_type = "tower_holder_blocked_elemental_water"
        }
    },
    ignore_walk_backwards_paths = {},
    invalid_path_ranges = {},
    level_mode_overrides = {
        {
            locked_powers = {},
            locked_towers = {},
            max_upgrade_level = 5
        },
        {},
        {
            available_towers = {
                "tower_build_flamespitter",
                "tower_build_ballista"
            },
            locked_towers = {
                "tower_build_arborean_emissary",
                "tower_build_sand",
                "tower_build_paladin_covenant",
                "tower_build_tricannon",
                "tower_build_demon_pit",
                "tower_build_arcane_wizard",
                "tower_build_elven_stargazers",
                "tower_build_rocket_gunners",
                "tower_build_barrel",
                "tower_build_royal_archers",
                "tower_build_ray",
                "tower_build_ghost",
                "tower_build_necromancer",
                "tower_build_hermit_toad"
            }
        }
    },
    level_terrain_type = 5,
    locked_hero = false,
    max_upgrade_level = 5,
    nav_mesh = {
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {}
    },
    required_exoskeletons = {
        "stage31_wood_holder_rayoDef",
        "stage31_wood_holder_rayo_explosionDef",
        "stage_33_anim_propsDef",
        "stage_33_olas_chicasDef",
        "stage_33_olas_grandesDef",
        "stage_33_dirtyDef",
        "stage_33_shadow_screenDef",
        "stage_33_storm_clouds_derDef",
        "stage_33_storm_clouds_izqDef",
        "stage_33_tejasDef",
        "stage_33_storm_shadowDef",
        "stage_33_clouds_flyDef",
        "stage_33_shadows_flyDef",
        "stage_33_tejas_puertaDef",
        "stage_33_storm_rayosDef",
        "stage_3_barco_velaDef",
        "stage_3_barcoDef",
        "stage33_water_mistDef",
        "stage33_water_holder_animations_parcheDef",
        "stage33_water_holder_cuernosDef",
        "stage33_water_holder_dragonDef",
        "stage33_water_holder_gradienteDef",
        "stage33_water_holder_habilidad_1Def",
        "stage33_water_holder_healDef",
        "stage33_water_holder_jarraDef",
        "stage33_water_holder_jarrahojasDef",
        "stage33_water_holder_rayo_explosionDef",
        "stage33_water_holder_rayoDef",
        "stage33_water_dragonflyDef",
        "stage33_water_dragonrootDef",
        "stage33_water_debuff_midDef",
        "stage33_explosion_escombrosDef"
    },
    required_sounds = {
        "music_stage33",
        "enemies_terrain_wukong_2",
        "stage_33",
        "terrain_wukong_common",
        "tower_paladin_covenant"
    },
    required_textures = {
        "go_stage33_bg",
        "go_stage33",
        "go_enemies_terrain_8_2",
        "go_towers_paladin_covenant"
    }
}
