return {
version=3,
partScaleCompensation=1,
fps=30,
parts={
{'goldholder_coin_asst_wukong_goldragon_coing',0,0},
{'goldholder_coin_asst_wukong_goldragon_coingold',0,0},
},
attach_points={
},
animations={
{name='in',frames={
{{1,2,0,-1.15,0,1,1,0,0,0},{1,1,0,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.234,-1.15,0,1,1,0,0,0},{1,1,0.234,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.438,-1.15,0,1,1,0,0,0},{1,1,0.438,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.609,-1.15,0,1,1,0,0,0},{1,1,0.609,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.750,-1.15,0,1,1,0,0,0},{1,1,0.750,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.859,-1.15,0,1,1,0,0,0},{1,1,0.859,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.938,-1.15,0,1,1,0,0,0},{1,1,0.938,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.984,-1.15,0,1,1,0,0,0},{1,1,0.984,-1.3,-0.8,1,1,0,0,0}},
}},
{name='loop',frames={
{{1,2,1,-1.15,0,1,1,0,0,0},{1,1,1,-1.3,-0.8,1,1,0,0,0}},
}},
{name='out',frames={
{{1,2,1,-1.15,0,1,1,0,0,0},{1,1,1,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.668,-1.15,0,1,1,0,0,0},{1,1,0.668,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0.332,-1.15,0,1,1,0,0,0},{1,1,0.332,-1.3,-0.8,1,1,0,0,0}},
{{1,2,0,-1.15,0,1,1,0,0,0},{1,1,0,-1.3,-0.8,1,1,0,0,0}},
}},
},
}
