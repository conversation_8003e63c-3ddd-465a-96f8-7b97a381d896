return {
version=3,
partScaleCompensation=1,
fps=30,
parts={
{'stage_35_fx_anime_asst_fx_anime01',83,0},
{'stage_35_fx_anime_asst_fx_anime02',120.8,0},
{'stage_35_fx_anime_asst_fx_anime03',160.1,0},
},
attach_points={
},
animations={
{name='in',frames={
{{1,1,1,-69.75,-85.3,0.699966,0.699966,0,0,0}},
{{1,1,1,-69.75,-85.3,0.699966,0.699966,0,0,0}},
{{1,2,1,-88.05,-73,0.699966,0.699966,0,0,0}},
{{1,3,1,-114.85,-63.85,0.699966,0.699966,0,0,0}},
}},
},
}
