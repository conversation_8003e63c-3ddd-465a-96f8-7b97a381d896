--
-- 2d spatial search optimization
-- Copyright (c) 2010-2025 Kalio Ltda.
-- 

local log = (require 'klua.log'):new('sso')

local SSO = {}

-- persistent lists (non-spatial)

SSO.persistent_lists = {}
function SSO:reset_p_lists()
    self.persistent_lists = {
        alive_enemies = {},
        graveyards = {},
        modifiers = {},
    }
end

function SSO:get_p_list(name)
    local l = self.persistent_lists and self.persistent_lists[name]
    if DEBUG and not l then
        log.error('SSO list %s does not exist', name)
    end
    return l
end

-- spatial search lists

SSO.bb_mode = true
if SSO.bb_mode then

    -- ------------------------------
    -- bounding box mode : space divided in grids of fixed size
    -- ------------------------------

    SSO.grids = {}
    SSO.bb_step = 128
    
    -- public interface
    function SSO:reset(entities)    
        --log.todo('SSO.bb - RESET ------ ')
        self.bb_all_entities = entities
        self.bb_grids = {}
    end

    function SSO:is_all_entities(entities)
        if entities == self.bb_all_entities then 
            return true
        elseif DEBUG then
            log.traceall('WARNING: the following find cannot be optimized as it does not pass store.entities')
        end
    end
    
    function SSO:create(name, x,y,w,h)
        local g = {}
        g.x = x
        g.y = y
        g.w = w
        g.h = h
        g.min_ix = 0
        g.max_ix = 0
        g.min_iy = 0
        g.max_iy = 0
        g.grid = {}
        g.name = name
        self.bb_grids[name] = g
        return g
    end

    function SSO:insert(g, x,y, v)
        --if v == nil then log.todo('SSO.bb %s - TRYING TO INSERT NIL: %s,%s (%s)%s', g.name, x,y, v.id, v.template_name) end
            
        local box,boy,bstep = g.x,g.y,self.bb_step
        local ix = math.ceil((x - box) / bstep)
        local iy = math.ceil((y - boy) / bstep)
        local gg = g.grid
        if not gg[ix] then gg[ix] = {} end
        if not gg[ix][iy] then gg[ix][iy] = {} end
        local gxy = gg[ix][iy]
        table.insert(gxy, v)
        g.min_ix = math.min(g.min_ix, ix)
        g.max_ix = math.max(g.max_ix, ix)
        g.min_iy = math.min(g.min_iy, iy)
        g.max_iy = math.max(g.max_iy, iy)
        --log.todo('SSO.bb - INSERT %s : %s,%s -> %s,%s', g.name, x,y, ix,iy)
    end

    function SSO:filter(out, name, x,y, range, filter_fn)
        local g = self.bb_grids[name]
        if not g then
            log.error('bb_grid %s not found', name)
            return
        end

        local box,boy,bstep = g.x,g.y,self.bb_step
        local ix0 = math.max(g.min_ix, math.ceil(((x-range) - box) / bstep))
        local ix1 = math.min(g.max_ix, math.ceil(((x+range) - box) / bstep))
        local iy0 = math.max(g.min_iy, math.ceil(((y-range) - boy) / bstep))
        local iy1 = math.min(g.max_iy, math.ceil(((y+range) - boy) / bstep))

        --log.todo('+++++++++ ix:%s,%s iy:%s,%s   x:%s - %s, y:%s - %s',
        --         ix0,ix1, iy0,iy1, g.min_ix,g.max_ix, g.min_iy,g.max_iy)
        
        local gg = g.grid
        for ix=ix0,ix1 do
            if gg[ix] then 
                for iy=iy0,iy1 do
                    if gg[ix][iy] then
                        for _,v in pairs(gg[ix][iy]) do 
                            if v and (not filter_fn or filter_fn(_,v)) then
                                table.insert(out, v)
                            end
                        end
                    end
                end
            end
        end
    end
    
else
    -- ------------------------------
    -- quadtree mode
    -- ------------------------------

    SSO.trees = {}
    SSO.max_depth = 8
    SSO.max_count = 4

    function SSO:reset()
        --log.todo('qt reset -----------')
        self.trees = {}
    end

    function SSO:create(name, x,y,w,h)
        local n = {
            x, y, w, h, -- 1-4: bounding box
            0,          --   5: depth
            {},         --   6: ptrs
            --   7: children nodes
        }
        n.name = name
        self.trees[name] = n
        return n
    end

    function SSO:insert(n, x,y, v)
        local nx,ny,nw,nh,nd = unpack(n)
        local nptrs = n[6]
        local nch = n[7]
        --log.todo('%s TRY INSERT depth:%s e:(%s)%s  %s,%s > (%s,%s,%s,%s)  qt:%s node:%s',
        --         string.rep(' ',nd), nd, v.id,v.template_name, x,y, nx,ny,nx+nw,ny+nh, n.name, n)
        
        -- check bounds
        if
            x <= nx or x > nx + nw or
            y <= ny or y > ny + nh
        then
            return false
        end
        
        -- fill leaf it not full or depth reached
        if nptrs and (#nptrs < SSO.max_count or nd >= SSO.max_depth) then
            --log.todo('%s   INSERT OK eid:(%s)%s ', string.rep(' ',nd), v.id, v.template_name)
            table.insert(nptrs, {x,y,v})
            return true
        end

        -- subdivide if not done yer
        if not nch then
            local ch_w,ch_h = nw/2, nh/2
            local new_depth = nd+1
            nch = {
                {nx + ch_w, ny + ch_h, ch_w, ch_h, new_depth, {}}, -- top/right
                {nx       , ny + ch_h, ch_w, ch_h, new_depth, {}}, -- top/left
                {nx       , ny,        ch_w, ch_h, new_depth, {}}, -- bot/left
                {nx + ch_w, ny,        ch_w, ch_h, new_depth, {}}, -- bot/right
            }
            n[6] = nil  -- remove leaf entities from the node 
            n[7] = nch  -- add children to the node
            -- move leaf entities to subtree
            if not nptrs then
                --log.todo('%s   ERROR!! nptrs is nil. x,y:%s,%s  depth:%s  v:(%s)%s  n:%s',
                --         string.rep(' ',nd), x,y,new_depth, v.id, v.template_name, getdump(n))
            end
            --log.todo('%s MOVING LEAVES...', string.rep(' ',nd))
            for _,pptr in pairs(nptrs) do
                for _,nnch in pairs(nch) do
                    if self:insert(nnch, pptr[1],pptr[2], pptr[3]) then
                        goto next_entity
                    end
                end
                ::next_entity::
            end
        end

        -- insert the current one
        for _,nnch in pairs(nch) do
            if self:insert(nnch, x,y, v) then
                return true
            end
        end

        return false
    end

    function SSO:filter(out, name, x,y, range, filter_fn)
        local n = self.trees[name]
        if not n then
            log.error('quadtree %s not found', name)
            return
        end
        SSO:filter_node(out, n, x,y, range, filter_fn)
    end

    function SSO:filter_node(out, n, x,y, range, filter_fn)
        local nx,ny,nw,nh,nd = unpack(n)
        local nptrs = n[6]
        local nch = n[7]

        -- check bounding box
        local clx = math.max(nx, math.min(x, nx+nw))
        local cly = math.max(ny, math.min(y, ny+nh))
        local dx = x - clx
        local dy = y - cly
        if dx*dx + dy*dy > range*range then
            return 
        end
        
        -- pointers in node
        if nptrs then
            for _,ptr in ipairs(nptrs) do
                local vx,vy,v = unpack(ptr)
                local dx = x - vx
                local dy = y - vy
                if dx*dx + dy*dy <= range*range then
                    -- filter function
                    if not filter_fn or filter_fn(_,v) then
                        table.insert(out, v)
                    end
                end
            end
        end

        -- recurse children
        if nch then
            for _,nnch in pairs(nch) do
                self:filter_node(out, nnch, x,y, range)
            end
        end
    end

end

---------------
-- manual override to disable SSO
-- return nil

return SSO

